import request from '@/utils/request'

// Skin86 API 基础配置
const SKIN86_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'http://localhost:8989' 
  : 'http://localhost:8989'

const SKIN86_API_KEY = process.env.NODE_ENV === 'production'
  ? 'sk-skin86-prod-87654321'
  : 'sk-skin86-dev-12345678'

// 创建专用的Skin86请求实例
const skin86Request = (config) => {
  return request({
    ...config,
    baseURL: SKIN86_BASE_URL,
    headers: {
      'X-API-Key': SKIN86_API_KEY,
      'Content-Type': 'application/json',
      ...config.headers
    }
  })
}

// ========== 任务管理接口 ==========

/**
 * 获取任务状态
 */
export function getTaskStatus() {
  return skin86Request({
    url: '/api/v1/tasks/status',
    method: 'get'
  })
}

/**
 * 手动执行商品数据获取
 * @param {Object} params - 请求参数
 * @param {string} params.platform - 平台名称（如：yp）
 * @param {number} params.startPage - 起始页码，默认为1
 * @param {number} params.maxPages - 最大页数，默认为null（获取所有）
 */
export function fetchGoodsData(params = {}) {
  return skin86Request({
    url: '/api/v1/tasks/goods/fetch',
    method: 'post',
    data: {
      platform: 'yp', // 默认使用yp平台
      startPage: 1,
      maxPages: null, // 获取所有页面
      ...params
    }
  })
}

/**
 * 停止当前任务
 */
export function stopCurrentTask() {
  return skin86Request({
    url: '/api/v1/tasks/stop',
    method: 'post'
  })
}

/**
 * 健康检查
 */
export function healthCheck() {
  return skin86Request({
    url: '/api/v1/tasks/health',
    method: 'get'
  })
}

// ========== 断点管理接口 ==========

/**
 * 获取所有断点信息
 */
export function getAllCheckpoints() {
  return skin86Request({
    url: '/api/v1/tasks/checkpoints',
    method: 'get'
  })
}

/**
 * 获取指定任务断点
 * @param {string} taskKey - 任务键名
 */
export function getTaskCheckpoint(taskKey) {
  return skin86Request({
    url: `/api/v1/tasks/checkpoints/${taskKey}`,
    method: 'get'
  })
}

/**
 * 删除指定断点
 * @param {string} taskKey - 任务键名
 */
export function deleteTaskCheckpoint(taskKey) {
  return skin86Request({
    url: `/api/v1/tasks/checkpoints/${taskKey}`,
    method: 'delete'
  })
}

/**
 * 清理过期断点
 */
export function cleanupExpiredCheckpoints() {
  return skin86Request({
    url: '/api/v1/tasks/checkpoints/cleanup',
    method: 'post'
  })
}

// ========== 数据库管理接口 ==========

/**
 * 获取数据库统计信息
 */
export function getDatabaseStats() {
  return skin86Request({
    url: '/api/v1/tasks/database/stats',
    method: 'get'
  })
}

/**
 * 获取平台商品数量
 * @param {string} platform - 平台名称
 */
export function getPlatformGoodsCount(platform = 'yp') {
  return skin86Request({
    url: `/api/v1/tasks/database/${platform}/count`,
    method: 'get'
  })
}

/**
 * 清理平台数据（谨慎使用）
 * @param {string} platform - 平台名称
 */
export function cleanPlatformData(platform) {
  return skin86Request({
    url: `/api/v1/tasks/database/${platform}`,
    method: 'delete'
  })
}

// ========== 工具函数 ==========

/**
 * 检查Skin86服务是否可用
 */
export async function checkSkin86Service() {
  try {
    const response = await healthCheck()
    return response && response.data && response.data.status === 'UP'
  } catch (error) {
    console.error('Skin86服务检查失败：', error)
    return false
  }
}

/**
 * 轮询任务状态直到完成
 * @param {Function} onProgress - 进度回调函数
 * @param {number} interval - 轮询间隔（毫秒）
 * @param {number} timeout - 超时时间（毫秒）
 */
export function pollTaskStatus(onProgress, interval = 5000, timeout = 300000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    
    const poll = async () => {
      try {
        if (Date.now() - startTime > timeout) {
          reject(new Error('任务执行超时'))
          return
        }
        
        const response = await getTaskStatus()
        if (response && response.data) {
          const taskData = response.data
          
          // 调用进度回调
          if (onProgress) {
            onProgress(taskData)
          }
          
          // 检查任务是否完成
          if (!taskData.isRunning) {
            resolve(taskData)
          } else {
            // 继续轮询
            setTimeout(poll, interval)
          }
        } else {
          reject(new Error('获取任务状态失败'))
        }
      } catch (error) {
        reject(error)
      }
    }
    
    poll()
  })
}

/**
 * 格式化错误信息
 * @param {Error} error - 错误对象
 */
export function formatSkin86Error(error) {
  if (error.response) {
    const { status, data } = error.response
    
    switch (status) {
      case 401:
        return 'API认证失败，请检查API Key配置'
      case 403:
        return 'IP地址不在白名单中，请联系管理员'
      case 429:
        return '请求频率超出限制，请稍后再试'
      case 500:
        return 'Skin86服务器内部错误'
      default:
        return data?.message || `请求失败 (${status})`
    }
  } else if (error.code === 'ECONNREFUSED') {
    return 'Skin86服务不可用，请检查服务状态'
  } else {
    return error.message || '未知错误'
  }
}
