import request from '@/utils/request'

// ========== 成本价异常商品管理 ==========

// 查询成本价异常商品列表
export function getCostPriceAnomalyList(query) {
  return request({
    url: '/priceAlert/anomaly/list',
    method: 'get',
    params: query
  })
}

// 统计成本价异常商品数量
export function getCostPriceAnomalyCount() {
  return request({
    url: '/priceAlert/anomaly/count',
    method: 'get'
  })
}

// 获取成本价异常商品统计信息
export function getCostPriceAnomalyStatistics() {
  return request({
    url: '/priceAlert/anomaly/statistics',
    method: 'get'
  })
}

// 导出成本价异常商品列表
export function exportCostPriceAnomalyList(query) {
  return request({
    url: '/priceAlert/anomaly/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  })
}

// ========== 数据同步功能 ==========

// 获取数据同步状态
export function getDataSyncStatus() {
  return request({
    url: '/priceAlert/anomaly/sync/status',
    method: 'get'
  })
}

// 手动触发数据同步（调用VIM后端接口）
export function triggerManualSync() {
  return request({
    url: '/priceAlert/anomaly/sync/trigger',
    method: 'post'
  })
}

// 手动触发外部数据获取（调用Skin86 API）
export function triggerExternalDataFetch(params) {
  return request({
    url: '/priceAlert/anomaly/sync/external-fetch',
    method: 'post',
    data: params
  })
}

// 检查数据更新状态
export function checkDataFreshness() {
  return request({
    url: '/priceAlert/anomaly/sync/check',
    method: 'get'
  })
}

// ========== 通知功能 ==========

// 获取通知列表
export function getNotifications(query) {
  return request({
    url: '/priceAlert/anomaly/notifications',
    method: 'get',
    params: query
  })
}

// 标记通知为已读
export function markNotificationAsRead(id) {
  return request({
    url: `/priceAlert/anomaly/notifications/${id}/read`,
    method: 'post'
  })
}

// 测试从库连接
export function testSlaveConnection() {
  return request({
    url: '/priceAlert/anomaly/test/slave-connection',
    method: 'get'
  })
}

// 测试外部API连接
export function testExternalApiConnection() {
  return request({
    url: '/priceAlert/anomaly/test/external-api',
    method: 'get'
  })
}

// 获取外部数据统计
export function getExternalDataStatistics() {
  return request({
    url: '/priceAlert/anomaly/external/statistics',
    method: 'get'
  })
}

// 批量标记通知为已读
export function batchMarkNotificationsAsRead(ids) {
  return request({
    url: '/priceAlert/anomaly/notifications/batch-read',
    method: 'post',
    data: ids
  })
}

// 获取未读通知数量
export function getUnreadNotificationCount() {
  return request({
    url: '/priceAlert/anomaly/notifications/unread-count',
    method: 'get'
  })
}
