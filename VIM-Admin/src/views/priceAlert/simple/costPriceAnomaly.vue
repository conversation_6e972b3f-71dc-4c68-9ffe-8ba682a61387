<template>
  <div class="app-container">
    <!-- 数据同步状态栏 -->
    <el-alert
        v-if="syncStatus.hasUpdate"
        :title="syncStatus.message"
        :type="syncStatus.type"
        :closable="false"
        show-icon
        class="mb-4"
    >
      <template #default>
        <div class="sync-status-content">
          <span>{{ syncStatus.message }}</span>
          <el-button
              type="primary"
              size="small"
              @click="handleRefreshData"
              :loading="syncLoading"
          >
            刷新数据
          </el-button>
          <el-button
              type="warning"
              size="small"
              @click="handleManualSync"
              :loading="syncLoading"
          >
            手动同步
          </el-button>
        </div>
      </template>
    </el-alert>

    <!-- 页面标题和统计信息 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span class="card-title">成本价异常商品管理</span>
            <el-button
                style="float: right; padding: 3px 0"
                type="text"
                @click="loadStatistics"
                :loading="statisticsLoading"
            >
              刷新统计
            </el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">{{ statistics.totalAnomalyItems || 0 }}</div>
                <div class="statistic-label">异常商品总数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">¥{{ statistics.avgPriceDifference || '0.00' }}</div>
                <div class="statistic-label">平均价格差异</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">¥{{ statistics.maxPriceDifference || '0.00' }}</div>
                <div class="statistic-label">最大价格差异</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">{{ Object.keys(statistics.tagStatistics || {}).length }}</div>
                <div class="statistic-label">涉及商品类型</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card class="box-card mb-4">
      <el-form :model="queryParams" ref="queryForm" size="small" v-show="showSearch" label-width="80px">
        <!-- 基础筛选 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="商品名称" prop="itemName">
              <el-input
                  v-model="queryParams.itemName"
                  placeholder="请输入商品名称"
                  clearable
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="盲盒名称" prop="boxName">
              <el-input
                  v-model="queryParams.boxName"
                  placeholder="请输入盲盒名称"
                  clearable
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品标签" prop="itemTag">
              <el-input
                  v-model="queryParams.itemTag"
                  placeholder="请输入商品标签"
                  clearable
                  @keyup.enter.native="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品状态" prop="saleStatus">
              <el-select v-model="queryParams.saleStatus" placeholder="请选择状态" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="上架" value="1"></el-option>
                <el-option label="下架" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 高级筛选 -->
        <el-collapse v-model="activeCollapse" class="mt-3">
          <el-collapse-item title="高级筛选" name="advanced">
            <el-row :gutter="20">
              <!-- 价格范围筛选 -->
              <el-col :span="12">
                <el-form-item label="成本价范围">
                  <el-row :gutter="10">
                    <el-col :span="11">
                      <el-input-number
                          v-model="queryParams.minCost"
                          placeholder="最小值"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                          style="width: 100%"
                      />
                    </el-col>
                    <el-col :span="2" class="text-center">
                      <span style="line-height: 32px;">-</span>
                    </el-col>
                    <el-col :span="11">
                      <el-input-number
                          v-model="queryParams.maxCost"
                          placeholder="最大值"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                          style="width: 100%"
                      />
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="回收价范围">
                  <el-row :gutter="10">
                    <el-col :span="11">
                      <el-input-number
                          v-model="queryParams.minRecycle"
                          placeholder="最小值"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                          style="width: 100%"
                      />
                    </el-col>
                    <el-col :span="2" class="text-center">
                      <span style="line-height: 32px;">-</span>
                    </el-col>
                    <el-col :span="11">
                      <el-input-number
                          v-model="queryParams.maxRecycle"
                          placeholder="最大值"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                          style="width: 100%"
                      />
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="价格差异范围">
                  <el-row :gutter="10">
                    <el-col :span="11">
                      <el-input-number
                          v-model="queryParams.minDifference"
                          placeholder="最小值"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                          style="width: 100%"
                      />
                    </el-col>
                    <el-col :span="2" class="text-center">
                      <span style="line-height: 32px;">-</span>
                    </el-col>
                    <el-col :span="11">
                      <el-input-number
                          v-model="queryParams.maxDifference"
                          placeholder="最大值"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                          style="width: 100%"
                      />
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="差异百分比范围">
                  <el-row :gutter="10">
                    <el-col :span="11">
                      <el-input-number
                          v-model="queryParams.minPercentage"
                          placeholder="最小值(%)"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                          style="width: 100%"
                      />
                    </el-col>
                    <el-col :span="2" class="text-center">
                      <span style="line-height: 32px;">-</span>
                    </el-col>
                    <el-col :span="11">
                      <el-input-number
                          v-model="queryParams.maxPercentage"
                          placeholder="最大值(%)"
                          :min="0"
                          :precision="2"
                          controls-position="right"
                          style="width: 100%"
                      />
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="库存状态" prop="stockStatus">
                  <el-select v-model="queryParams.stockStatus" placeholder="请选择库存状态" clearable>
                    <el-option label="全部" value=""></el-option>
                    <el-option label="有库存" value="1"></el-option>
                    <el-option label="无库存" value="0"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="异常程度" prop="severityLevel">
                  <el-select v-model="queryParams.severityLevel" placeholder="请选择异常程度" clearable>
                    <el-option label="全部" value=""></el-option>
                    <el-option label="轻微(0-10%)" value="1"></el-option>
                    <el-option label="中等(10-50%)" value="2"></el-option>
                    <el-option label="严重(>50%)" value="3"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="排序方式" prop="sortField">
                  <el-select v-model="queryParams.sortField" placeholder="请选择排序方式" clearable>
                    <el-option label="价格差异(降序)" value="difference_desc"></el-option>
                    <el-option label="价格差异(升序)" value="difference_asc"></el-option>
                    <el-option label="差异百分比(降序)" value="percentage_desc"></el-option>
                    <el-option label="差异百分比(升序)" value="percentage_asc"></el-option>
                    <el-option label="成本价(降序)" value="cost_desc"></el-option>
                    <el-option label="成本价(升序)" value="cost_asc"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>

        <!-- 操作按钮 -->
        <el-row class="mt-3">
          <el-col :span="24" class="text-center">
            <el-button type="primary" icon="el-icon-search" size="default" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="default" @click="resetQuery">重置</el-button>
            <el-button type="info" icon="el-icon-download" size="default" @click="saveFilterConditions">保存筛选条件
            </el-button>
            <el-button type="warning" icon="el-icon-upload2" size="default" @click="loadFilterConditions">加载筛选条件
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb-8">
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['priceAlert:anomaly:export']"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="el-icon-refresh"
            size="mini"
            @click="handleManualSync"
            :loading="syncLoading"
            v-hasPermi="['priceAlert:anomaly:sync']"
        >手动同步
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="info"
            plain
            icon="el-icon-bell"
            size="mini"
            @click="handleViewNotifications"
            v-hasPermi="['priceAlert:anomaly:notification']"
        >通知管理
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="el-icon-view"
            size="mini"
            @click="handleViewSyncStatus"
            v-hasPermi="['priceAlert:anomaly:query']"
        >同步状态
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="el-icon-connection"
            size="mini"
            @click="handleTestSlaveConnection"
            v-hasPermi="['priceAlert:anomaly:query']"
        >测试从库
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table
        v-loading="loading"
        :data="anomalyList"
        @selection-change="handleSelectionChange"
        :default-sort="{prop: 'priceDifference', order: 'descending'}"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="商品信息" align="center" min-width="200">
        <template #default="scope">
          <div v-if="scope.row" class="item-info">
            <el-image
                :src="scope.row.itemImage"
                :preview-src-list="[scope.row.itemImage]"
                fit="cover"
                style="width: 40px; height: 40px; border-radius: 4px; margin-right: 10px;"
                :preview-teleported="true"
            >
              <template #error>
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
            <div class="item-details">
              <div class="item-name">{{ scope.row.itemName || '未知商品' }}</div>
              <div class="item-tag">{{ scope.row.itemTag || '未分类' }}</div>
            </div>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="价格信息" align="center" min-width="180">
        <template #default="scope">
          <div v-if="scope.row" class="price-info">
            <div class="price-row">
              <span class="price-label">成本价:</span>
              <span class="price-value cost-price">¥{{ scope.row.priceCost || '0.00' }}</span>
            </div>
            <div class="price-row">
              <span class="price-label">回收价:</span>
              <span class="price-value recycle-price">¥{{ scope.row.priceRecycle || '0.00' }}</span>
            </div>
            <div class="price-row">
              <span class="price-label">展示价:</span>
              <span class="price-value">¥{{ scope.row.priceShow || '0.00' }}</span>
            </div>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="价格差异" align="center" prop="priceDifference" sortable min-width="120">
        <template #default="scope">
          <div v-if="scope.row" class="difference-info">
            <div class="difference-amount">
              <el-tag type="danger" size="small">
                ¥{{ scope.row.priceDifference || '0.00' }}
              </el-tag>
            </div>
            <div class="difference-percentage">
              {{ scope.row.differencePercentage || '0.00' }}%
            </div>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="所属盲盒" align="center" min-width="200">
        <template #default="scope">
          <div v-if="scope.row">
            <div v-if="scope.row.boxNames" class="box-info">
              <!-- 盲盒数量统计 -->
              <div class="box-count">
                <el-tag type="info" size="small">
                  共{{ scope.row.boxCount || 0 }}个盲盒
                </el-tag>
              </div>
              <!-- 盲盒名称列表（已包含类型信息：盲盒名称（类型名称）） -->
              <div class="box-names">
                <el-tooltip
                    :content="scope.row.boxNames"
                    placement="top"
                    :disabled="scope.row.boxNames.length <= 50"
                >
                  <span class="box-names-text">
                    {{
                      scope.row.boxNames.length > 50 ? scope.row.boxNames.substring(0, 50) + '...' : scope.row.boxNames
                    }}
                  </span>
                </el-tooltip>
              </div>
              <!-- 盲盒价格范围 -->
              <div v-if="scope.row.boxPrices" class="box-prices">
                <span class="box-prices-label">价格：</span>
                <span class="box-prices-text">{{ scope.row.boxPrices }}</span>
              </div>
            </div>
            <span v-else class="text-muted">未关联已上架盲盒</span>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="商品状态" align="center" width="100">
        <template #default="scope">
          <div v-if="scope.row">
            <el-tag :type="scope.row.itemSale === 1 ? 'success' : 'danger'" size="small">
              {{ scope.row.itemSale === 1 ? '上架' : '下架' }}
            </el-tag>
          </div>
          <div v-else class="loading-placeholder">-</div>
        </template>
      </el-table-column>
      <el-table-column label="库存" align="center" prop="itemStock" width="80"/>
    </el-table>

    <!-- 分页 -->
    <pagination
        v-show="total > 0"
        :total="total"
        :page="queryParams.pageNum"
        :limit="queryParams.pageSize"
        @update:page="handlePageChange"
        @update:limit="handleSizeChange"
        @pagination="handlePagination"
    />

    <!-- 同步进度对话框 -->
    <el-dialog
        :title="syncProgressDialog.title"
        :visible.sync="syncProgressDialog.visible"
        width="600px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="syncProgressDialog.isCompleted"
    >
      <div class="sync-progress-content">
        <!-- 步骤指示器 -->
        <el-steps
            :active="syncProgressDialog.currentStep - 1"
            finish-status="success"
            class="mb-4"
        >
          <el-step
              v-for="(stepText, index) in syncProgressDialog.stepTexts"
              :key="index"
              :title="stepText"
          ></el-step>
        </el-steps>

        <!-- 进度条 -->
        <div class="progress-section mb-4">
          <el-progress
              :percentage="syncProgressDialog.progress"
              :status="syncProgressDialog.isCompleted ? 'success' : 'active'"
              :stroke-width="8"
          ></el-progress>
        </div>

        <!-- 当前状态消息 -->
        <div class="status-message">
          <el-alert
              :title="syncProgressDialog.currentMessage"
              type="info"
              :closable="false"
              show-icon
          ></el-alert>
        </div>
      </div>

      <div slot="footer" class="dialog-footer" v-if="syncProgressDialog.isCompleted">
        <el-button type="primary" @click="syncProgressDialog.visible = false">
          完成
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCostPriceAnomalyList,
  getCostPriceAnomalyStatistics,
  getDataSyncStatus,
  triggerManualSync,
  triggerExternalDataFetch,
  getNotifications,
  markNotificationAsRead,
  testSlaveConnection
} from '@/api/priceAlert/costPriceAnomaly'
import {
  getTaskStatus,
  fetchGoodsData,
  pollTaskStatus,
  formatSkin86Error,
  checkSkin86Service
} from '@/api/external/skin86'

export default {
  name: 'CostPriceAnomaly',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 统计信息加载状态
      statisticsLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 异常商品表格数据
      anomalyList: [],
      // 统计信息
      statistics: {},
      // 高级筛选折叠面板状态
      activeCollapse: [],
      // 数据同步状态
      syncStatus: {
        hasUpdate: false,
        message: '',
        type: 'info'
      },
      // 同步加载状态
      syncLoading: false,
      // 通知列表
      notifications: [],
      // 同步进度对话框
      syncProgressDialog: {
        visible: false,
        title: '数据同步进度',
        currentStep: 1,
        totalSteps: 4,
        stepTexts: [
          '检查外部数据服务状态',
          '启动外部数据获取任务',
          '监控任务进度',
          '同步到系统数据库'
        ],
        progress: 0,
        currentMessage: '',
        isCompleted: false
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // 基础筛选
        itemName: null,
        boxName: null,
        itemTag: null,
        saleStatus: null,
        // 价格范围筛选
        minCost: null,
        maxCost: null,
        minRecycle: null,
        maxRecycle: null,
        minDifference: null,
        maxDifference: null,
        minPercentage: null,
        maxPercentage: null,
        // 状态筛选
        stockStatus: null,
        severityLevel: null,
        // 排序
        sortField: null
      }
    }
  },
  created() {
    console.log('组件创建，初始查询参数：', this.queryParams)
    this.getList()
    this.loadStatistics()
    this.checkDataSyncStatus()
  },
  mounted() {
    console.log('组件挂载完成，当前查询参数：', this.queryParams)
    console.log('分页组件测试 - 可在控制台调用 window.testCostPriceAnomalyPagination() 进行测试')
  },
  watch: {
    // 监听分页参数变化
    'queryParams.pageNum': function (newVal, oldVal) {
      console.log('页码变化：', oldVal, '->', newVal)
    },
    'queryParams.pageSize': function (newVal, oldVal) {
      console.log('每页大小变化：', oldVal, '->', newVal)
    }
  },
  methods: {
    /** 查询异常商品列表 */
    getList() {
      this.loading = true
      console.log('🔍 开始获取成本价异常商品列表')
      console.log('查询参数：', JSON.parse(JSON.stringify(this.queryParams)))
      console.log('当前时间戳：', new Date().toISOString())

      getCostPriceAnomalyList(this.queryParams).then(response => {
        console.log('✅ API响应成功：', response)

        // 简化数据处理，按照VIM系统标准格式
        this.anomalyList = response.rows || []
        this.total = response.total || 0

        console.log(`📊 数据加载完成：共${this.total}条记录，当前页${this.anomalyList.length}条`)
        console.log('当前页码：', this.queryParams.pageNum)
        console.log('每页大小：', this.queryParams.pageSize)

        this.loading = false
      }).catch(error => {
        console.error('❌ 获取数据失败：', error)
        this.$modal.msgError('获取数据失败：' + (error.message || '未知错误'))
        this.anomalyList = []
        this.total = 0
        this.loading = false
      })
    },
    /** 加载统计信息 */
    loadStatistics() {
      this.statisticsLoading = true
      console.log('开始加载统计信息')

      getCostPriceAnomalyStatistics().then(response => {
        console.log('统计信息API响应：', response)

        if (response && typeof response === 'object') {
          this.statistics = response.data || response || {}
        } else {
          console.warn('统计信息响应格式异常：', response)
          this.statistics = {}
        }

        console.log('处理后的统计信息：', this.statistics)
        this.statisticsLoading = false
      }).catch(error => {
        console.error('加载统计信息失败：', error)
        this.$modal.msgError('加载统计信息失败：' + (error.message || '未知错误'))
        this.statistics = {}
        this.statisticsLoading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      console.log('执行搜索，重置页码为1')
      this.queryParams.pageNum = 1
      console.log('搜索参数：', this.queryParams)
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      console.log('重置搜索条件')
      this.resetForm('queryForm')
      // 重置所有查询参数
      this.queryParams = {
        pageNum: 1,
        pageSize: this.queryParams.pageSize, // 保持当前页面大小
        // 基础筛选
        itemName: null,
        boxName: null,
        itemTag: null,
        saleStatus: null,
        // 价格范围筛选
        minCost: null,
        maxCost: null,
        minRecycle: null,
        maxRecycle: null,
        minDifference: null,
        maxDifference: null,
        minPercentage: null,
        maxPercentage: null,
        // 状态筛选
        stockStatus: null,
        severityLevel: null,
        // 排序
        sortField: null
      }
      // 收起高级筛选面板
      this.activeCollapse = []
      console.log('重置后的参数：', this.queryParams)
      this.getList()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.itemId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 页码变化处理 */
    handlePageChange(page) {
      console.log('🔄 页码变化：', this.queryParams.pageNum, '->', page)
      this.queryParams.pageNum = Number(page)
      this.getList()
    },
    /** 每页大小变化处理 */
    handleSizeChange(size) {
      console.log('🔄 每页大小变化：', this.queryParams.pageSize, '->', size)
      this.queryParams.pageNum = 1 // 重置到第一页
      this.queryParams.pageSize = Number(size)
      this.getList()
    },
    /** 分页事件处理（备用） */
    handlePagination({page, limit}) {
      console.group('🔄 分页事件处理（备用）')
      console.log('分页事件参数：', {page, limit})
      console.log('当前查询参数（更新前）：', JSON.parse(JSON.stringify(this.queryParams)))

      // 确保参数更新
      this.queryParams.pageNum = Number(page)
      this.queryParams.pageSize = Number(limit)

      console.log('更新后的查询参数：', JSON.parse(JSON.stringify(this.queryParams)))
      this.getList()

      console.groupEnd()
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('priceAlert/simple/cost-price-anomaly/export', {
        ...this.queryParams
      }, `成本价异常商品_${new Date().getTime()}.xlsx`)
    },
    /** 保存筛选条件 */
    saveFilterConditions() {
      try {
        const filterConditions = {
          ...this.queryParams,
          activeCollapse: this.activeCollapse,
          saveTime: new Date().toISOString()
        }
        localStorage.setItem('costPriceAnomalyFilter', JSON.stringify(filterConditions))
        this.$modal.msgSuccess('筛选条件已保存')
        console.log('保存的筛选条件：', filterConditions)
      } catch (error) {
        console.error('保存筛选条件失败：', error)
        this.$modal.msgError('保存筛选条件失败')
      }
    },
    /** 加载筛选条件 */
    loadFilterConditions() {
      try {
        const savedFilter = localStorage.getItem('costPriceAnomalyFilter')
        if (savedFilter) {
          const filterConditions = JSON.parse(savedFilter)
          // 恢复查询参数（排除分页参数）
          const {pageNum, pageSize, saveTime, activeCollapse, ...queryConditions} = filterConditions
          this.queryParams = {
            ...this.queryParams,
            ...queryConditions,
            pageNum: 1 // 重置到第一页
          }
          // 恢复折叠面板状态
          if (activeCollapse) {
            this.activeCollapse = activeCollapse
          }
          this.$modal.msgSuccess(`已加载筛选条件（保存时间：${new Date(saveTime).toLocaleString()}）`)
          console.log('加载的筛选条件：', this.queryParams)
          // 自动执行搜索
          this.getList()
        } else {
          this.$modal.msgWarning('未找到保存的筛选条件')
        }
      } catch (error) {
        console.error('加载筛选条件失败：', error)
        this.$modal.msgError('加载筛选条件失败')
      }
    },
    /** 测试分页功能 */
    testPagination() {
      if (process.env.NODE_ENV === 'development') {
        console.group('🧪 分页功能测试')
        console.log('当前页码：', this.queryParams.pageNum)
        console.log('每页大小：', this.queryParams.pageSize)
        console.log('总记录数：', this.total)
        console.log('当前数据条数：', this.anomalyList.length)
        console.log('分页组件是否显示：', this.total > 0)

        // 模拟分页切换
        const testPage = this.queryParams.pageNum === 1 ? 2 : 1
        console.log(`模拟切换到第${testPage}页`)

        // 使用分页事件处理方法进行测试
        this.handlePagination({
          page: testPage,
          limit: this.queryParams.pageSize
        })

        console.groupEnd()
      }
    },
    /** 手动测试分页参数更新 */
    testPageParamUpdate() {
      if (process.env.NODE_ENV === 'development') {
        console.group('🔧 手动测试分页参数更新')

        const originalPage = this.queryParams.pageNum
        const testPage = originalPage === 1 ? 2 : 1

        console.log('原始页码：', originalPage)
        console.log('测试页码：', testPage)

        // 直接修改参数
        this.queryParams.pageNum = testPage
        console.log('修改后的查询参数：', this.queryParams)

        // 延迟调用API，观察参数是否正确传递
        setTimeout(() => {
          console.log('延迟后的查询参数：', this.queryParams)
          this.getList()
        }, 100)

        console.groupEnd()
      }
    },
    /** 检查分页组件状态 */
    checkPaginationStatus() {
      if (process.env.NODE_ENV === 'development') {
        console.group('📊 分页组件状态检查')
        console.log('Vue组件实例：', this)
        console.log('queryParams对象：', this.queryParams)
        console.log('queryParams是否为响应式：', this.$data.queryParams === this.queryParams)
        console.log('total值：', this.total)
        console.log('anomalyList长度：', this.anomalyList.length)
        console.log('loading状态：', this.loading)

        // 检查分页组件是否正确渲染
        const paginationEl = this.$el.querySelector('.pagination-container')
        console.log('分页组件DOM元素：', paginationEl)

        if (paginationEl) {
          const currentPageEl = paginationEl.querySelector('.el-pager .is-active')
          console.log('当前激活页码元素：', currentPageEl)
          console.log('当前激活页码文本：', currentPageEl ? currentPageEl.textContent : '未找到')
        }

        console.groupEnd()
      }
    },
    /** 模拟点击分页按钮 */
    simulatePageClick(targetPage) {
      if (process.env.NODE_ENV === 'development') {
        console.group(`🖱️ 模拟点击第${targetPage}页`)

        // 查找分页按钮
        const paginationEl = this.$el.querySelector('.pagination-container')
        if (paginationEl) {
          const pageButtons = paginationEl.querySelectorAll('.el-pager .number')
          const targetButton = Array.from(pageButtons).find(btn =>
              btn.textContent.trim() === targetPage.toString()
          )

          if (targetButton) {
            console.log('找到目标按钮：', targetButton)
            console.log('点击前的页码：', this.queryParams.pageNum)

            // 模拟点击事件
            targetButton.click()

            // 延迟检查结果
            setTimeout(() => {
              console.log('点击后的页码：', this.queryParams.pageNum)
              console.log('是否成功切换：', this.queryParams.pageNum === targetPage)
            }, 100)
          } else {
            console.warn('未找到目标页码按钮')
          }
        } else {
          console.warn('未找到分页组件')
        }

        console.groupEnd()
      }
    },
    /** 检查数据同步状态 */
    async checkDataSyncStatus() {
      try {
        const response = await getDataSyncStatus()
        if (response && response.data) {
          const statusList = response.data

          // 检查是否有新数据更新
          const priceCheckStatus = statusList.find(s => s.syncType === 'price_check')
          if (priceCheckStatus && priceCheckStatus.hasNewData) {
            this.syncStatus = {
              hasUpdate: true,
              message: '检测到新的价格数据更新，建议刷新数据',
              type: 'warning'
            }
          } else {
            this.syncStatus = {
              hasUpdate: false,
              message: '',
              type: 'info'
            }
          }
        }
      } catch (error) {
        console.error('检查数据同步状态失败：', error)
      }
    },
    /** 手动同步数据 */
    async handleManualSync() {
      this.syncLoading = true

      // 初始化进度对话框
      this.syncProgressDialog = {
        visible: true,
        title: '数据同步进度',
        currentStep: 1,
        totalSteps: 4,
        stepTexts: [
          '检查外部数据服务状态',
          '启动外部数据获取任务',
          '监控任务进度',
          '同步到系统数据库'
        ],
        progress: 0,
        currentMessage: '正在检查外部数据服务状态...',
        isCompleted: false
      }

      try {
        // 第一步：检查Skin86服务状态
        this.updateSyncProgress(1, 10, '正在检查外部数据服务状态...')
        const isServiceAvailable = await checkSkin86Service()

        if (!isServiceAvailable) {
          this.updateSyncProgress(4, 50, '外部数据服务不可用，使用内部数据同步...')
          await this.handleInternalSync()
          return
        }

        // 第二步：启动外部数据获取任务
        this.updateSyncProgress(2, 25, '正在启动外部数据获取任务...')
        const fetchResponse = await fetchGoodsData({
          platform: 'yp',
          startPage: 1,
          maxPages: 50 // 限制页数避免过长时间
        })

        if (fetchResponse && fetchResponse.data) {
          const taskInfo = fetchResponse.data

          // 第三步：监控任务进度
          this.updateSyncProgress(3, 40, `数据获取任务已启动：${taskInfo.taskId}`)

          await pollTaskStatus(
            (taskData) => {
              // 进度回调
              if (taskData.isRunning) {
                const progress = taskData.currentPage && taskData.totalPages
                  ? Math.round((taskData.currentPage / taskData.totalPages) * 100)
                  : 0
                const overallProgress = 40 + Math.round(progress * 0.4) // 40% + 40% * task progress
                this.updateSyncProgress(3, overallProgress,
                  `数据获取进行中... 进度：${progress}% (${taskData.processedCount || 0}个商品)`)
              }
            },
            5000, // 5秒轮询一次
            300000 // 5分钟超时
          )

          // 第四步：外部数据获取完成，触发内部同步
          this.updateSyncProgress(4, 85, '外部数据获取完成，正在同步到系统...')
          await this.handleInternalSync()

        } else {
          throw new Error('启动外部数据获取任务失败')
        }

      } catch (error) {
        console.error('手动同步失败：', error)
        const errorMessage = formatSkin86Error(error)

        // 更新进度对话框显示错误
        this.updateSyncProgress(this.syncProgressDialog.currentStep, this.syncProgressDialog.progress,
          `同步失败：${errorMessage}`)

        // 如果外部同步失败，尝试内部同步
        if (error.message.includes('ECONNREFUSED') || error.response?.status === 401) {
          this.updateSyncProgress(4, 50, '外部数据服务异常，使用内部数据同步...')
          await this.handleInternalSync()
        } else {
          // 标记为完成（失败）
          this.syncProgressDialog.isCompleted = true
          this.$modal.msgError('同步失败：' + errorMessage)
        }
      } finally {
        this.syncLoading = false
      }
    },

    /** 内部数据同步 */
    async handleInternalSync() {
      try {
        const response = await triggerManualSync()
        if (response && response.data) {
          const result = response.data

          // 更新进度
          this.updateSyncProgress(4, 100,
            `内部同步完成！异常商品：${result.anomalyCount || 0}个，总商品：${result.totalItems || 0}个`)

          // 刷新数据
          this.getList()
          this.loadStatistics()

          // 更新同步状态
          this.syncStatus = {
            hasUpdate: false,
            message: '',
            type: 'info'
          }

          // 标记为完成
          this.syncProgressDialog.isCompleted = true

          // 延迟显示成功消息
          setTimeout(() => {
            this.$modal.msgSuccess(`数据同步完成！异常商品：${result.anomalyCount || 0}个，总商品：${result.totalItems || 0}个`)
          }, 500)
        }
      } catch (error) {
        console.error('内部同步失败：', error)
        this.updateSyncProgress(4, this.syncProgressDialog.progress, `内部同步失败：${error.message || '未知错误'}`)
        this.syncProgressDialog.isCompleted = true
        this.$modal.msgError('内部同步失败：' + (error.message || '未知错误'))
      }
    },

    /** 更新同步进度 */
    updateSyncProgress(step, progress, message) {
      this.syncProgressDialog.currentStep = step
      this.syncProgressDialog.progress = progress
      this.syncProgressDialog.currentMessage = message
    },
    /** 测试从库连接 */
    async handleTestSlaveConnection() {
      try {
        this.$message.info('正在测试从库连接...')
        const response = await testSlaveConnection()

        if (response && response.data) {
          const testResults = response.data

          // 格式化测试结果
          const resultHtml = testResults.map(test =>
            `<div style="margin-bottom: 15px; padding: 12px; border: 1px solid #eee; border-radius: 6px;">
              <div style="font-weight: bold; color: #409EFF; margin-bottom: 8px;">
                ${test.testName}
              </div>
              <div style="margin-bottom: 5px;">
                <span style="color: #666;">状态：</span>
                <span style="color: ${test.status === 'SUCCESS' ? '#67C23A' : test.status === 'FAILED' ? '#F56C6C' : '#409EFF'};">
                  ${test.status}
                </span>
              </div>
              ${test.result ? `<div style="margin-bottom: 5px;">
                <span style="color: #666;">结果：</span>
                <span>${test.result}</span>
              </div>` : ''}
              ${test.error ? `<div style="margin-bottom: 5px;">
                <span style="color: #666;">错误：</span>
                <span style="color: #F56C6C;">${test.error}</span>
              </div>` : ''}
              <div>
                <span style="color: #666;">说明：</span>
                <span>${test.message}</span>
              </div>
              ${test.data ? `<div style="margin-top: 8px;">
                <details>
                  <summary style="color: #409EFF; cursor: pointer;">查看详细数据</summary>
                  <pre style="background: #f5f5f5; padding: 8px; margin-top: 5px; border-radius: 4px; font-size: 12px;">${JSON.stringify(test.data, null, 2)}</pre>
                </details>
              </div>` : ''}
            </div>`
          ).join('')

          this.$alert(resultHtml, '从库连接测试结果', {
            dangerouslyUseHTMLString: true,
            customClass: 'slave-connection-test-dialog'
          })

          // 检查是否有失败的测试
          const hasFailures = testResults.some(test => test.status === 'FAILED')
          if (hasFailures) {
            this.$modal.msgWarning('从库连接测试发现问题，请检查测试结果')
          } else {
            this.$modal.msgSuccess('从库连接测试全部通过')
          }
        }
      } catch (error) {
        console.error('从库连接测试失败：', error)
        this.$modal.msgError('从库连接测试失败：' + (error.message || '未知错误'))
      }
    },
    /** 刷新数据 */
    handleRefreshData() {
      this.getList()
      this.loadStatistics()
      this.checkDataSyncStatus()
    },
    /** 查看通知管理 */
    async handleViewNotifications() {
      try {
        const response = await getNotifications()
        if (response && response.data) {
          const notifications = response.data.rows || response.data

          // 使用MessageBox显示通知列表
          const notificationHtml = notifications.map(n =>
            `<div style="margin-bottom: 10px; padding: 10px; border: 1px solid #eee; border-radius: 4px;">
              <div style="font-weight: bold; color: #409EFF;">${n.title}</div>
              <div style="color: #666; font-size: 12px; margin: 5px 0;">${n.content}</div>
              <div style="color: #999; font-size: 11px;">
                ${new Date(n.createTimeStamp * 1000).toLocaleString()}
                ${n.readStatus === 0 ? '<span style="color: #F56C6C;">[未读]</span>' : '<span style="color: #67C23A;">[已读]</span>'}
              </div>
            </div>`
          ).join('')

          this.$alert(notificationHtml, '通知管理', {
            dangerouslyUseHTMLString: true,
            customClass: 'notification-dialog'
          })
        }
      } catch (error) {
        console.error('获取通知失败：', error)
        this.$modal.msgError('获取通知失败：' + (error.message || '未知错误'))
      }
    },
    /** 查看同步状态 */
    async handleViewSyncStatus() {
      try {
        const response = await getDataSyncStatus()
        if (response && response.data) {
          const statusList = response.data

          // 格式化同步状态信息
          const statusHtml = statusList.map(status =>
            `<div style="margin-bottom: 15px; padding: 12px; border: 1px solid #eee; border-radius: 6px;">
              <div style="font-weight: bold; color: #409EFF; margin-bottom: 8px;">
                ${status.syncType === 'price_check' ? '价格检查' : '异常扫描'}
              </div>
              <div style="margin-bottom: 5px;">
                <span style="color: #666;">状态：</span>
                <span style="color: ${status.syncStatus === 1 ? '#67C23A' : '#F56C6C'};">
                  ${status.syncStatus === 1 ? '正常' : '异常'}
                </span>
              </div>
              <div style="margin-bottom: 5px;">
                <span style="color: #666;">最后同步：</span>
                <span>${status.lastSyncTime ? new Date(status.lastSyncTime * 1000).toLocaleString() : '未知'}</span>
              </div>
              <div style="margin-bottom: 5px;">
                <span style="color: #666;">异常商品：</span>
                <span style="color: #E6A23C;">${status.anomalyCount || 0}个</span>
              </div>
              <div>
                <span style="color: #666;">总商品：</span>
                <span>${status.totalItems || 0}个</span>
              </div>
            </div>`
          ).join('')

          this.$alert(statusHtml, '数据同步状态', {
            dangerouslyUseHTMLString: true,
            customClass: 'sync-status-dialog'
          })
        }
      } catch (error) {
        console.error('获取同步状态失败：', error)
        this.$modal.msgError('获取同步状态失败：' + (error.message || '未知错误'))
      }
    }
  },
  // 添加开发环境下的全局方法
  beforeMount() {
    if (process.env.NODE_ENV === 'development') {
      // 将测试方法挂载到window对象，方便在控制台调用
      window.testCostPriceAnomalyPagination = this.testPagination
      window.testCostPriceAnomalyPageUpdate = this.testPageParamUpdate
      window.checkCostPriceAnomalyPaginationStatus = this.checkPaginationStatus
      window.simulateCostPriceAnomalyPageClick = this.simulatePageClick

      console.log('🛠️ 开发环境测试方法已注册：')
      console.log('- window.testCostPriceAnomalyPagination() - 测试分页切换')
      console.log('- window.testCostPriceAnomalyPageUpdate() - 测试参数更新')
      console.log('- window.checkCostPriceAnomalyPaginationStatus() - 检查分页状态')
      console.log('- window.simulateCostPriceAnomalyPageClick(页码) - 模拟点击分页按钮')
    }
  }
}
</script>

<style scoped>
.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.statistic-item {
  text-align: center;
  padding: 10px;
}

.statistic-value {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 5px;
}

.statistic-label {
  font-size: 14px;
  color: #909399;
}

.item-info {
  display: flex;
  align-items: center;
}

.item-details {
  flex: 1;
  text-align: left;
}

.item-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.item-tag {
  font-size: 12px;
  color: #909399;
}

.price-info {
  text-align: left;
}

.price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.price-label {
  font-size: 12px;
  color: #909399;
}

.price-value {
  font-weight: 500;
}

.cost-price {
  color: #F56C6C;
}

.recycle-price {
  color: #67C23A;
}

.difference-info {
  text-align: center;
}

.difference-amount {
  margin-bottom: 4px;
}

.difference-percentage {
  font-size: 12px;
  color: #909399;
}

.box-info {
  text-align: left;
  padding: 4px 0;
}

.box-count {
  margin-bottom: 6px;
}

.box-names {
  margin-bottom: 4px;
}

.box-names-text {
  font-weight: 500;
  color: #303133;
  font-size: 13px;
  line-height: 1.4;
  display: block;
  word-break: break-all;
}

.box-types {
  margin-bottom: 4px;
  font-size: 12px;
}

.box-types-label {
  color: #909399;
  font-weight: normal;
}

.box-types-text {
  color: #606266;
}

.box-prices {
  font-size: 12px;
}

.box-prices-label {
  color: #909399;
  font-weight: normal;
}

.box-prices-text {
  color: #E6A23C;
  font-weight: 500;
}

.text-muted {
  color: #C0C4CC;
  font-style: italic;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

.loading-placeholder {
  color: #C0C4CC;
  font-style: italic;
  font-size: 12px;
  text-align: center;
}

/* 新增筛选功能样式 */
.mt-3 {
  margin-top: 12px;
}

.text-center {
  text-align: center;
}

/* 高级筛选面板样式 */
.el-collapse {
  border: none;
}

.el-collapse-item__header {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0 15px;
  font-weight: 500;
  color: #495057;
}

.el-collapse-item__content {
  padding: 15px;
  background-color: #ffffff;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 4px 4px;
}

/* 输入框组样式 */
.el-input-number {
  width: 100%;
}

/* 按钮组样式 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 表单项间距 */
.el-form-item {
  margin-bottom: 15px;
}

/* 同步状态栏样式 */
.sync-status-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sync-status-content span {
  flex: 1;
  margin-right: 15px;
}

.sync-status-content .el-button {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-col {
    margin-bottom: 10px;
  }

  .statistic-item {
    padding: 15px 10px;
  }

  .statistic-value {
    font-size: 20px;
  }

  .sync-status-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .sync-status-content span {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .sync-status-content .el-button {
    margin-left: 0;
    margin-right: 8px;
  }
}

/* 通知对话框样式 */
.notification-dialog {
  max-height: 500px;
  overflow-y: auto;
}

.notification-dialog .el-message-box__content {
  max-height: 400px;
  overflow-y: auto;
}

/* 同步状态对话框样式 */
.sync-status-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.sync-status-dialog .el-message-box__content {
  max-height: 500px;
  overflow-y: auto;
}

/* 同步进度对话框样式 */
.sync-progress-content {
  padding: 20px 0;
}

.sync-progress-content .el-steps {
  margin-bottom: 30px;
}

.progress-section {
  margin: 20px 0;
}

.status-message {
  margin-top: 20px;
}

.status-message .el-alert {
  border-radius: 6px;
}

.dialog-footer {
  text-align: center;
  padding-top: 20px;
}

/* 从库连接测试对话框样式 */
.slave-connection-test-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.slave-connection-test-dialog .el-message-box__content {
  max-height: 500px;
  overflow-y: auto;
}

.slave-connection-test-dialog details {
  margin-top: 8px;
}

.slave-connection-test-dialog summary {
  outline: none;
  user-select: none;
}

.slave-connection-test-dialog pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
}
</style>
