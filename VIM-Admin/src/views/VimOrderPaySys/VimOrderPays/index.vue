<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入订单号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="uid">
        <el-input
          v-model="queryParams.uid"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="消耗电能" prop="amount">
        <el-input
          v-model="queryParams.amount"
          placeholder="请输入消耗电能"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="消费说明" prop="info">
        <el-input
          v-model="queryParams.info"
          placeholder="请输入消费说明"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['VimOrderPaySys:VimOrderPays:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['VimOrderPaySys:VimOrderPays:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['VimOrderPaySys:VimOrderPays:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['VimOrderPaySys:VimOrderPays:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="VimOrderPaysList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" align="center" prop="id" width="180"/>
      <el-table-column label="用户ID" align="center" prop="uid" width="80"/>
      <el-table-column label="用户信息" align="center" width="150">
        <template #default="scope">
          <div>{{ scope.row.userDisplayName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="消耗电能" align="center" prop="amount" width="100">
        <template #default="scope">
          <span style="color: #f56c6c;">{{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="电能余量" align="center" prop="balance" width="100">
        <template #default="scope">
          <span style="color: #67c23a;">{{ scope.row.balance }}</span>
        </template>
      </el-table-column>
      <el-table-column label="消耗时间" align="center" prop="time" width="160">
        <template #default="scope">
          {{ formatTimestamp(scope.row.time) }}
        </template>
      </el-table-column>
      <el-table-column label="电能消耗说明" align="center" prop="info" :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['VimOrderPaySys:VimOrderPays:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['VimOrderPaySys:VimOrderPays:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户电能消费对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="vimOrderPayRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="订单号" prop="id">
          <el-input v-model="form.id" placeholder="请输入订单号" :disabled="form.id != null" />
        </el-form-item>
        <el-form-item label="用户ID" prop="uid">
          <el-input v-model="form.uid" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="消耗电能" prop="amount">
          <el-input-number v-model="form.amount" :precision="2" :min="0" placeholder="请输入消耗电能" style="width: 100%" />
        </el-form-item>
          <el-form-item label="电能余量" prop="balance">
          <el-input-number v-model="form.balance" :precision="2" :min="0" placeholder="请输入电能余量" style="width: 100%" />
        </el-form-item>
        <el-form-item label="消耗时间" prop="time">
          <el-input v-model="form.time" placeholder="请输入时间戳" />
        </el-form-item>
        <el-form-item label="电能消耗说明" prop="info">
          <el-input v-model="form.info" type="textarea" placeholder="请输入电能消耗说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VimOrderPays">
import { listVimOrderPays, getVimOrderPays, delVimOrderPays, addVimOrderPays, updateVimOrderPays } from "@/api/VimOrderPaySys/VimOrderPays";

const { proxy } = getCurrentInstance();

const VimOrderPaysList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    id: null,
    uid: null,
    amount: null,
    info: null,
  },
  rules: {
    id: [
      { required: true, message: "订单号不能为空", trigger: "blur" }
    ],
    uid: [
      { required: true, message: "用户ID不能为空", trigger: "blur" }
    ],
    amount: [
      { required: true, message: "消耗电能不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户电能消费列表 */
function getList() {
  loading.value = true;
  listVimOrderPays(queryParams.value).then(response => {
    VimOrderPaysList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    uid: null,
    amount: null,
    balance: null,
    time: null,
    info: null
  };
  proxy.resetForm("vimOrderPayRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加用户电能消费";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getVimOrderPays(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改用户电能消费";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["vimOrderPayRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateVimOrderPays(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addVimOrderPays(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除用户电能消费编号为"' + _ids + '"的数据项？').then(function() {
    return delVimOrderPays(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('VimOrderPaySys/VimOrderPays/export', {
    ...queryParams.value
  }, `用户电能消费_${new Date().getTime()}.xlsx`)
}

/** 格式化时间戳 */
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-';
  const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
  const date = new Date(ts);
  const padZero = num => num.toString().padStart(2, '0');

  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
};

onMounted(() => {
  getList();
});
</script>
