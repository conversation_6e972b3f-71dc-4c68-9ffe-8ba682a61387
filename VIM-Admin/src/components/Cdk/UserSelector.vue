<template>
  <div class="user-id-selector" :style="{ width: width }">
    <el-input
        v-model="userIdInput"
        :placeholder="placeholder"
        clearable
        @input="handleUserIdInput"
        @blur="handleUserIdBlur"
        @keyup.enter="handleUserIdSearch"
        :style="{ width: '100%' }"
    >
      <template #append>
        <el-button
            :loading="loading"
            @click="handleUserIdSearch"
            :disabled="!userIdInput || userIdInput.trim() === ''"
        >
          查询
        </el-button>
      </template>
    </el-input>

    <!-- 用户信息显示 -->
    <div v-if="selectedUser" class="selected-user-info" style="margin-top: 8px;">
      <div class="user-basic-info">
        <el-tag type="success" size="small">
          {{ selectedUser.nickname }} ({{ selectedUser.phone }}) - ID: {{ selectedUser.id }}
        </el-tag>
        <el-tag
            :type="getIdentityTagType(selectedUser.identity)"
            size="small"
            style="margin-left: 4px;"
        >
          {{ selectedUser.identityName }}
        </el-tag>
      </div>
      <div class="user-detail-info" style="margin-top: 4px; font-size: 12px; color: #666;">
        等级: {{ selectedUser.level }} |
        余额: {{ selectedUser.balance }} |
        钥匙: {{ selectedUser.keyAmount }} |
        背包价值: {{ selectedUser.backpackValue }}
      </div>
      <el-button
          type="text"
          size="small"
          @click="clearSelection"
          style="margin-top: 4px;"
      >
        清除选择
      </el-button>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message" style="margin-top: 4px;">
      <el-text type="danger" size="small">{{ errorMessage }}</el-text>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { getUserDetailInfo } from '@/api/cdk'

// Props
const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  placeholder: {
    type: String,
    default: '请输入用户ID'
  },
  width: {
    type: String,
    default: '300px'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'user-selected'])

// 响应式数据
const userIdInput = ref('')
const selectedUser = ref(null)
const loading = ref(false)
const errorMessage = ref('')

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && newVal !== userIdInput.value) {
    userIdInput.value = newVal.toString()
    // 如果有值但没有用户信息，尝试查询
    if (!selectedUser.value) {
      handleUserIdSearch()
    }
  } else if (!newVal) {
    clearSelection()
  }
})

// 处理用户ID输入
const handleUserIdInput = (value) => {
  errorMessage.value = ''
  // 只允许输入数字
  const numericValue = value.replace(/[^\d]/g, '')
  if (numericValue !== value) {
    userIdInput.value = numericValue
  }
}

// 处理输入框失焦
const handleUserIdBlur = () => {
  if (userIdInput.value && userIdInput.value.trim() !== '') {
    handleUserIdSearch()
  }
}

// 查询用户信息
const handleUserIdSearch = () => {
  const userId = userIdInput.value.trim()

  if (!userId) {
    errorMessage.value = '请输入用户ID'
    return
  }

  if (!/^\d+$/.test(userId)) {
    errorMessage.value = '用户ID必须是数字'
    return
  }

  console.log('UserSelector searching for user ID:', userId)

  loading.value = true
  errorMessage.value = ''

  getUserDetailInfo(parseInt(userId)).then(response => {
    console.log('UserSelector user detail response:', response)

    // 正确解析后端返回的数据结构
    if (response && response.code === 200 && response.data && response.data.id) {
      const userData = response.data

      selectedUser.value = {
        id: userData.id,
        nickname: userData.nickname,
        phone: userData.phone,
        username: userData.username,
        level: userData.level,
        identity: userData.identity,
        identityName: userData.identityName,
        balance: userData.balance,
        keyAmount: userData.keyAmount,
        backpackValue: userData.backpackValue
      }

      console.log('UserSelector selected user:', selectedUser.value)

      // 触发选择事件
      emit('update:modelValue', userData.id)
      emit('change', userData.id, selectedUser.value)
      emit('user-selected', userData.id, selectedUser.value)

      errorMessage.value = ''
    } else if (response && response.code !== 200) {
      // 后端返回错误
      errorMessage.value = response.msg || '查询用户信息失败'
      selectedUser.value = null
    } else {
      // 数据格式异常
      errorMessage.value = '未找到该用户或数据格式异常'
      selectedUser.value = null
    }
  }).catch(error => {
    console.error('UserSelector user detail error:', error)

    // 更详细的错误处理
    if (error.response) {
      // 服务器返回错误状态码
      if (error.response.status === 404) {
        errorMessage.value = '用户不存在'
      } else if (error.response.status === 403) {
        errorMessage.value = '没有权限查询该用户'
      } else {
        errorMessage.value = `查询失败：${error.response.data?.msg || '服务器错误'}`
      }
    } else if (error.request) {
      // 网络错误
      errorMessage.value = '网络连接失败，请检查网络连接'
    } else {
      // 其他错误
      errorMessage.value = '查询用户信息失败，请稍后重试'
    }

    selectedUser.value = null
  }).finally(() => {
    loading.value = false
  })
}

// 清除选择
const clearSelection = () => {
  userIdInput.value = ''
  selectedUser.value = null
  errorMessage.value = ''

  emit('update:modelValue', null)
  emit('change', null, null)
  emit('user-selected', null, null)
}

// 获取身份标签类型
const getIdentityTagType = (identity) => {
  switch (identity) {
    case 1:
      return 'info'      // 普通用户 - 蓝色
    case 2:
      return 'warning'   // 线上主播 - 橙色
    case 3:
      return 'danger'    // 线下主播 - 红色
    default:
      return 'info'
  }
}
</script>

<style scoped>
.user-id-selector {
  position: relative;
}

.selected-user-info {
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-basic-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.user-detail-info {
  margin-top: 6px;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  line-height: 1.4;
}

.text-gray-400 {
  color: #9ca3af;
}
</style>
