package com.skin86.starter.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 商品列表请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record GoodsListRequest(
    @JsonProperty("platform") @NotBlank String platform,
    @JsonProperty("page") @NotNull @Min(1) Integer page,
    @JsonProperty("page_size") @NotNull @Min(1) Integer pageSize
) {
    
    /**
     * 创建请求对象的便捷方法
     * 
     * @param platform 平台名称
     * @param page 页码
     * @param pageSize 页面大小
     * @return 请求对象
     */
    public static GoodsListRequest of(String platform, Integer page, Integer pageSize) {
        return new GoodsListRequest(platform, page, pageSize);
    }
    
    /**
     * 创建默认页面大小的请求对象
     * 
     * @param platform 平台名称
     * @param page 页码
     * @return 请求对象
     */
    public static GoodsListRequest of(String platform, Integer page) {
        return new GoodsListRequest(platform, page, 100);
    }
}