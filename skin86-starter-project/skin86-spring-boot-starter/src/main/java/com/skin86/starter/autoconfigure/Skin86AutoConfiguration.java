package com.skin86.starter.autoconfigure;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.skin86.starter.client.Skin86ApiClient;
import com.skin86.starter.service.Skin86DataService;
import com.skin86.starter.service.Skin86DataServiceImpl;
import okhttp3.OkHttpClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * Skin86 自动配置类
 * 
 * 自动配置Skin86相关的Bean，包括API客户端、数据服务等
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@AutoConfiguration
@ConditionalOnClass({Skin86ApiClient.class, OkHttpClient.class})
@ConditionalOnProperty(prefix = "skin86.api", name = {"app-id", "app-secret"})
@EnableConfigurationProperties(Skin86Properties.class)
@EnableAsync
public class Skin86AutoConfiguration {

    /**
     * 配置ObjectMapper
     * 
     * @return ObjectMapper实例
     */
    @Bean
    @ConditionalOnMissingBean
    public ObjectMapper skin86ObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }

    /**
     * 配置OkHttpClient
     * 
     * @param properties Skin86配置属性
     * @return OkHttpClient实例
     */
    @Bean
    @ConditionalOnMissingBean(name = "skin86OkHttpClient")
    public OkHttpClient skin86OkHttpClient(Skin86Properties properties) {
        return new OkHttpClient.Builder()
            .connectTimeout(Duration.ofSeconds(10))
            .readTimeout(Duration.ofSeconds(20))
            .writeTimeout(Duration.ofSeconds(10))
            .callTimeout(properties.api().timeout())
            .retryOnConnectionFailure(true)
            .connectionPool(new okhttp3.ConnectionPool(
                20,                                           // 最大空闲连接数
                5,                                            // 连接保活时间
                TimeUnit.MINUTES
            ))
            .dispatcher(createOptimizedDispatcher(properties))
            .build();
    }

    /**
     * 创建优化的OkHttp Dispatcher
     * 
     * @param properties Skin86配置属性
     * @return Dispatcher实例
     */
    private okhttp3.Dispatcher createOptimizedDispatcher(Skin86Properties properties) {
        okhttp3.Dispatcher dispatcher = new okhttp3.Dispatcher();
        dispatcher.setMaxRequests(properties.performance().maxConcurrency() * 2);
        dispatcher.setMaxRequestsPerHost(properties.performance().maxConcurrency());
        return dispatcher;
    }

    /**
     * 配置Skin86 API客户端
     * 
     * @param httpClient OkHttpClient实例
     * @param objectMapper ObjectMapper实例
     * @param properties Skin86配置属性
     * @return Skin86ApiClient实例
     */
    @Bean
    @ConditionalOnMissingBean
    public Skin86ApiClient skin86ApiClient(
            OkHttpClient httpClient,
            ObjectMapper objectMapper,
            Skin86Properties properties) {
        return new Skin86ApiClient(httpClient, objectMapper, properties);
    }

    /**
     * 配置Skin86数据服务
     * 
     * @param apiClient Skin86 API客户端
     * @param properties Skin86配置属性
     * @return Skin86DataService实例
     */
    @Bean
    @ConditionalOnMissingBean
    public Skin86DataService skin86DataService(
            Skin86ApiClient apiClient,
            Skin86Properties properties) {
        return new Skin86DataServiceImpl(apiClient, properties);
    }

    /**
     * 条件配置类 - 当需要数据持久化时
     */
    @Configuration
    @ConditionalOnClass(name = "org.springframework.data.jpa.repository.JpaRepository")
    @ConditionalOnProperty(prefix = "skin86", name = "enable-persistence", havingValue = "true", matchIfMissing = false)
    static class PersistenceConfiguration {
        // 这里可以添加JPA相关的自动配置
        // 比如实体扫描、Repository配置等
    }

    /**
     * 条件配置类 - 当需要Web支持时
     */
    @Configuration
    @ConditionalOnClass(name = "org.springframework.web.servlet.DispatcherServlet")
    @ConditionalOnProperty(prefix = "skin86", name = "enable-web", havingValue = "true", matchIfMissing = false)
    static class WebConfiguration {
        // 这里可以添加Web相关的自动配置
        // 比如Controller、WebMvcConfigurer等
    }
}