package com.skin86.starter.service;

import com.skin86.starter.dto.SkinGoodsInfo;
import com.skin86.starter.dto.SkinPriceInfo;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Skin86数据服务接口
 * 
 * 提供简洁易用的数据获取API，屏蔽底层实现细节
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public interface Skin86DataService {

    /**
     * 获取指定平台的商品列表
     * 
     * @param platform 平台名称（如："yp", "buff", "steam", "igxe"）
     * @param page 页码（从1开始）
     * @param pageSize 每页数量
     * @return 商品列表
     */
    CompletableFuture<List<SkinGoodsInfo>> getGoodsList(String platform, int page, int pageSize);

    /**
     * 获取指定平台的所有商品列表（自动分页）
     * 
     * @param platform 平台名称
     * @param maxPages 最大页数（0表示获取所有）
     * @return 商品列表
     */
    CompletableFuture<List<SkinGoodsInfo>> getAllGoods(String platform, int maxPages);

    /**
     * 获取指定平台的所有商品列表（无页数限制）
     * 
     * @param platform 平台名称
     * @return 商品列表
     */
    default CompletableFuture<List<SkinGoodsInfo>> getAllGoods(String platform) {
        return getAllGoods(platform, 0);
    }

    /**
     * 获取单个商品的今日价格
     * 
     * @param platform 平台名称
     * @param goodsId 商品ID
     * @return 价格信息
     */
    CompletableFuture<SkinPriceInfo> getTodayPrice(String platform, Integer goodsId);

    /**
     * 批量获取商品价格
     * 
     * @param platform 平台名称
     * @param goodsIds 商品ID列表
     * @param date 查询日期
     * @return 价格列表
     */
    CompletableFuture<List<SkinPriceInfo>> getBatchPrices(String platform, List<Integer> goodsIds, LocalDate date);

    /**
     * 批量获取商品今日价格
     * 
     * @param platform 平台名称
     * @param goodsIds 商品ID列表
     * @return 价格列表
     */
    default CompletableFuture<List<SkinPriceInfo>> getTodayBatchPrices(String platform, List<Integer> goodsIds) {
        return getBatchPrices(platform, goodsIds, LocalDate.now());
    }

    /**
     * 搜索商品
     * 
     * @param platform 平台名称
     * @param keyword 关键词（商品名称）
     * @param maxResults 最大结果数
     * @return 商品列表
     */
    CompletableFuture<List<SkinGoodsInfo>> searchGoods(String platform, String keyword, int maxResults);

    /**
     * 获取支持的平台列表
     * 
     * @return 平台列表
     */
    List<String> getSupportedPlatforms();

    /**
     * 检查平台是否支持
     * 
     * @param platform 平台名称
     * @return 支持返回true，否则返回false
     */
    boolean isPlatformSupported(String platform);
}