package com.skin86.starter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 饰品商品信息DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record SkinGoodsInfo(
    @JsonProperty("goods_id") Integer goodsId,
    @JsonProperty("platform_id") String platformId,
    @JsonProperty("market_name") String marketName,
    @JsonProperty("market_hash_name") String marketHashName,
    @JsonProperty("sell_min_price") BigDecimal sellMinPrice,
    @JsonProperty("sell_max_num") Integer sellMaxNum,
    @JsonProperty("sell_valuation") BigDecimal sellValuation,
    @JsonProperty("buy_max_price") BigDecimal buyMaxPrice,
    @JsonProperty("buy_max_num") Integer buyMaxNum,
    @JsonProperty("price_alter_percentage_7d") BigDecimal priceAlterPercentage7d,
    @JsonProperty("price_alter_value_7d") BigDecimal priceAlterValue7d,
    @JsonProperty("category_group_name") String categoryGroupName,
    @JsonProperty("rarity_color") String rarityColor,
    @JsonProperty("icon_url") String iconUrl,
    @JsonProperty("is_follow") Boolean isFollow,
    @JsonProperty("redirect_url") String redirectUrl,
    @JsonProperty("exterior") String exterior,
    @JsonProperty("rarity") String rarity,
    @JsonProperty("platform") String platform
) {
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return marketName != null ? marketName : marketHashName;
    }
    
    /**
     * 是否有有效价格
     * 
     * @return 有价格返回true，否则返回false
     */
    public boolean hasValidPrice() {
        return (sellMinPrice != null && sellMinPrice.compareTo(BigDecimal.ZERO) > 0) ||
               (buyMaxPrice != null && buyMaxPrice.compareTo(BigDecimal.ZERO) > 0);
    }
    
    /**
     * 获取最优价格（优先取卖出最低价）
     * 
     * @return 最优价格
     */
    public BigDecimal getBestPrice() {
        if (sellMinPrice != null && sellMinPrice.compareTo(BigDecimal.ZERO) > 0) {
            return sellMinPrice;
        }
        if (buyMaxPrice != null && buyMaxPrice.compareTo(BigDecimal.ZERO) > 0) {
            return buyMaxPrice;
        }
        return BigDecimal.ZERO;
    }
}