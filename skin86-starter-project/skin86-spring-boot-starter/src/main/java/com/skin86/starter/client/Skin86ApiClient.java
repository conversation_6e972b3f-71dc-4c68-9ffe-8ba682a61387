package com.skin86.starter.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.skin86.starter.autoconfigure.Skin86Properties;
import com.skin86.starter.dto.ApiResponse;
import com.skin86.starter.dto.request.BatchPricesRequest;
import com.skin86.starter.dto.request.GoodsListRequest;
import com.skin86.starter.dto.request.TodayPriceRequest;
import com.skin86.starter.dto.response.BatchPricesResponse;
import com.skin86.starter.dto.response.GoodsListResponse;
import com.skin86.starter.dto.response.TodayPriceResponse;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;

/**
 * Skin86 API客户端
 * 
 * 提供对Skin86 API的统一访问接口
 * 支持签名认证、异步调用和错误处理
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class Skin86ApiClient {

    private static final Logger logger = LoggerFactory.getLogger(Skin86ApiClient.class);
    
    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final Skin86Properties properties;
    private final SecureRandom secureRandom;

    public Skin86ApiClient(OkHttpClient httpClient, ObjectMapper objectMapper, Skin86Properties properties) {
        this.httpClient = httpClient;
        this.objectMapper = objectMapper;
        this.properties = properties;
        this.secureRandom = new SecureRandom();
    }

    /**
     * 获取指定饰品价格
     * 
     * @param request 请求参数
     * @return 价格信息
     */
    public CompletableFuture<ApiResponse<TodayPriceResponse>> getTodayPrice(TodayPriceRequest request) {
        return executeApiCall("/api/v1/skin/goods/third/today_price", request, 
            new TypeReference<ApiResponse<TodayPriceResponse>>() {});
    }

    /**
     * 获取饰品列表
     * 
     * @param request 请求参数
     * @return 饰品列表
     */
    public CompletableFuture<ApiResponse<GoodsListResponse>> getGoodsList(GoodsListRequest request) {
        return executeApiCall("/api/v1/skin/goods/third/list", request,
            new TypeReference<ApiResponse<GoodsListResponse>>() {});
    }

    /**
     * 批量获取饰品价格
     * 
     * @param request 请求参数
     * @return 批量价格信息
     */
    public CompletableFuture<ApiResponse<BatchPricesResponse>> getBatchPrices(BatchPricesRequest request) {
        return executeApiCall("/api/v1/skin/goods/third/prices", request,
            new TypeReference<ApiResponse<BatchPricesResponse>>() {});
    }

    /**
     * 执行API调用
     * 
     * @param endpoint API端点
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @return 异步响应结果
     */
    private <T, R> CompletableFuture<ApiResponse<R>> executeApiCall(
            String endpoint, T requestBody, TypeReference<ApiResponse<R>> responseType) {
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 生成请求参数
                String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
                String nonce = generateNonce();
                
                // 构建签名参数
                Map<String, String> signParams = buildSignatureParams(timestamp, nonce, endpoint);
                String signature = generateSignature(signParams);
                
                // 构建请求
                String requestJson = objectMapper.writeValueAsString(requestBody);
                RequestBody body = RequestBody.create(requestJson, JSON);
                
                Request request = new Request.Builder()
                    .url(properties.api().baseUrl() + endpoint)
                    .post(body)
                    .addHeader("appId", properties.api().appId())
                    .addHeader("timestamp", timestamp)
                    .addHeader("nonce", nonce)
                    .addHeader("signature", signature)
                    .addHeader("Content-Type", "application/json")
                    .build();
                
                logger.debug("发送API请求: {} - URL: {}", endpoint, properties.api().baseUrl() + endpoint);
                logger.debug("请求体: {}", requestJson);

                // 执行请求
                try (Response response = httpClient.newCall(request).execute()) {
                    logger.debug("收到HTTP响应: code={}, successful={}", response.code(), response.isSuccessful());

                    if (!response.isSuccessful()) {
                        String errorBody = response.body() != null ? response.body().string() : "无响应体";
                        logger.error("API调用失败: HTTP {} - 响应体: {}", response.code(), errorBody);
                        throw new RuntimeException("API调用失败: " + response.code() + " - " + response.message());
                    }

                    String responseBody = null;
                    if (response.body() != null) {
                        responseBody = response.body().string();
                    }
                    return objectMapper.readValue(responseBody, responseType);
                }
                
            } catch (IOException e) {
                logger.error("网络IO异常: {} - {}", endpoint, e.getMessage(), e);
                throw new RuntimeException("网络连接失败: " + e.getMessage(), e);
            } catch (Exception e) {
                logger.error("API调用异常: {} - {}", endpoint, e.getMessage(), e);
                throw new RuntimeException("API调用失败: " + e.getMessage(), e);
            }
        });
    }

    /**
     * 构建签名参数
     * 
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param action API路径
     * @return 签名参数
     */
    private Map<String, String> buildSignatureParams(String timestamp, String nonce, String action) {
        Map<String, String> params = new TreeMap<>();
        params.put("appId", properties.api().appId());
        params.put("timestamp", timestamp);
        params.put("nonce", nonce);
        params.put("action", action);
        params.put("version", "v1");
        return params;
    }

    /**
     * 生成HMAC-SHA256签名
     * 
     * @param params 参数映射
     * @return Base64编码的签名
     */
    private String generateSignature(Map<String, String> params) {
        try {
            // 按key字典序排序并拼接
            StringBuilder signString = new StringBuilder();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                signString.append(entry.getKey()).append(entry.getValue());
            }
            signString.append(properties.api().appSecret());
            
            logger.debug("待签名字符串: {}", signString.toString());
            
            // 生成HMAC-SHA256签名
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(
                properties.api().appSecret().getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);
            
            byte[] signBytes = mac.doFinal(signString.toString().getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signBytes);
            
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("签名生成失败", e);
        }
    }

    /**
     * 生成随机字符串
     * 
     * @return 12位随机字符串
     */
    private String generateNonce() {
        byte[] bytes = new byte[9]; // 9 bytes = 12 characters in base64
        secureRandom.nextBytes(bytes);
        return Base64.getEncoder().encodeToString(bytes).substring(0, 12);
    }
}