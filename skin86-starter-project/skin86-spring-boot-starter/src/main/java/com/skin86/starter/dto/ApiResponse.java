package com.skin86.starter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 通用API响应DTO
 * 
 * @param code 响应码
 * @param message 响应消息
 * @param data 响应数据
 * @param <T> 数据类型
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record ApiResponse<T>(
    @JsonProperty("code") Integer code,
    @JsonProperty("message") String message,
    @JsonProperty("data") T data
) {
    
    /**
     * 判断请求是否成功
     * 
     * @return 成功返回true，失败返回false
     */
    public boolean isSuccess() {
        return code != null && code == 200;
    }
    
    /**
     * 创建成功响应
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "success", data);
    }
    
    /**
     * 创建失败响应
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null);
    }
}