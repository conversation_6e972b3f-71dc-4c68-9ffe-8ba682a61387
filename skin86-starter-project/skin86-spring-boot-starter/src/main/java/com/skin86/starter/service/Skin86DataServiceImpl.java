package com.skin86.starter.service;

import com.skin86.starter.autoconfigure.Skin86Properties;
import com.skin86.starter.client.Skin86ApiClient;
import com.skin86.starter.dto.ApiResponse;
import com.skin86.starter.dto.SkinGoodsInfo;
import com.skin86.starter.dto.SkinPriceInfo;
import com.skin86.starter.dto.request.BatchPricesRequest;
import com.skin86.starter.dto.request.GoodsListRequest;
import com.skin86.starter.dto.request.TodayPriceRequest;
import com.skin86.starter.dto.response.BatchPricesResponse;
import com.skin86.starter.dto.response.GoodsListResponse;
import com.skin86.starter.dto.response.TodayPriceResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * Skin86数据服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class Skin86DataServiceImpl implements Skin86DataService {

    private static final Logger logger = LoggerFactory.getLogger(Skin86DataServiceImpl.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final Skin86ApiClient apiClient;
    private final Skin86Properties properties;
    private final Semaphore concurrencyLimiter;

    public Skin86DataServiceImpl(Skin86ApiClient apiClient, Skin86Properties properties) {
        this.apiClient = apiClient;
        this.properties = properties;
        this.concurrencyLimiter = new Semaphore(properties.performance().maxConcurrency());
    }

    @Override
    @Async
    public CompletableFuture<List<SkinGoodsInfo>> getGoodsList(String platform, int page, int pageSize) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                concurrencyLimiter.acquire();
                
                String normalizedPlatform = normalizePlatformName(platform);
                GoodsListRequest request = GoodsListRequest.of(normalizedPlatform, page, pageSize);
                
                ApiResponse<GoodsListResponse> response = apiClient.getGoodsList(request).get();
                
                if (!response.isSuccess()) {
                    logger.error("获取商品列表失败：{} - {}", response.code(), response.message());
                    return Collections.emptyList();
                }
                
                GoodsListResponse data = response.data();
                return data != null && data.list() != null ? data.list() : Collections.emptyList();
                
            } catch (Exception e) {
                logger.error("获取{}平台第{}页商品列表异常", platform, page, e);
                return Collections.emptyList();
            } finally {
                concurrencyLimiter.release();
            }
        });
    }

    @Override
    @Async
    public CompletableFuture<List<SkinGoodsInfo>> getAllGoods(String platform, int maxPages) {
        return CompletableFuture.supplyAsync(() -> {
            List<SkinGoodsInfo> allGoods = new ArrayList<>();
            int currentPage = 1;
            int pageSize = properties.performance().batchSize();
            
            logger.info("开始获取{}平台所有商品数据，最大页数：{}", platform, maxPages > 0 ? maxPages : "无限制");
            
            try {
                while (maxPages <= 0 || currentPage <= maxPages) {
                    // 添加延迟避免API限流
                    if (currentPage > 1) {
                        Thread.sleep(properties.performance().delayMs().toMillis());
                    }
                    
                    List<SkinGoodsInfo> pageGoods = getGoodsList(platform, currentPage, pageSize).get();
                    
                    if (pageGoods.isEmpty()) {
                        logger.info("{}平台第{}页无数据，停止获取", platform, currentPage);
                        break;
                    }
                    
                    allGoods.addAll(pageGoods);
                    logger.debug("{}平台第{}页获取完成，本页{}条数据，累计{}条", 
                        platform, currentPage, pageGoods.size(), allGoods.size());
                    
                    // 如果本页数据少于页面大小，说明已经是最后一页
                    if (pageGoods.size() < pageSize) {
                        logger.info("{}平台数据获取完成，共{}页，累计{}条数据", platform, currentPage, allGoods.size());
                        break;
                    }
                    
                    currentPage++;
                }
                
            } catch (Exception e) {
                logger.error("获取{}平台所有商品异常", platform, e);
            }
            
            return allGoods;
        });
    }

    @Override
    @Async
    public CompletableFuture<SkinPriceInfo> getTodayPrice(String platform, Integer goodsId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                concurrencyLimiter.acquire();
                
                String normalizedPlatform = normalizePlatformName(platform);
                TodayPriceRequest request = TodayPriceRequest.of(normalizedPlatform, goodsId);
                
                ApiResponse<TodayPriceResponse> response = apiClient.getTodayPrice(request).get();
                
                if (!response.isSuccess()) {
                    logger.error("获取今日价格失败：{} - {}", response.code(), response.message());
                    return null;
                }
                
                TodayPriceResponse data = response.data();
                return data != null ? data.priceInfo() : null;
                
            } catch (Exception e) {
                logger.error("获取{}平台商品{}今日价格异常", platform, goodsId, e);
                return null;
            } finally {
                concurrencyLimiter.release();
            }
        });
    }

    @Override
    @Async
    public CompletableFuture<List<SkinPriceInfo>> getBatchPrices(String platform, List<Integer> goodsIds, LocalDate date) {
        return CompletableFuture.supplyAsync(() -> {
            if (goodsIds == null || goodsIds.isEmpty()) {
                return Collections.emptyList();
            }
            
            List<SkinPriceInfo> allPrices = new ArrayList<>();
            int batchSize = properties.performance().batchSize();
            String dateStr = date.format(DATE_FORMATTER);
            
            logger.info("开始批量获取{}平台{}条商品价格，日期：{}", platform, goodsIds.size(), dateStr);
            
            try {
                // 分批处理商品ID
                for (int i = 0; i < goodsIds.size(); i += batchSize) {
                    concurrencyLimiter.acquire();
                    
                    try {
                        // 添加延迟
                        if (i > 0) {
                            Thread.sleep(properties.performance().delayMs().toMillis());
                        }
                        
                        // 构建批次商品ID
                        List<Integer> batchIds = goodsIds.subList(i, Math.min(i + batchSize, goodsIds.size()));
                        String goodsIdsStr = batchIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                        
                        // 调用API
                        String normalizedPlatform = normalizePlatformName(platform);
                        BatchPricesRequest request = BatchPricesRequest.of(
                            normalizedPlatform, 1, batchSize, dateStr, goodsIdsStr);
                        
                        ApiResponse<BatchPricesResponse> response = apiClient.getBatchPrices(request).get();
                        
                        if (!response.isSuccess()) {
                            logger.error("批次价格API调用失败：{} - {}", response.code(), response.message());
                            continue;
                        }
                        
                        BatchPricesResponse data = response.data();
                        if (data != null && data.list() != null) {
                            allPrices.addAll(data.list());
                        }
                        
                        logger.debug("{}平台价格批次{}获取完成，本批次{}条", 
                            platform, (i / batchSize) + 1, data != null ? data.getDataCount() : 0);
                        
                    } finally {
                        concurrencyLimiter.release();
                    }
                }
                
            } catch (Exception e) {
                logger.error("批量获取{}平台商品价格异常", platform, e);
            }
            
            logger.info("{}平台价格数据获取完成：成功{}条", platform, allPrices.size());
            return allPrices;
        });
    }

    @Override
    @Async
    public CompletableFuture<List<SkinGoodsInfo>> searchGoods(String platform, String keyword, int maxResults) {
        return CompletableFuture.supplyAsync(() -> {
            if (keyword == null || keyword.trim().isEmpty()) {
                return Collections.emptyList();
            }
            
            String lowerKeyword = keyword.toLowerCase().trim();
            List<SkinGoodsInfo> results = new ArrayList<>();
            int currentPage = 1;
            int pageSize = properties.performance().batchSize();
            
            logger.info("开始搜索{}平台商品，关键词：{}，最大结果数：{}", platform, keyword, maxResults);
            
            try {
                while (results.size() < maxResults) {
                    // 添加延迟避免API限流
                    if (currentPage > 1) {
                        Thread.sleep(properties.performance().delayMs().toMillis());
                    }
                    
                    List<SkinGoodsInfo> pageGoods = getGoodsList(platform, currentPage, pageSize).get();
                    
                    if (pageGoods.isEmpty()) {
                        break;
                    }
                    
                    // 过滤匹配的商品
                    List<SkinGoodsInfo> matchedGoods = pageGoods.stream()
                        .filter(goods -> matchesKeyword(goods, lowerKeyword))
                        .limit(maxResults - results.size())
                        .toList();
                    
                    results.addAll(matchedGoods);
                    
                    logger.debug("{}平台第{}页搜索完成，匹配{}条，累计{}条", 
                        platform, currentPage, matchedGoods.size(), results.size());
                    
                    // 如果本页数据少于页面大小，说明已经是最后一页
                    if (pageGoods.size() < pageSize) {
                        break;
                    }
                    
                    currentPage++;
                }
                
            } catch (Exception e) {
                logger.error("搜索{}平台商品异常", platform, e);
            }
            
            logger.info("{}平台商品搜索完成：找到{}条匹配结果", platform, results.size());
            return results;
        });
    }

    @Override
    public List<String> getSupportedPlatforms() {
        return new ArrayList<>(properties.platform().supported());
    }

    @Override
    public boolean isPlatformSupported(String platform) {
        if (platform == null || platform.trim().isEmpty()) {
            return false;
        }
        return properties.platform().supported().contains(platform.toLowerCase().trim());
    }

    /**
     * 标准化平台名称
     */
    private String normalizePlatformName(String platform) {
        if (platform == null) {
            return null;
        }

        String normalized = platform.toLowerCase().trim();
        return switch (normalized) {
            case "yp" -> "YP";
            case "buff", "buff163" -> "BUFF";
            case "steam" -> "STEAM";
            case "igex", "igxe" -> "IGXE";
            default -> {
                logger.warn("未知的平台名称: {}，使用大写格式", platform);
                yield platform.toUpperCase();
            }
        };
    }

    /**
     * 检查商品是否匹配关键词
     */
    private boolean matchesKeyword(SkinGoodsInfo goods, String lowerKeyword) {
        if (goods == null) {
            return false;
        }
        
        // 检查市场名称
        if (goods.marketName() != null && goods.marketName().toLowerCase().contains(lowerKeyword)) {
            return true;
        }
        
        // 检查市场哈希名称
        if (goods.marketHashName() != null && goods.marketHashName().toLowerCase().contains(lowerKeyword)) {
            return true;
        }
        
        // 检查稀有度
        if (goods.rarity() != null && goods.rarity().toLowerCase().contains(lowerKeyword)) {
            return true;
        }
        
        // 检查磨损
        if (goods.exterior() != null && goods.exterior().toLowerCase().contains(lowerKeyword)) {
            return true;
        }
        
        return false;
    }
}