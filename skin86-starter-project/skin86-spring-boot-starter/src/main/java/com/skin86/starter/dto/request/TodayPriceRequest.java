package com.skin86.starter.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 今日价格请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record TodayPriceRequest(
    @JsonProperty("platform") @NotBlank String platform,
    @JsonProperty("goods_id") @NotNull Integer goodsId
) {
    
    /**
     * 创建请求对象的便捷方法
     * 
     * @param platform 平台名称
     * @param goodsId 商品ID
     * @return 请求对象
     */
    public static TodayPriceRequest of(String platform, Integer goodsId) {
        return new TodayPriceRequest(platform, goodsId);
    }
}