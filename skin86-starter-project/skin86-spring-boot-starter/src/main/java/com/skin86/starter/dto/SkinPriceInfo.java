package com.skin86.starter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 饰品价格信息DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record SkinPriceInfo(
    @JsonProperty("goods_id") Integer goodsId,
    @JsonProperty("platform") String platform,
    @JsonProperty("price_date") LocalDate priceDate,
    @JsonProperty("sell_min_price") BigDecimal sellMinPrice,
    @JsonProperty("sell_max_num") Integer sellMaxNum,
    @JsonProperty("buy_max_price") BigDecimal buyMaxPrice,
    @JsonProperty("buy_max_num") Integer buyMaxNum,
    @JsonProperty("market_name") String marketName
) {
    
    /**
     * 是否有有效价格
     * 
     * @return 有价格返回true，否则返回false
     */
    public boolean hasValidPrice() {
        return (sellMinPrice != null && sellMinPrice.compareTo(BigDecimal.ZERO) > 0) ||
               (buyMaxPrice != null && buyMaxPrice.compareTo(BigDecimal.ZERO) > 0);
    }
    
    /**
     * 获取最优价格（优先取卖出最低价）
     * 
     * @return 最优价格
     */
    public BigDecimal getBestPrice() {
        if (sellMinPrice != null && sellMinPrice.compareTo(BigDecimal.ZERO) > 0) {
            return sellMinPrice;
        }
        if (buyMaxPrice != null && buyMaxPrice.compareTo(BigDecimal.ZERO) > 0) {
            return buyMaxPrice;
        }
        return BigDecimal.ZERO;
    }
    
    /**
     * 计算价格差异
     * 
     * @param otherPrice 其他价格
     * @return 价格差异
     */
    public BigDecimal calculatePriceDifference(SkinPriceInfo otherPrice) {
        if (otherPrice == null) {
            return BigDecimal.ZERO;
        }
        return this.getBestPrice().subtract(otherPrice.getBestPrice());
    }
    
    /**
     * 计算价格差异百分比
     * 
     * @param otherPrice 其他价格
     * @return 价格差异百分比
     */
    public BigDecimal calculatePriceDifferencePercentage(SkinPriceInfo otherPrice) {
        if (otherPrice == null || otherPrice.getBestPrice().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal difference = calculatePriceDifference(otherPrice);
        return difference.divide(otherPrice.getBestPrice(), 4, java.math.RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
    }
}