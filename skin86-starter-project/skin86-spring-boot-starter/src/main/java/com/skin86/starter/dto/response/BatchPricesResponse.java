package com.skin86.starter.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.skin86.starter.dto.SkinPriceInfo;
import java.util.List;

/**
 * 批量价格响应DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record BatchPricesResponse(
    @JsonProperty("list") List<SkinPriceInfo> list,
    @JsonProperty("total") Long total,
    @JsonProperty("page") Integer page,
    @JsonProperty("page_size") Integer pageSize
) {
    
    /**
     * 是否有数据
     * 
     * @return 有数据返回true，否则返回false
     */
    public boolean hasData() {
        return list != null && !list.isEmpty();
    }
    
    /**
     * 获取数据数量
     * 
     * @return 数据数量
     */
    public int getDataCount() {
        return list != null ? list.size() : 0;
    }
    
    /**
     * 获取有效价格数量
     * 
     * @return 有效价格数量
     */
    public int getValidPriceCount() {
        if (list == null) {
            return 0;
        }
        return (int) list.stream().filter(SkinPriceInfo::hasValidPrice).count();
    }
}