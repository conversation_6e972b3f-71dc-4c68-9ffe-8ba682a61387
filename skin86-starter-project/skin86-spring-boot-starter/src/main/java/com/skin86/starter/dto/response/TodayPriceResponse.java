package com.skin86.starter.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.skin86.starter.dto.SkinPriceInfo;

/**
 * 今日价格响应DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record TodayPriceResponse(
    @JsonProperty("price_info") SkinPriceInfo priceInfo
) {
    
    /**
     * 是否有有效价格
     * 
     * @return 有价格返回true，否则返回false
     */
    public boolean hasValidPrice() {
        return priceInfo != null && priceInfo.hasValidPrice();
    }
}