package com.skin86.starter.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 批量价格请求DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record BatchPricesRequest(
    @JsonProperty("platform") @NotBlank String platform,
    @JsonProperty("page") @NotNull @Min(1) Integer page,
    @JsonProperty("page_size") @NotNull @Min(1) Integer pageSize,
    @JsonProperty("date") @NotBlank String date,
    @JsonProperty("goods_ids") @NotBlank String goodsIds
) {
    
    /**
     * 创建请求对象的便捷方法
     * 
     * @param platform 平台名称
     * @param page 页码
     * @param pageSize 页面大小
     * @param date 日期
     * @param goodsIds 商品ID列表（逗号分隔）
     * @return 请求对象
     */
    public static BatchPricesRequest of(String platform, Integer page, Integer pageSize, String date, String goodsIds) {
        return new BatchPricesRequest(platform, page, pageSize, date, goodsIds);
    }
}