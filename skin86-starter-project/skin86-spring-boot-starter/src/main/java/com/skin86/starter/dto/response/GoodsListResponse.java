package com.skin86.starter.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.skin86.starter.dto.SkinGoodsInfo;
import java.util.List;

/**
 * 商品列表响应DTO
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record GoodsListResponse(
    @JsonProperty("list") List<SkinGoodsInfo> list,
    @JsonProperty("total") Long total,
    @JsonProperty("page") Integer page,
    @JsonProperty("page_size") Integer pageSize
) {
    
    /**
     * 是否有数据
     * 
     * @return 有数据返回true，否则返回false
     */
    public boolean hasData() {
        return list != null && !list.isEmpty();
    }
    
    /**
     * 获取数据数量
     * 
     * @return 数据数量
     */
    public int getDataCount() {
        return list != null ? list.size() : 0;
    }
    
    /**
     * 是否还有下一页
     * 
     * @return 有下一页返回true，否则返回false
     */
    public boolean hasNextPage() {
        if (total == null || page == null || pageSize == null) {
            return false;
        }
        long totalPages = (total + pageSize - 1) / pageSize;
        return page < totalPages;
    }
}