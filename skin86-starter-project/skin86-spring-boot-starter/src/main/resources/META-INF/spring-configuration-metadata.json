{"groups": [{"name": "skin86", "type": "com.skin86.starter.autoconfigure.Skin86Properties", "description": "Skin86 数据采集相关配置"}, {"name": "skin86.api", "type": "com.skin86.starter.autoconfigure.Skin86Properties$ApiConfig", "description": "Skin86 API相关配置"}, {"name": "skin86.performance", "type": "com.skin86.starter.autoconfigure.Skin86Properties$PerformanceConfig", "description": "性能相关配置"}, {"name": "skin86.platform", "type": "com.skin86.starter.autoconfigure.Skin86Properties$PlatformConfig", "description": "平台相关配置"}], "properties": [{"name": "skin86.api.app-id", "type": "java.lang.String", "description": "Skin86 API应用ID", "required": true}, {"name": "skin86.api.app-secret", "type": "java.lang.String", "description": "Skin86 API应用密钥", "required": true}, {"name": "skin86.api.base-url", "type": "java.lang.String", "description": "Skin86 API基础URL", "defaultValue": "https://csdata-api.skin86.com"}, {"name": "skin86.api.timeout", "type": "java.time.Duration", "description": "API调用超时时间", "defaultValue": "30s"}, {"name": "skin86.performance.max-concurrency", "type": "java.lang.Integer", "description": "最大并发数", "defaultValue": 10, "minimum": 1, "maximum": 50}, {"name": "skin86.performance.delay-ms", "type": "java.time.Duration", "description": "请求延迟时间", "defaultValue": "200ms"}, {"name": "skin86.performance.retry-times", "type": "java.lang.Integer", "description": "重试次数", "defaultValue": 3, "minimum": 1, "maximum": 10}, {"name": "skin86.performance.retry-delay", "type": "java.time.Duration", "description": "重试延迟时间", "defaultValue": "2s"}, {"name": "skin86.performance.batch-size", "type": "java.lang.Integer", "description": "批次大小", "defaultValue": 500, "minimum": 10, "maximum": 2000}, {"name": "skin86.platform.supported", "type": "java.util.List<java.lang.String>", "description": "支持的平台列表", "defaultValue": ["yp", "buff", "steam", "igxe"]}, {"name": "skin86.platform.default-platforms", "type": "java.util.List<java.lang.String>", "description": "默认平台列表", "defaultValue": ["yp"]}], "hints": [{"name": "skin86.platform.supported", "values": [{"value": "yp", "description": "悠悠有品平台"}, {"value": "buff", "description": "BUFF平台"}, {"value": "steam", "description": "Steam平台"}, {"value": "igxe", "description": "IGXE平台"}]}, {"name": "skin86.platform.default-platforms", "values": [{"value": "yp", "description": "悠悠有品平台"}, {"value": "buff", "description": "BUFF平台"}, {"value": "steam", "description": "Steam平台"}, {"value": "igxe", "description": "IGXE平台"}]}]}