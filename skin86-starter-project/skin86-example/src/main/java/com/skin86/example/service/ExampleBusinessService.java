package com.skin86.example.service;

import com.skin86.starter.dto.SkinGoodsInfo;
import com.skin86.starter.dto.SkinPriceInfo;
import com.skin86.starter.service.Skin86DataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 示例业务服务
 * 
 * 演示如何在业务层使用 Skin86DataService
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class ExampleBusinessService {

    private static final Logger logger = LoggerFactory.getLogger(ExampleBusinessService.class);

    @Autowired
    private Skin86DataService skin86DataService;

    /**
     * 获取热门商品
     * 
     * @param platform 平台名称
     * @param limit 数量限制
     * @return 热门商品列表
     */
    public CompletableFuture<List<SkinGoodsInfo>> getPopularGoods(String platform, int limit) {
        logger.info("获取{}平台热门商品，数量限制：{}", platform, limit);
        
        return skin86DataService.getGoodsList(platform, 1, limit)
            .thenApply(goods -> goods.stream()
                .filter(SkinGoodsInfo::hasValidPrice)
                .filter(good -> good.getBestPrice().compareTo(BigDecimal.valueOf(100)) > 0) // 价格大于100的商品
                .limit(limit)
                .toList());
    }

    /**
     * 价格比较分析
     * 
     * @param goodsId 商品ID
     * @param platforms 平台列表
     * @return 价格比较结果
     */
    public CompletableFuture<PriceComparisonResult> comparePrices(Integer goodsId, List<String> platforms) {
        logger.info("开始价格比较分析，商品ID：{}，平台：{}", goodsId, platforms);
        
        // 并行获取各平台价格
        List<CompletableFuture<PlatformPrice>> futures = platforms.stream()
            .map(platform -> skin86DataService.getTodayPrice(platform, goodsId)
                .thenApply(price -> new PlatformPrice(platform, price)))
            .toList();
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenApply(v -> {
                List<PlatformPrice> prices = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(pp -> pp.price() != null && pp.price().hasValidPrice())
                    .toList();
                
                return analyzePrices(goodsId, prices);
            });
    }

    /**
     * 分析价格数据
     */
    private PriceComparisonResult analyzePrices(Integer goodsId, List<PlatformPrice> prices) {
        if (prices.isEmpty()) {
            return new PriceComparisonResult(goodsId, null, null, BigDecimal.ZERO, null);
        }
        
        PlatformPrice minPrice = prices.stream()
            .min((p1, p2) -> p1.price().getBestPrice().compareTo(p2.price().getBestPrice()))
            .orElse(null);
        
        PlatformPrice maxPrice = prices.stream()
            .max((p1, p2) -> p1.price().getBestPrice().compareTo(p2.price().getBestPrice()))
            .orElse(null);
        
        BigDecimal priceDiff = maxPrice != null && minPrice != null ?
            maxPrice.price().getBestPrice().subtract(minPrice.price().getBestPrice()) : BigDecimal.ZERO;
        
        return new PriceComparisonResult(goodsId, minPrice, maxPrice, priceDiff, prices);
    }

    /**
     * 搜索并过滤商品
     * 
     * @param platform 平台名称
     * @param keyword 关键词
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 过滤后的商品列表
     */
    public CompletableFuture<List<SkinGoodsInfo>> searchAndFilterGoods(
            String platform, String keyword, BigDecimal minPrice, BigDecimal maxPrice) {
        
        logger.info("搜索并过滤商品，平台：{}，关键词：{}，价格范围：{}-{}", platform, keyword, minPrice, maxPrice);
        
        return skin86DataService.searchGoods(platform, keyword, 100)
            .thenApply(goods -> goods.stream()
                .filter(SkinGoodsInfo::hasValidPrice)
                .filter(good -> {
                    BigDecimal price = good.getBestPrice();
                    return (minPrice == null || price.compareTo(minPrice) >= 0) &&
                           (maxPrice == null || price.compareTo(maxPrice) <= 0);
                })
                .toList());
    }

    /**
     * 平台价格记录
     */
    public record PlatformPrice(String platform, SkinPriceInfo price) {}

    /**
     * 价格比较结果
     */
    public record PriceComparisonResult(
        Integer goodsId,
        PlatformPrice minPrice,
        PlatformPrice maxPrice,
        BigDecimal priceDifference,
        List<PlatformPrice> allPrices
    ) {}
}