package com.skin86.example.controller;

import com.skin86.starter.dto.SkinGoodsInfo;
import com.skin86.starter.dto.SkinPriceInfo;
import com.skin86.starter.service.Skin86DataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Skin86 示例控制器
 * 
 * 演示如何使用 Skin86DataService 获取数据
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/skin86")
public class ExampleController {

    @Autowired
    private Skin86DataService skin86DataService;

    /**
     * 获取商品列表
     * 
     * @param platform 平台名称
     * @param page 页码
     * @param pageSize 页面大小
     * @return 商品列表
     */
    @GetMapping("/goods")
    public CompletableFuture<List<SkinGoodsInfo>> getGoods(
            @RequestParam(defaultValue = "yp") String platform,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize) {
        return skin86DataService.getGoodsList(platform, page, pageSize);
    }

    /**
     * 获取单个商品今日价格
     * 
     * @param platform 平台名称
     * @param goodsId 商品ID
     * @return 价格信息
     */
    @GetMapping("/price/{goodsId}")
    public CompletableFuture<SkinPriceInfo> getTodayPrice(
            @RequestParam(defaultValue = "yp") String platform,
            @PathVariable Integer goodsId) {
        return skin86DataService.getTodayPrice(platform, goodsId);
    }

    /**
     * 搜索商品
     * 
     * @param platform 平台名称
     * @param keyword 关键词
     * @param maxResults 最大结果数
     * @return 商品列表
     */
    @GetMapping("/search")
    public CompletableFuture<List<SkinGoodsInfo>> searchGoods(
            @RequestParam(defaultValue = "yp") String platform,
            @RequestParam String keyword,
            @RequestParam(defaultValue = "10") int maxResults) {
        return skin86DataService.searchGoods(platform, keyword, maxResults);
    }

    /**
     * 获取支持的平台列表
     * 
     * @return 平台列表
     */
    @GetMapping("/platforms")
    public List<String> getSupportedPlatforms() {
        return skin86DataService.getSupportedPlatforms();
    }

    /**
     * 检查平台是否支持
     * 
     * @param platform 平台名称
     * @return 支持状态
     */
    @GetMapping("/platforms/{platform}/check")
    public boolean checkPlatformSupport(@PathVariable String platform) {
        return skin86DataService.isPlatformSupported(platform);
    }
}