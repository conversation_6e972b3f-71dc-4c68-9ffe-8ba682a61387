# Skin86 Spring Boot Starter - 快速使用指南

## 🎉 项目已完成！

恭喜！您的 Skin86 Spring Boot Starter 已经成功创建并可以使用了。

## 📁 项目结构

```
skin86-starter-project/
├── pom.xml                                    # 父项目POM
├── README.md                                  # 详细文档
├── skin86-spring-boot-starter/                # Starter主模块
│   ├── src/main/java/com/skin86/starter/
│   │   ├── autoconfigure/                     # 自动配置
│   │   │   ├── Skin86AutoConfiguration.java  # 主要自动配置类
│   │   │   └── Skin86Properties.java         # 配置属性类
│   │   ├── client/                           # API客户端
│   │   │   └── Skin86ApiClient.java          # 核心API客户端
│   │   ├── dto/                              # 数据传输对象
│   │   │   ├── request/                      # 请求DTO
│   │   │   ├── response/                     # 响应DTO
│   │   │   ├── ApiResponse.java              # 通用响应
│   │   │   ├── SkinGoodsInfo.java            # 商品信息DTO
│   │   │   └── SkinPriceInfo.java            # 价格信息DTO
│   │   └── service/                          # 服务层
│   │       ├── Skin86DataService.java        # 服务接口
│   │       └── Skin86DataServiceImpl.java    # 服务实现
│   └── src/main/resources/
│       └── META-INF/                         # 自动配置元数据
└── skin86-example/                           # 示例应用
    ├── src/main/java/com/skin86/example/
    │   ├── ExampleApplication.java           # 启动类
    │   ├── controller/                       # 控制器
    │   │   └── ExampleController.java        # 示例控制器
    │   └── service/                          # 业务服务
    │       └── ExampleBusinessService.java   # 示例业务服务
    └── src/main/resources/
        └── application.yml                   # 配置文件
```

## 🚀 如何使用

### 1. 在其他项目中使用此 Starter

在您的新项目的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.skin86</groupId>
    <artifactId>skin86-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 配置 API 凭证

在 `application.yml` 中添加配置：

```yaml
skin86:
  api:
    app-id: your-app-id      # 替换为您的实际API ID
    app-secret: your-secret  # 替换为您的实际API密钥
```

### 3. 注入服务并使用

```java
@RestController
public class MyController {
    
    @Autowired
    private Skin86DataService skin86DataService;
    
    @GetMapping("/goods")
    public CompletableFuture<List<SkinGoodsInfo>> getGoods() {
        return skin86DataService.getGoodsList("yp", 1, 20);
    }
}
```

## 🔧 运行示例应用

1. **确保已安装到本地仓库**（已完成）：
   ```bash
   mvn -f skin86-starter-project/pom.xml clean install
   ```

2. **运行示例应用**：
   ```bash
   mvn -f skin86-starter-project/skin86-example/pom.xml spring-boot:run
   ```

3. **访问示例API**：
   - 获取商品列表：`http://localhost:8080/api/skin86/goods`
   - 获取价格信息：`http://localhost:8080/api/skin86/price/123`
   - 搜索商品：`http://localhost:8080/api/skin86/search?keyword=AK-47`
   - 支持的平台：`http://localhost:8080/api/skin86/platforms`

## ✨ 主要特性

1. **开箱即用**：零配置启动，只需提供 API 凭证
2. **高性能**：基于 OkHttp 和异步编程
3. **多平台支持**：支持 YP、BUFF、Steam、IGXE 等平台
4. **简洁API**：直观的编程接口
5. **类型安全**：基于 Java 21 Record 类
6. **智能提示**：完整的配置元数据

## 🎯 核心API

### Skin86DataService 主要方法：

- `getGoodsList(platform, page, pageSize)` - 获取商品列表
- `getAllGoods(platform)` - 获取所有商品（自动分页）
- `getTodayPrice(platform, goodsId)` - 获取今日价格
- `getBatchPrices(platform, goodsIds, date)` - 批量获取价格
- `searchGoods(platform, keyword, maxResults)` - 搜索商品
- `getSupportedPlatforms()` - 获取支持的平台列表

## 📖 下一步

1. 查看 `README.md` 获取详细使用文档
2. 参考 `skin86-example` 项目了解具体用法
3. 根据需要调整配置参数
4. 在您的项目中集成并使用

## 🎊 恭喜！

您已经成功创建了一个功能完整的 Spring Boot Starter！现在任何 Spring Boot 项目都可以通过简单的依赖添加来使用您的 Skin86 数据采集功能。