# Skin86 Spring Boot Starter

一个高性能的 CS2 饰品数据采集 Spring Boot Starter，提供简洁易用的 API 接口来获取 Skin86 平台的饰品和价格数据。

## 🚀 特性

- **🔧 开箱即用**：零配置启动，只需提供 API 凭证即可使用
- **⚡ 高性能**：基于 OkHttp 和异步编程，支持高并发数据获取
- **🛡️ 可靠稳定**：内置重试机制、限流控制和错误处理
- **📊 多平台支持**：支持悠悠有品(YP)、BUFF、Steam、IGXE 等平台
- **🎯 简洁API**：提供直观的 API 接口，屏蔽底层实现复杂度
- **🔒 类型安全**：基于 Java 21 Record 类，提供完整的类型安全
- **📖 智能提示**：完整的配置元数据，IDE 中享受智能提示

## 📋 系统要求

- Java 21+
- Spring Boot 3.2.1+
- Maven 3.9.6+

## 🛠️ 快速开始

### 1. 添加依赖

在您的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.skin86</groupId>
    <artifactId>skin86-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 配置 API 凭证

在 `application.yml` 中添加您的 API 凭证：

```yaml
skin86:
  api:
    app-id: your-app-id
    app-secret: your-app-secret
    base-url: https://csdata-api.skin86.com  # 可选，使用默认值
    timeout: 30s                             # 可选，默认30秒
```

或使用环境变量：

```bash
export SKIN86_APP_ID=your-app-id
export SKIN86_APP_SECRET=your-app-secret
```

### 3. 注入服务并使用

```java
@RestController
public class SkinController {
    
    @Autowired
    private Skin86DataService skin86DataService;
    
    @GetMapping("/goods")
    public CompletableFuture<List<SkinGoodsInfo>> getGoods() {
        return skin86DataService.getGoodsList("yp", 1, 20);
    }
    
    @GetMapping("/price/{goodsId}")
    public CompletableFuture<SkinPriceInfo> getPrice(@PathVariable Integer goodsId) {
        return skin86DataService.getTodayPrice("yp", goodsId);
    }
}
```

## 📖 详细使用指南

### 核心服务：Skin86DataService

`Skin86DataService` 是 starter 的核心接口，提供以下主要功能：

#### 获取商品列表

```java
// 获取指定页的商品
CompletableFuture<List<SkinGoodsInfo>> getGoodsList(String platform, int page, int pageSize);

// 获取所有商品（自动分页）
CompletableFuture<List<SkinGoodsInfo>> getAllGoods(String platform);

// 获取所有商品（限制最大页数）
CompletableFuture<List<SkinGoodsInfo>> getAllGoods(String platform, int maxPages);
```

#### 获取价格信息

```java
// 获取单个商品今日价格
CompletableFuture<SkinPriceInfo> getTodayPrice(String platform, Integer goodsId);

// 批量获取商品价格
CompletableFuture<List<SkinPriceInfo>> getBatchPrices(String platform, List<Integer> goodsIds, LocalDate date);

// 批量获取今日价格
CompletableFuture<List<SkinPriceInfo>> getTodayBatchPrices(String platform, List<Integer> goodsIds);
```

#### 搜索功能

```java
// 搜索商品
CompletableFuture<List<SkinGoodsInfo>> searchGoods(String platform, String keyword, int maxResults);
```

#### 平台管理

```java
// 获取支持的平台列表
List<String> getSupportedPlatforms();

// 检查平台是否支持
boolean isPlatformSupported(String platform);
```

### 支持的平台

| 平台代码 | 平台名称 | 描述 |
|---------|---------|------|
| `yp` | 悠悠有品 | 国内知名CS2饰品交易平台 |
| `buff` | BUFF | 网易BUFF饰品交易平台 |
| `steam` | Steam | Steam官方市场 |
| `igxe` | IGXE | IGXE饰品交易平台 |

### 数据模型

#### SkinGoodsInfo（商品信息）

```java
public record SkinGoodsInfo(
    Integer goodsId,                    // 商品ID
    String platformId,                  // 平台ID
    String marketName,                  // 市场名称
    String marketHashName,              // 市场哈希名称
    BigDecimal sellMinPrice,            // 最低售价
    Integer sellMaxNum,                 // 最大售出数量
    BigDecimal sellValuation,           // 售出估值
    BigDecimal buyMaxPrice,             // 最高收购价
    Integer buyMaxNum,                  // 最大收购数量
    BigDecimal priceAlterPercentage7d,  // 7天价格变化百分比
    BigDecimal priceAlterValue7d,       // 7天价格变化值
    String categoryGroupName,           // 分类组名称
    String rarityColor,                 // 稀有度颜色
    String iconUrl,                     // 图标URL
    Boolean isFollow,                   // 是否关注
    String redirectUrl,                 // 跳转URL
    String exterior,                    // 外观磨损
    String rarity,                      // 稀有度
    String platform                     // 平台
) {
    // 便利方法
    public String getDisplayName();     // 获取显示名称
    public boolean hasValidPrice();     // 是否有有效价格
    public BigDecimal getBestPrice();   // 获取最优价格
}
```

#### SkinPriceInfo（价格信息）

```java
public record SkinPriceInfo(
    Integer goodsId,                    // 商品ID
    String platform,                    // 平台
    LocalDate priceDate,                // 价格日期
    BigDecimal sellMinPrice,            // 最低售价
    Integer sellMaxNum,                 // 最大售出数量
    BigDecimal buyMaxPrice,             // 最高收购价
    Integer buyMaxNum,                  // 最大收购数量
    String marketName                   // 市场名称
) {
    // 便利方法
    public boolean hasValidPrice();                                    // 是否有有效价格
    public BigDecimal getBestPrice();                                  // 获取最优价格
    public BigDecimal calculatePriceDifference(SkinPriceInfo other);   // 计算价格差异
    public BigDecimal calculatePriceDifferencePercentage(SkinPriceInfo other); // 计算价格差异百分比
}
```

## ⚙️ 配置选项

### 完整配置示例

```yaml
skin86:
  # API配置
  api:
    app-id: your-app-id                              # 必填：API应用ID
    app-secret: your-app-secret                      # 必填：API应用密钥
    base-url: https://csdata-api.skin86.com         # 可选：API基础URL
    timeout: 30s                                     # 可选：请求超时时间
    
  # 性能配置
  performance:
    max-concurrency: 10                              # 可选：最大并发数 (1-50)
    delay-ms: 200ms                                  # 可选：请求延迟时间
    retry-times: 3                                   # 可选：重试次数 (1-10)
    retry-delay: 2s                                  # 可选：重试延迟时间
    batch-size: 500                                  # 可选：批次大小 (10-2000)
    
  # 平台配置
  platform:
    supported: [yp, buff, steam, igxe]               # 可选：支持的平台列表
    default-platforms: [yp]                         # 可选：默认平台列表
```

### 配置说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `skin86.api.app-id` | String | - | **必填**：API应用ID |
| `skin86.api.app-secret` | String | - | **必填**：API应用密钥 |
| `skin86.api.base-url` | String | `https://csdata-api.skin86.com` | API基础URL |
| `skin86.api.timeout` | Duration | `30s` | API请求超时时间 |
| `skin86.performance.max-concurrency` | Integer | `10` | 最大并发数 (1-50) |
| `skin86.performance.delay-ms` | Duration | `200ms` | 请求间隔延迟 |
| `skin86.performance.retry-times` | Integer | `3` | 失败重试次数 (1-10) |
| `skin86.performance.retry-delay` | Duration | `2s` | 重试延迟时间 |
| `skin86.performance.batch-size` | Integer | `500` | 批处理大小 (10-2000) |

## 📝 使用示例

### 基础示例

```java
@Service
public class SkinService {
    
    @Autowired
    private Skin86DataService skin86DataService;
    
    // 获取热门商品
    public CompletableFuture<List<SkinGoodsInfo>> getPopularGoods() {
        return skin86DataService.getGoodsList("yp", 1, 20)
            .thenApply(goods -> goods.stream()
                .filter(SkinGoodsInfo::hasValidPrice)
                .filter(good -> good.getBestPrice().compareTo(BigDecimal.valueOf(100)) > 0)
                .toList());
    }
    
    // 搜索AK47
    public CompletableFuture<List<SkinGoodsInfo>> searchAK47() {
        return skin86DataService.searchGoods("yp", "AK-47", 50);
    }
}
```

### 高级示例：跨平台价格比较

```java
@Service
public class PriceAnalysisService {
    
    @Autowired
    private Skin86DataService skin86DataService;
    
    public CompletableFuture<PriceComparisonResult> comparePrices(Integer goodsId) {
        List<String> platforms = List.of("yp", "buff", "steam");
        
        // 并行获取各平台价格
        List<CompletableFuture<PlatformPrice>> futures = platforms.stream()
            .map(platform -> skin86DataService.getTodayPrice(platform, goodsId)
                .thenApply(price -> new PlatformPrice(platform, price)))
            .toList();
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenApply(v -> {
                List<PlatformPrice> prices = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(pp -> pp.price() != null && pp.price().hasValidPrice())
                    .toList();
                
                return analyzePrices(goodsId, prices);
            });
    }
    
    private PriceComparisonResult analyzePrices(Integer goodsId, List<PlatformPrice> prices) {
        PlatformPrice minPrice = prices.stream()
            .min(Comparator.comparing(p -> p.price().getBestPrice()))
            .orElse(null);
            
        PlatformPrice maxPrice = prices.stream()
            .max(Comparator.comparing(p -> p.price().getBestPrice()))
            .orElse(null);
            
        return new PriceComparisonResult(goodsId, minPrice, maxPrice, prices);
    }
    
    public record PlatformPrice(String platform, SkinPriceInfo price) {}
    public record PriceComparisonResult(Integer goodsId, PlatformPrice minPrice, PlatformPrice maxPrice, List<PlatformPrice> allPrices) {}
}
```

### 批量数据处理示例

```java
@Service
public class DataAnalysisService {
    
    @Autowired
    private Skin86DataService skin86DataService;
    
    // 批量获取价格并分析
    public CompletableFuture<MarketAnalysisResult> analyzeMarket(String platform) {
        return skin86DataService.getAllGoods(platform, 5) // 获取前5页商品
            .thenCompose(goods -> {
                List<Integer> goodsIds = goods.stream()
                    .map(SkinGoodsInfo::goodsId)
                    .toList();
                
                return skin86DataService.getTodayBatchPrices(platform, goodsIds)
                    .thenApply(prices -> analyzeMarketData(goods, prices));
            });
    }
    
    private MarketAnalysisResult analyzeMarketData(List<SkinGoodsInfo> goods, List<SkinPriceInfo> prices) {
        BigDecimal avgPrice = prices.stream()
            .filter(SkinPriceInfo::hasValidPrice)
            .map(SkinPriceInfo::getBestPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .divide(BigDecimal.valueOf(prices.size()), 2, RoundingMode.HALF_UP);
            
        long validPriceCount = prices.stream()
            .mapToLong(p -> p.hasValidPrice() ? 1 : 0)
            .sum();
            
        return new MarketAnalysisResult(goods.size(), validPriceCount, avgPrice);
    }
    
    public record MarketAnalysisResult(int totalGoods, long validPrices, BigDecimal averagePrice) {}
}
```

## 🔧 自定义配置

### 自定义 HTTP 客户端

如果您需要自定义 HTTP 客户端配置，可以提供自己的 Bean：

```java
@Configuration
public class CustomHttpConfig {
    
    @Bean
    @Primary
    public OkHttpClient customSkin86OkHttpClient() {
        return new OkHttpClient.Builder()
            .connectTimeout(Duration.ofSeconds(15))
            .readTimeout(Duration.ofSeconds(30))
            .writeTimeout(Duration.ofSeconds(15))
            // 其他自定义配置
            .build();
    }
}
```

### 自定义 ObjectMapper

```java
@Configuration
public class CustomJsonConfig {
    
    @Bean
    @Primary
    public ObjectMapper customSkin86ObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 其他自定义配置
        return mapper;
    }
}
```

## 🐛 错误处理

Starter 内置了完善的错误处理机制：

### 常见错误及解决方案

1. **API 认证失败**
   ```
   错误：API调用失败: 401 - Unauthorized
   解决：检查 app-id 和 app-secret 是否正确
   ```

2. **请求限流**
   ```
   错误：API调用失败: 429 - Too Many Requests
   解决：增加 delay-ms 配置值，降低请求频率
   ```

3. **网络超时**
   ```
   错误：网络连接失败: timeout
   解决：增加 timeout 配置值或检查网络连接
   ```

### 自定义错误处理

```java
@Component
public class Skin86ErrorHandler {
    
    @EventListener
    public void handleSkin86Error(Skin86ErrorEvent event) {
        logger.error("Skin86 API调用失败: {}", event.getMessage());
        // 自定义错误处理逻辑
    }
}
```

## 📊 性能优化建议

1. **合理设置并发数**：根据服务器性能和API限制调整 `max-concurrency`
2. **适当的延迟时间**：设置合适的 `delay-ms` 避免触发限流
3. **批量处理**：优先使用批量API（`getBatchPrices`）而非单个请求
4. **异步处理**：充分利用 `CompletableFuture` 进行异步处理
5. **缓存策略**：对不经常变化的数据（如商品列表）实施缓存

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源。

## 🤝 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

## 📞 支持

如有问题或建议，请通过以下方式联系我们：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/skin86/skin86-spring-boot-starter/issues)
- 📖 Wiki: [项目Wiki](https://github.com/skin86/skin86-spring-boot-starter/wiki)

## 🔄 更新日志

### v1.0.0 (2025-08-26)
- 🎉 首次发布
- ✨ 支持商品列表获取
- ✨ 支持价格信息查询
- ✨ 支持商品搜索功能
- ✨ 支持多平台数据获取
- ⚡ 高性能异步处理
- 🛡️ 完善的错误处理和重试机制