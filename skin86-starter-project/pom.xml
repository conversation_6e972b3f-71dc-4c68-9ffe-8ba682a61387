<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.skin86</groupId>
    <artifactId>skin86-spring-boot-starter-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>Skin86 Spring Boot Starter Parent</name>
    <description>CS2 饰品数据采集 Spring Boot Starter 父项目</description>
    <url>https://github.com/skin86/skin86-spring-boot-starter</url>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>21</java.version>

        <!-- Spring Boot版本 -->
        <spring-boot.version>3.2.1</spring-boot.version>
        
        <!-- 第三方库版本 -->
        <okhttp.version>4.12.0</okhttp.version>
        <mysql.version>8.0.33</mysql.version>
        <micrometer.version>1.12.1</micrometer.version>
    </properties>

    <modules>
        <module>skin86-spring-boot-starter</module>
        <module>skin86-example</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 第三方库版本管理 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>21</source>
                        <target>21</target>
                        <compilerArgs>
                            <arg>--enable-preview</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <licenses>
        <license>
            <name>MIT License</name>
            <url>http://www.opensource.org/licenses/mit-license.php</url>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>Skin86 Team</name>
            <email><EMAIL></email>
            <organization>Skin86</organization>
        </developer>
    </developers>

    <scm>
        <connection>scm:git:git://github.com/skin86/skin86-spring-boot-starter.git</connection>
        <developerConnection>scm:git:ssh://github.com:skin86/skin86-spring-boot-starter.git</developerConnection>
        <url>https://github.com/skin86/skin86-spring-boot-starter/tree/main</url>
    </scm>
</project>