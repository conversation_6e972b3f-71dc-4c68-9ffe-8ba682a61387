package com.ruoyi.project.priceAlert.service;

import com.ruoyi.project.priceAlert.domain.VimCostPriceAnomalyDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 成本价异常商品服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface ICostPriceAnomalyService {

    /**
     * 查询成本价异常商品列表
     *
     * @param itemName 商品名称（可选）
     * @param boxName 盲盒名称（可选）
     * @param itemTag 商品标签（可选）
     * @param saleStatus 商品状态（可选）
     * @param minCost 最小成本价（可选）
     * @param maxCost 最大成本价（可选）
     * @param minRecycle 最小回收价（可选）
     * @param maxRecycle 最大回收价（可选）
     * @param minDifference 最小价格差异（可选）
     * @param maxDifference 最大价格差异（可选）
     * @param minPercentage 最小差异百分比（可选）
     * @param maxPercentage 最大差异百分比（可选）
     * @param stockStatus 库存状态（可选）
     * @param severityLevel 异常严重程度（可选）
     * @param sortField 排序字段（可选）
     * @return 成本价异常商品列表
     */
    List<VimCostPriceAnomalyDTO> selectCostPriceAnomalyList(String itemName, String boxName, String itemTag,
            Integer saleStatus, BigDecimal minCost, BigDecimal maxCost,
            BigDecimal minRecycle, BigDecimal maxRecycle,
            BigDecimal minDifference, BigDecimal maxDifference,
            BigDecimal minPercentage, BigDecimal maxPercentage,
            Integer stockStatus, Integer severityLevel, String sortField);

    /**
     * 统计成本价异常商品数量
     *
     * @return 异常商品数量
     */
    int countCostPriceAnomalyItems();

    /**
     * 获取成本价异常商品统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getCostPriceAnomalyStatistics();

    /**
     * 根据异常严重程度分组统计
     *
     * @return 分组统计结果
     */
    Map<String, Object> getAnomalyStatisticsBySeverity();

    /**
     * 获取异常商品趋势数据
     *
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getAnomalyTrendData(int days);

    /**
     * 获取异常商品分布数据（按标签分组）
     *
     * @return 分布数据
     */
    List<Map<String, Object>> getAnomalyDistributionByTag();

    /**
     * 检测新增的异常商品
     *
     * @param lastCheckTime 上次检查时间戳
     * @return 新增异常商品列表
     */
    List<VimCostPriceAnomalyDTO> detectNewAnomalyItems(Long lastCheckTime);

    /**
     * 计算异常严重程度
     *
     * @param priceDifference 价格差异
     * @param differencePercentage 差异百分比
     * @return 严重程度等级（1=轻微，2=中等，3=严重）
     */
    int calculateSeverityLevel(BigDecimal priceDifference, BigDecimal differencePercentage);

    /**
     * 获取异常商品详情（包含参考价格对比）
     *
     * @param itemId 商品ID
     * @return 详细信息
     */
    Map<String, Object> getAnomalyItemDetail(Long itemId);

    /**
     * 批量检查商品异常状态
     *
     * @param itemIds 商品ID列表
     * @return 检查结果
     */
    Map<String, Object> batchCheckAnomalyStatus(List<Long> itemIds);

    /**
     * 测试从库连接
     *
     * @return 测试数据
     */
    List<Map<String, Object>> testSlaveConnection();
}
