package com.ruoyi.project.priceAlert.domain;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

/**
 * 成本价异常商品数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@ApiModel(value = "VimCostPriceAnomalyDTO", description = "成本价异常商品数据传输对象")
@Data
public class VimCostPriceAnomalyDTO {
    
    /** 商品ID */
    @ApiModelProperty(value = "商品ID", example = "1")
    private Long itemId;
    
    /** 商品名称 */
    @ApiModelProperty(value = "商品名称", example = "AK-47 | 红线")
    @Excel(name = "商品名称")
    private String itemName;
    
    /** 商品英文名 */
    @ApiModelProperty(value = "商品英文名", example = "AK-47 | Redline")
    private String itemHashname;
    
    /** 商品标签 */
    @ApiModelProperty(value = "商品标签", example = "步枪")
    @Excel(name = "商品标签")
    private String itemTag;
    
    /** 商品图片 */
    @ApiModelProperty(value = "商品图片", example = "http://example.com/images/item.jpg")
    private String itemImage;
    
    /** 成本价 */
    @ApiModelProperty(value = "成本价", example = "150.00")
    @Excel(name = "成本价")
    private BigDecimal priceCost;
    
    /** 回收价 */
    @ApiModelProperty(value = "回收价", example = "120.00")
    @Excel(name = "回收价")
    private BigDecimal priceRecycle;
    
    /** 展示价 */
    @ApiModelProperty(value = "展示价", example = "180.00")
    @Excel(name = "展示价")
    private BigDecimal priceShow;
    
    /** 购买价 */
    @ApiModelProperty(value = "购买价", example = "200.00")
    @Excel(name = "购买价")
    private BigDecimal priceBuy;
    
    /** 价格差异（成本价-回收价） */
    @ApiModelProperty(value = "价格差异", example = "30.00")
    @Excel(name = "价格差异")
    private BigDecimal priceDifference;
    
    /** 差异百分比 */
    @ApiModelProperty(value = "差异百分比", example = "25.00")
    @Excel(name = "差异百分比(%)")
    private BigDecimal differencePercentage;
    
    /** 盲盒ID列表（逗号分隔） */
    @ApiModelProperty(value = "盲盒ID列表", example = "1,2,3")
    private String boxIds;

    /** 盲盒名称列表（逗号分隔） */
    @ApiModelProperty(value = "盲盒名称列表", example = "神秘盲盒,高级盲盒,特殊盲盒")
    @Excel(name = "所属盲盒")
    private String boxNames;

    /** 盲盒类型列表（逗号分隔） */
    @ApiModelProperty(value = "盲盒类型列表", example = "普通盲盒,高级盲盒,特殊盲盒")
    @Excel(name = "盲盒类型")
    private String boxTypes;

    /** 盲盒价格列表（逗号分隔） */
    @ApiModelProperty(value = "盲盒价格列表", example = "50.00,100.00,200.00")
    @Excel(name = "盲盒价格")
    private String boxPrices;

    /** 盲盒数量 */
    @ApiModelProperty(value = "所属盲盒数量", example = "3")
    @Excel(name = "盲盒数量")
    private Integer boxCount;
    
    /** 商品是否上架 */
    @ApiModelProperty(value = "商品是否上架", example = "1", notes = "0=下架，1=上架")
    @Excel(name = "是否上架", readConverterExp = "0=下架,1=上架")
    private Long itemSale;
    
    /** 商品库存 */
    @ApiModelProperty(value = "商品库存", example = "100")
    @Excel(name = "库存")
    private Long itemStock;

    /** 商品hashname（用于查询参考价格） */
    @ApiModelProperty(value = "商品hashname", example = "AK-47 | Redline (Field-Tested)")
    private String hashname;

    /** 参考价格（从skin_goods表获取） */
    @ApiModelProperty(value = "参考价格", example = "140.00")
    @Excel(name = "参考价格")
    private BigDecimal referencePrice;

    /** 严重程度等级 */
    @ApiModelProperty(value = "严重程度等级", example = "2", notes = "1=轻微，2=中等，3=严重")
    @Excel(name = "严重程度", readConverterExp = "1=轻微,2=中等,3=严重")
    private Integer severityLevel;

    /** 严重程度文本 */
    @ApiModelProperty(value = "严重程度文本", example = "中等")
    private String severityText;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private Long updateTime;

    /** 盲盒名称（单个，用于显示） */
    @ApiModelProperty(value = "盲盒名称", example = "神秘盲盒")
    private String boxName;

    /** 盲盒ID（单个，用于关联） */
    @ApiModelProperty(value = "盲盒ID", example = "1")
    private Long boxId;

}
