package com.ruoyi.project.priceAlert.mapper;

import com.ruoyi.project.priceAlert.domain.VimCostPriceAnomalyDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 成本价异常商品Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Mapper
public interface CostPriceAnomalyMapper {

    /**
     * 查询成本价异常商品列表
     * 
     * @param params 查询参数
     * @return 成本价异常商品列表
     */
    List<VimCostPriceAnomalyDTO> selectCostPriceAnomalyList(Map<String, Object> params);

    /**
     * 统计成本价异常商品数量
     * 
     * @return 异常商品数量
     */
    int countCostPriceAnomalyItems();

    /**
     * 统计总商品数量
     * 
     * @return 总商品数量
     */
    int countTotalItems();

    /**
     * 根据ID查询异常商品详情
     * 
     * @param itemId 商品ID
     * @return 异常商品详情
     */
    VimCostPriceAnomalyDTO getAnomalyItemById(@Param("itemId") Long itemId);

    /**
     * 按严重程度统计异常商品
     * 
     * @return 严重程度统计
     */
    List<Map<String, Object>> getAnomalyStatisticsBySeverity();

    /**
     * 获取价格范围统计
     * 
     * @return 价格范围统计
     */
    Map<String, Object> getPriceRangeStatistics();

    /**
     * 获取异常商品趋势数据
     * 
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getAnomalyTrendData(@Param("days") int days);

    /**
     * 获取异常商品标签分布
     * 
     * @return 标签分布数据
     */
    List<Map<String, Object>> getAnomalyDistributionByTag();

    /**
     * 检测新增的异常商品
     * 
     * @param params 查询参数
     * @return 新增异常商品列表
     */
    List<VimCostPriceAnomalyDTO> detectNewAnomalyItems(Map<String, Object> params);

    /**
     * 批量检查商品异常状态
     * 
     * @param itemIds 商品ID列表
     * @return 异常状态列表
     */
    List<Map<String, Object>> batchCheckAnomalyStatus(@Param("itemIds") List<Long> itemIds);

    /**
     * 获取异常商品的盲盒关联信息
     * 
     * @param itemId 商品ID
     * @return 盲盒关联信息
     */
    List<Map<String, Object>> getAnomalyItemBoxRelations(@Param("itemId") Long itemId);

    /**
     * 统计异常商品的库存分布
     * 
     * @return 库存分布统计
     */
    List<Map<String, Object>> getAnomalyStockDistribution();

    /**
     * 获取异常商品的销售状态分布
     * 
     * @return 销售状态分布
     */
    List<Map<String, Object>> getAnomalySaleStatusDistribution();
}
