package com.ruoyi.project.priceAlert.service;

import java.util.List;
import java.util.Map;

/**
 * 外部数据同步服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IExternalDataSyncService {

    /**
     * 从Skin86 API获取最新商品数据
     * 
     * @param platform 平台名称（如：yp）
     * @param startPage 起始页码
     * @param maxPages 最大页数
     * @return 同步结果
     */
    Map<String, Object> fetchDataFromSkin86(String platform, Integer startPage, Integer maxPages);

    /**
     * 检查Skin86服务状态
     * 
     * @return 服务状态信息
     */
    Map<String, Object> checkSkin86ServiceStatus();

    /**
     * 获取Skin86任务状态
     * 
     * @return 任务状态信息
     */
    Map<String, Object> getSkin86TaskStatus();

    /**
     * 停止当前Skin86任务
     * 
     * @return 操作结果
     */
    Map<String, Object> stopSkin86Task();

    /**
     * 从外部API更新商品参考价格
     * 
     * @param hashNames 商品hashName列表
     * @return 更新结果
     */
    Map<String, Object> updateReferencePrices(List<String> hashNames);

    /**
     * 执行完整的外部数据同步流程
     * 
     * @return 同步结果
     */
    Map<String, Object> executeFullExternalSync();

    /**
     * 验证外部API连接
     * 
     * @return 连接测试结果
     */
    Map<String, Object> testExternalApiConnection();

    /**
     * 获取外部数据源统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getExternalDataStatistics();

    /**
     * 同步指定商品的最新价格
     * 
     * @param hashName 商品hashName
     * @return 同步结果
     */
    Map<String, Object> syncSingleItemPrice(String hashName);

    /**
     * 批量同步商品价格
     * 
     * @param hashNames 商品hashName列表
     * @param batchSize 批次大小
     * @return 同步结果
     */
    Map<String, Object> batchSyncItemPrices(List<String> hashNames, Integer batchSize);
}
