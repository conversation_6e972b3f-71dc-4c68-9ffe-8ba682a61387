package com.ruoyi.project.priceAlert.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.framework.web.domain.BaseEntity;

import java.util.Date;
import java.util.Map;

/**
 * 通知消息对象 vim_notification_log
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "NotificationMessage", description = "通知消息对象")
public class NotificationMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    /** 通知类型：data_sync,price_anomaly,system_alert */
    @ApiModelProperty(value = "通知类型", example = "data_sync", required = true, 
                     allowableValues = "data_sync,price_anomaly,system_alert")
    private String notificationType;

    /** 通知标题 */
    @ApiModelProperty(value = "通知标题", example = "数据同步完成", required = true)
    private String title;

    /** 通知内容 */
    @ApiModelProperty(value = "通知内容", example = "发现10个成本价异常商品")
    private String content;

    /** 目标用户ID列表（JSON格式） */
    @ApiModelProperty(value = "目标用户ID列表", example = "[1,2,3]")
    private String targetUsers;

    /** 发送状态：1=成功，2=失败 */
    @ApiModelProperty(value = "发送状态", example = "1", allowableValues = "1,2", notes = "1=成功，2=失败")
    private Integer sendStatus;

    /** 阅读状态：0=未读，1=已读 */
    @ApiModelProperty(value = "阅读状态", example = "0", allowableValues = "0,1", notes = "0=未读，1=已读")
    private Integer readStatus;

    /** 额外数据（JSON格式） */
    @ApiModelProperty(value = "额外数据", example = "{\"anomalyCount\":10,\"totalItems\":1000}")
    private String extraData;

    /** 创建时间戳（秒） */
    @ApiModelProperty(value = "创建时间戳", example = "1693123200")
    private Long createTimeStamp;

    // ========== 辅助字段 ==========

    /** 创建时间（格式化显示） */
    @ApiModelProperty(value = "创建时间", example = "2025-08-25 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeFormatted;

    /** 通知类型文本 */
    @ApiModelProperty(value = "通知类型文本", example = "数据同步")
    private String notificationTypeText;

    /** 发送状态文本 */
    @ApiModelProperty(value = "发送状态文本", example = "成功")
    private String sendStatusText;

    /** 阅读状态文本 */
    @ApiModelProperty(value = "阅读状态文本", example = "未读")
    private String readStatusText;

    /** 额外数据对象 */
    @ApiModelProperty(value = "额外数据对象")
    private Map<String, Object> extraDataMap;

    // ========== 枚举定义 ==========

    /**
     * 通知类型枚举
     */
    public enum Type {
        DATA_SYNC("data_sync", "数据同步"),
        PRICE_ANOMALY("price_anomaly", "价格异常"),
        SYSTEM_ALERT("system_alert", "系统提醒");

        private final String code;
        private final String description;

        Type(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 发送状态枚举
     */
    public enum SendStatus {
        SUCCESS(1, "成功"),
        FAILED(2, "失败");

        private final Integer code;
        private final String description;

        SendStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 阅读状态枚举
     */
    public enum ReadStatus {
        UNREAD(0, "未读"),
        READ(1, "已读");

        private final Integer code;
        private final String description;

        ReadStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    // ========== 辅助方法 ==========

    /**
     * 获取通知类型文本
     */
    public String getNotificationTypeText() {
        if (notificationType == null) {
            return "未知";
        }
        for (Type type : Type.values()) {
            if (type.getCode().equals(notificationType)) {
                return type.getDescription();
            }
        }
        return "未知";
    }

    /**
     * 获取发送状态文本
     */
    public String getSendStatusText() {
        if (sendStatus == null) {
            return "未知";
        }
        return sendStatus == 1 ? "成功" : "失败";
    }

    /**
     * 获取阅读状态文本
     */
    public String getReadStatusText() {
        if (readStatus == null) {
            return "未知";
        }
        return readStatus == 0 ? "未读" : "已读";
    }

    /**
     * 获取格式化的创建时间
     */
    public Date getCreateTimeFormatted() {
        if (createTimeStamp == null) {
            return null;
        }
        return new Date(createTimeStamp * 1000);
    }

    /**
     * 判断是否未读
     */
    public boolean isUnread() {
        return readStatus != null && readStatus == 0;
    }

    /**
     * 判断是否发送成功
     */
    public boolean isSendSuccess() {
        return sendStatus != null && sendStatus == 1;
    }
}
