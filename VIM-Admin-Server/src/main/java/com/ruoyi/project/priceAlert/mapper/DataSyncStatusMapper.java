package com.ruoyi.project.priceAlert.mapper;

import com.ruoyi.framework.aspectj.lang.annotation.DataSource;
import com.ruoyi.project.priceAlert.domain.DataSyncStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import static com.ruoyi.framework.aspectj.lang.enums.DataSourceType.SLAVE;

/**
 * 数据同步状态Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Mapper
@DataSource(SLAVE)
public interface DataSyncStatusMapper {

    /**
     * 查询数据同步状态列表
     * 
     * @param dataSyncStatus 数据同步状态
     * @return 数据同步状态集合
     */
    List<DataSyncStatus> selectDataSyncStatusList(DataSyncStatus dataSyncStatus);

    /**
     * 根据同步类型查询数据同步状态
     * 
     * @param syncType 同步类型
     * @return 数据同步状态
     */
    DataSyncStatus selectDataSyncStatusBySyncType(@Param("syncType") String syncType);

    /**
     * 查询最新的数据同步状态
     * 
     * @return 数据同步状态列表
     */
    List<DataSyncStatus> selectLatestDataSyncStatus();

    /**
     * 新增数据同步状态
     * 
     * @param dataSyncStatus 数据同步状态
     * @return 结果
     */
    int insertDataSyncStatus(DataSyncStatus dataSyncStatus);

    /**
     * 修改数据同步状态
     * 
     * @param dataSyncStatus 数据同步状态
     * @return 结果
     */
    int updateDataSyncStatus(DataSyncStatus dataSyncStatus);

    /**
     * 根据同步类型更新数据同步状态
     * 
     * @param dataSyncStatus 数据同步状态
     * @return 结果
     */
    int updateDataSyncStatusBySyncType(DataSyncStatus dataSyncStatus);

    /**
     * 删除数据同步状态
     * 
     * @param id 数据同步状态主键
     * @return 结果
     */
    int deleteDataSyncStatusById(Long id);

    /**
     * 批量删除数据同步状态
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDataSyncStatusByIds(Long[] ids);

    /**
     * 获取最后的数据更新时间（从skin_goods表）
     * 
     * @return 最后更新时间戳
     */
    Long getLastDataUpdateFromSkinGoods();

    /**
     * 统计异常商品数量
     * 
     * @return 异常商品数量
     */
    int countAnomalyItems();

    /**
     * 统计总商品数量
     * 
     * @return 总商品数量
     */
    int countTotalItems();

    /**
     * 检查是否有新的数据更新
     * 
     * @param lastCheckTime 上次检查时间
     * @return 是否有新数据
     */
    boolean hasNewDataUpdate(@Param("lastCheckTime") Long lastCheckTime);

    /**
     * 清理过期的同步状态记录
     * 
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanExpiredSyncStatus(@Param("retentionDays") int retentionDays);
}
