package com.ruoyi.project.priceAlert.service;

import com.ruoyi.project.priceAlert.domain.DataSyncStatus;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 数据同步监控服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IDataSyncMonitorService {

    /**
     * 获取数据同步状态列表
     *
     * @return 同步状态列表
     */
    List<DataSyncStatus> getDataSyncStatusList();

    /**
     * 根据同步类型获取数据同步状态
     *
     * @param syncType 同步类型
     * @return 同步状态
     */
    DataSyncStatus getDataSyncStatusBySyncType(String syncType);

    /**
     * 检查数据新鲜度
     *
     * @return 检查结果
     */
    Map<String, Object> checkDataFreshness();

    /**
     * 手动触发数据同步
     *
     * @return 同步结果
     */
    Map<String, Object> triggerManualSync();

    /**
     * 异步执行数据同步
     *
     * @param syncType 同步类型
     * @return 异步结果
     */
    CompletableFuture<Map<String, Object>> executeAsyncSync(String syncType);

    /**
     * 更新数据同步状态
     *
     * @param syncType 同步类型
     * @param syncStatus 同步状态
     * @param anomalyCount 异常数量
     * @param totalItems 总商品数量
     * @param syncDuration 同步耗时
     * @param errorMessage 错误信息
     * @return 更新结果
     */
    boolean updateDataSyncStatus(String syncType, Integer syncStatus, Integer anomalyCount, 
                                Integer totalItems, Integer syncDuration, String errorMessage);

    /**
     * 获取最后的数据更新时间（从skin_goods表）
     *
     * @return 最后更新时间戳
     */
    Long getLastDataUpdateFromSkinGoods();

    /**
     * 检查是否有新的数据更新
     *
     * @param lastCheckTime 上次检查时间
     * @return 是否有新数据
     */
    boolean hasNewDataUpdate(Long lastCheckTime);

    /**
     * 统计异常商品数量
     *
     * @return 异常商品数量
     */
    int countAnomalyItems();

    /**
     * 统计总商品数量
     *
     * @return 总商品数量
     */
    int countTotalItems();

    /**
     * 获取同步状态概览
     *
     * @return 状态概览
     */
    Map<String, Object> getSyncStatusOverview();

    /**
     * 获取同步历史记录
     *
     * @param syncType 同步类型（可选）
     * @param limit 限制数量
     * @return 历史记录
     */
    List<DataSyncStatus> getSyncHistory(String syncType, int limit);

    /**
     * 清理过期的同步状态记录
     *
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanExpiredSyncStatus(int retentionDays);

    /**
     * 初始化数据同步状态
     *
     * @return 初始化结果
     */
    boolean initializeDataSyncStatus();

    /**
     * 获取同步性能统计
     *
     * @return 性能统计
     */
    Map<String, Object> getSyncPerformanceStatistics();

    /**
     * 检测同步异常
     *
     * @return 异常检测结果
     */
    Map<String, Object> detectSyncAnomalies();
}
