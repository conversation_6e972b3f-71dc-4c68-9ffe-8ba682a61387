package com.ruoyi.project.priceAlert.util;

import com.ruoyi.project.commoditySys.domain.VimItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 价格计算工具类
 * 
 * 专注于成本价异常商品的价格计算逻辑
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class PriceCalculationUtil {

    // 默认精度
    private static final int DEFAULT_SCALE = 2;
    private static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 计算价格差异
     * 
     * @param priceCost 成本价
     * @param priceRecycle 回收价
     * @return 价格差异
     */
    public static BigDecimal calculatePriceDifference(BigDecimal priceCost, BigDecimal priceRecycle) {
        if (priceCost == null || priceRecycle == null) {
            return BigDecimal.ZERO;
        }
        return priceCost.subtract(priceRecycle).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 计算价格差异百分比
     * 
     * @param priceCost 成本价
     * @param priceRecycle 回收价
     * @return 差异百分比
     */
    public static BigDecimal calculateDifferencePercentage(BigDecimal priceCost, BigDecimal priceRecycle) {
        if (priceCost == null || priceRecycle == null || priceRecycle.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal difference = priceCost.subtract(priceRecycle);
        return difference.divide(priceRecycle, 4, DEFAULT_ROUNDING_MODE)
                        .multiply(new BigDecimal("100"))
                        .setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 判断是否为成本价异常商品
     * 
     * @param priceCost 成本价
     * @param priceRecycle 回收价
     * @return 是否异常
     */
    public static boolean isCostPriceAnomaly(BigDecimal priceCost, BigDecimal priceRecycle) {
        if (priceCost == null || priceRecycle == null) {
            return false;
        }
        return priceCost.compareTo(priceRecycle) > 0;
    }

    /**
     * 计算严重程度等级
     * 
     * @param priceDifference 价格差异
     * @param differencePercentage 差异百分比
     * @return 严重程度等级（1=轻微，2=中等，3=严重）
     */
    public static int calculateSeverityLevel(BigDecimal priceDifference, BigDecimal differencePercentage) {
        if (priceDifference == null || differencePercentage == null) {
            return 1; // 默认轻微
        }

        // 严重程度判断逻辑
        if (differencePercentage.compareTo(new BigDecimal("50")) > 0 || 
            priceDifference.compareTo(new BigDecimal("100")) > 0) {
            return 3; // 严重
        } else if (differencePercentage.compareTo(new BigDecimal("20")) > 0 || 
                   priceDifference.compareTo(new BigDecimal("50")) > 0) {
            return 2; // 中等
        } else {
            return 1; // 轻微
        }
    }

    /**
     * 获取严重程度文本
     * 
     * @param severityLevel 严重程度等级
     * @return 严重程度文本
     */
    public static String getSeverityText(int severityLevel) {
        switch (severityLevel) {
            case 1: return "轻微";
            case 2: return "中等";
            case 3: return "严重";
            default: return "未知";
        }
    }

    /**
     * 计算商品的完整价格信息
     * 
     * @param item 商品信息
     * @return 价格计算结果
     */
    public static Map<String, Object> calculateItemPriceInfo(VimItem item) {
        Map<String, Object> result = new HashMap<>();
        
        if (item == null) {
            return result;
        }

        BigDecimal priceCost = item.getPriceCost();
        BigDecimal priceRecycle = item.getPriceRecycle();
        
        // 基础价格信息
        result.put("priceCost", priceCost);
        result.put("priceRecycle", priceRecycle);
        result.put("priceShow", item.getPriceShow());
        result.put("priceBuy", item.getPriceBuy());
        
        // 计算价格差异
        BigDecimal priceDifference = calculatePriceDifference(priceCost, priceRecycle);
        result.put("priceDifference", priceDifference);
        
        // 计算差异百分比
        BigDecimal differencePercentage = calculateDifferencePercentage(priceCost, priceRecycle);
        result.put("differencePercentage", differencePercentage);
        
        // 判断是否异常
        boolean isAnomaly = isCostPriceAnomaly(priceCost, priceRecycle);
        result.put("isAnomaly", isAnomaly);
        
        // 计算严重程度
        if (isAnomaly) {
            int severityLevel = calculateSeverityLevel(priceDifference, differencePercentage);
            result.put("severityLevel", severityLevel);
            result.put("severityText", getSeverityText(severityLevel));
        } else {
            result.put("severityLevel", 0);
            result.put("severityText", "正常");
        }
        
        return result;
    }

    /**
     * 计算价格调整建议
     * 
     * @param priceCost 当前成本价
     * @param priceRecycle 当前回收价
     * @param referencePrice 参考价格
     * @return 调整建议
     */
    public static Map<String, Object> calculatePriceAdjustmentSuggestion(
            BigDecimal priceCost, BigDecimal priceRecycle, BigDecimal referencePrice) {
        
        Map<String, Object> suggestion = new HashMap<>();
        
        if (referencePrice == null || referencePrice.compareTo(BigDecimal.ZERO) <= 0) {
            suggestion.put("hasSuggestion", false);
            suggestion.put("reason", "无有效参考价格");
            return suggestion;
        }
        
        // 建议的成本价（参考价格的90%）
        BigDecimal suggestedCost = referencePrice.multiply(new BigDecimal("0.9"))
                                                 .setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
        
        // 建议的回收价（参考价格的95%）
        BigDecimal suggestedRecycle = referencePrice.multiply(new BigDecimal("0.95"))
                                                   .setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
        
        suggestion.put("hasSuggestion", true);
        suggestion.put("referencePrice", referencePrice);
        suggestion.put("currentCost", priceCost);
        suggestion.put("currentRecycle", priceRecycle);
        suggestion.put("suggestedCost", suggestedCost);
        suggestion.put("suggestedRecycle", suggestedRecycle);
        
        // 计算调整幅度
        if (priceCost != null) {
            BigDecimal costAdjustment = suggestedCost.subtract(priceCost);
            BigDecimal costAdjustmentPercentage = priceCost.compareTo(BigDecimal.ZERO) > 0 ?
                costAdjustment.divide(priceCost, 4, DEFAULT_ROUNDING_MODE).multiply(new BigDecimal("100")) :
                BigDecimal.ZERO;
            
            suggestion.put("costAdjustment", costAdjustment);
            suggestion.put("costAdjustmentPercentage", costAdjustmentPercentage.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE));
        }
        
        if (priceRecycle != null) {
            BigDecimal recycleAdjustment = suggestedRecycle.subtract(priceRecycle);
            BigDecimal recycleAdjustmentPercentage = priceRecycle.compareTo(BigDecimal.ZERO) > 0 ?
                recycleAdjustment.divide(priceRecycle, 4, DEFAULT_ROUNDING_MODE).multiply(new BigDecimal("100")) :
                BigDecimal.ZERO;
            
            suggestion.put("recycleAdjustment", recycleAdjustment);
            suggestion.put("recycleAdjustmentPercentage", recycleAdjustmentPercentage.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE));
        }
        
        return suggestion;
    }

    /**
     * 验证价格数据的有效性
     * 
     * @param priceCost 成本价
     * @param priceRecycle 回收价
     * @param priceShow 展示价
     * @param priceBuy 购买价
     * @return 验证结果
     */
    public static Map<String, Object> validatePriceData(BigDecimal priceCost, BigDecimal priceRecycle, 
                                                        BigDecimal priceShow, BigDecimal priceBuy) {
        Map<String, Object> validation = new HashMap<>();
        boolean isValid = true;
        StringBuilder errors = new StringBuilder();
        
        // 检查价格是否为负数
        if (priceCost != null && priceCost.compareTo(BigDecimal.ZERO) < 0) {
            isValid = false;
            errors.append("成本价不能为负数; ");
        }
        
        if (priceRecycle != null && priceRecycle.compareTo(BigDecimal.ZERO) < 0) {
            isValid = false;
            errors.append("回收价不能为负数; ");
        }
        
        if (priceShow != null && priceShow.compareTo(BigDecimal.ZERO) < 0) {
            isValid = false;
            errors.append("展示价不能为负数; ");
        }
        
        if (priceBuy != null && priceBuy.compareTo(BigDecimal.ZERO) < 0) {
            isValid = false;
            errors.append("购买价不能为负数; ");
        }
        
        // 检查价格逻辑关系
        if (priceCost != null && priceRecycle != null && priceCost.compareTo(priceRecycle) > 0) {
            // 这是我们要检测的异常情况，不算错误
            validation.put("hasAnomaly", true);
        }
        
        validation.put("isValid", isValid);
        validation.put("errors", errors.toString());
        
        return validation;
    }

    /**
     * 格式化价格显示
     * 
     * @param price 价格
     * @return 格式化后的价格字符串
     */
    public static String formatPrice(BigDecimal price) {
        if (price == null) {
            return "0.00";
        }
        return price.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE).toString();
    }

    /**
     * 格式化百分比显示
     * 
     * @param percentage 百分比
     * @return 格式化后的百分比字符串
     */
    public static String formatPercentage(BigDecimal percentage) {
        if (percentage == null) {
            return "0.00%";
        }
        return percentage.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE).toString() + "%";
    }
}
