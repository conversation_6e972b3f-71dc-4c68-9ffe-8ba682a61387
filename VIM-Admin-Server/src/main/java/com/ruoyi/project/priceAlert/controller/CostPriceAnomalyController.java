package com.ruoyi.project.priceAlert.controller;

import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.priceAlert.domain.VimCostPriceAnomalyDTO;
import com.ruoyi.project.priceAlert.domain.DataSyncStatus;
import com.ruoyi.project.priceAlert.domain.NotificationMessage;
import com.ruoyi.project.priceAlert.service.ICostPriceAnomalyService;
import com.ruoyi.project.priceAlert.service.IDataSyncMonitorService;
import com.ruoyi.project.priceAlert.service.IExternalDataSyncService;
import com.ruoyi.project.priceAlert.service.INotificationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import java.util.HashMap;
import java.util.Map;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 成本价异常商品管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Api(tags = "成本价异常商品管理")
@Slf4j
@RestController
@RequestMapping("/priceAlert/anomaly")
public class CostPriceAnomalyController extends BaseController {

    @Autowired
    private ICostPriceAnomalyService costPriceAnomalyService;

    @Autowired
    private IDataSyncMonitorService dataSyncMonitorService;

    @Autowired
    private IExternalDataSyncService externalDataSyncService;

    @Autowired
    private INotificationService notificationService;

    // ========== 成本价异常商品管理 ==========

    /**
     * 查询成本价异常商品列表
     */
    @ApiOperation("查询成本价异常商品列表")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @GetMapping("/list")
    public TableDataInfo getCostPriceAnomalyList(
            @ApiParam("商品名称") @RequestParam(required = false) String itemName,
            @ApiParam("盲盒名称") @RequestParam(required = false) String boxName,
            @ApiParam("商品标签") @RequestParam(required = false) String itemTag,
            @ApiParam("销售状态") @RequestParam(required = false) Integer saleStatus,
            @ApiParam("最小成本价") @RequestParam(required = false) BigDecimal minCost,
            @ApiParam("最大成本价") @RequestParam(required = false) BigDecimal maxCost,
            @ApiParam("最小回收价") @RequestParam(required = false) BigDecimal minRecycle,
            @ApiParam("最大回收价") @RequestParam(required = false) BigDecimal maxRecycle,
            @ApiParam("最小价格差异") @RequestParam(required = false) BigDecimal minDifference,
            @ApiParam("最大价格差异") @RequestParam(required = false) BigDecimal maxDifference,
            @ApiParam("最小差异百分比") @RequestParam(required = false) BigDecimal minPercentage,
            @ApiParam("最大差异百分比") @RequestParam(required = false) BigDecimal maxPercentage,
            @ApiParam("库存状态") @RequestParam(required = false) Integer stockStatus,
            @ApiParam("异常严重程度") @RequestParam(required = false) Integer severityLevel,
            @ApiParam("排序字段") @RequestParam(required = false) String sortField) {

        startPage();
        List<VimCostPriceAnomalyDTO> list = costPriceAnomalyService.selectCostPriceAnomalyList(
            itemName, boxName, itemTag, saleStatus, minCost, maxCost, minRecycle, maxRecycle,
            minDifference, maxDifference, minPercentage, maxPercentage, stockStatus, severityLevel, sortField);
        return getDataTable(list);
    }

    /**
     * 统计成本价异常商品数量
     */
    @ApiOperation("统计成本价异常商品数量")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @GetMapping("/count")
    public AjaxResult getCostPriceAnomalyCount() {
        int count = costPriceAnomalyService.countCostPriceAnomalyItems();
        return success(count);
    }

    /**
     * 获取成本价异常商品统计信息
     */
    @ApiOperation("获取成本价异常商品统计信息")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @GetMapping("/statistics")
    public AjaxResult getCostPriceAnomalyStatistics() {
        Map<String, Object> statistics = costPriceAnomalyService.getCostPriceAnomalyStatistics();
        return success(statistics);
    }

    /**
     * 导出成本价异常商品列表
     */
    @ApiOperation("导出成本价异常商品列表")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:export')")
    @Log(title = "成本价异常商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void exportCostPriceAnomalyList(HttpServletResponse response,
            @ApiParam("商品名称") @RequestParam(required = false) String itemName,
            @ApiParam("盲盒名称") @RequestParam(required = false) String boxName,
            @ApiParam("商品标签") @RequestParam(required = false) String itemTag,
            @ApiParam("销售状态") @RequestParam(required = false) Integer saleStatus,
            @ApiParam("最小成本价") @RequestParam(required = false) BigDecimal minCost,
            @ApiParam("最大成本价") @RequestParam(required = false) BigDecimal maxCost,
            @ApiParam("最小回收价") @RequestParam(required = false) BigDecimal minRecycle,
            @ApiParam("最大回收价") @RequestParam(required = false) BigDecimal maxRecycle,
            @ApiParam("最小价格差异") @RequestParam(required = false) BigDecimal minDifference,
            @ApiParam("最大价格差异") @RequestParam(required = false) BigDecimal maxDifference,
            @ApiParam("最小差异百分比") @RequestParam(required = false) BigDecimal minPercentage,
            @ApiParam("最大差异百分比") @RequestParam(required = false) BigDecimal maxPercentage,
            @ApiParam("库存状态") @RequestParam(required = false) Integer stockStatus,
            @ApiParam("异常严重程度") @RequestParam(required = false) Integer severityLevel,
            @ApiParam("排序字段") @RequestParam(required = false) String sortField) {
        
        List<VimCostPriceAnomalyDTO> list = costPriceAnomalyService.selectCostPriceAnomalyList(
            itemName, boxName, itemTag, saleStatus, minCost, maxCost, minRecycle, maxRecycle,
            minDifference, maxDifference, minPercentage, maxPercentage, stockStatus, severityLevel, sortField);
        ExcelUtil<VimCostPriceAnomalyDTO> util = new ExcelUtil<VimCostPriceAnomalyDTO>(VimCostPriceAnomalyDTO.class);
        util.exportExcel(response, list, "成本价异常商品数据");
    }

    // ========== 数据同步功能 ==========

    /**
     * 获取数据同步状态
     */
    @ApiOperation("获取数据同步状态")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @GetMapping("/sync/status")
    public AjaxResult getDataSyncStatus() {
        List<DataSyncStatus> statusList = dataSyncMonitorService.getDataSyncStatusList();
        return success(statusList);
    }

    /**
     * 手动触发数据同步
     */
    @ApiOperation("手动触发数据同步")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:sync')")
    @Log(title = "数据同步", businessType = BusinessType.UPDATE)
    @PostMapping("/sync/trigger")
    public AjaxResult triggerManualSync() {
        Map<String, Object> result = dataSyncMonitorService.triggerManualSync();
        return success(result);
    }

    /**
     * 检查数据更新状态
     */
    @ApiOperation("检查数据更新状态")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @GetMapping("/sync/check")
    public AjaxResult checkDataFreshness() {
        Map<String, Object> result = dataSyncMonitorService.checkDataFreshness();
        return success(result);
    }

    /**
     * 触发外部数据获取
     */
    @ApiOperation("触发外部数据获取")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:sync')")
    @Log(title = "外部数据获取", businessType = BusinessType.UPDATE)
    @PostMapping("/sync/external-fetch")
    public AjaxResult triggerExternalDataFetch(@RequestBody Map<String, Object> params) {
        try {
            String platform = (String) params.getOrDefault("platform", "yp");
            Integer startPage = (Integer) params.getOrDefault("startPage", 1);
            Integer maxPages = (Integer) params.getOrDefault("maxPages", 50);

            Map<String, Object> result = externalDataSyncService.fetchDataFromSkin86(platform, startPage, maxPages);

            if ((Boolean) result.getOrDefault("success", false)) {
                return AjaxResult.success("外部数据获取任务启动成功", result);
            } else {
                return AjaxResult.error("外部数据获取失败：" + result.get("error"));
            }
        } catch (Exception e) {
            log.error("触发外部数据获取失败", e);
            return AjaxResult.error("外部数据获取失败：" + e.getMessage());
        }
    }

    /**
     * 测试外部API连接
     */
    @ApiOperation("测试外部API连接")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:query')")
    @GetMapping("/test/external-api")
    public AjaxResult testExternalApiConnection() {
        try {
            Map<String, Object> result = externalDataSyncService.testExternalApiConnection();

            if ((Boolean) result.getOrDefault("success", false)) {
                return AjaxResult.success("外部API连接测试成功", result);
            } else {
                return AjaxResult.error("外部API连接测试失败", result);
            }
        } catch (Exception e) {
            log.error("外部API连接测试失败", e);
            return AjaxResult.error("外部API连接测试失败：" + e.getMessage());
        }
    }

    /**
     * 获取外部数据统计
     */
    @ApiOperation("获取外部数据统计")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:query')")
    @GetMapping("/external/statistics")
    public AjaxResult getExternalDataStatistics() {
        try {
            Map<String, Object> result = externalDataSyncService.getExternalDataStatistics();
            return AjaxResult.success("获取外部数据统计成功", result);
        } catch (Exception e) {
            log.error("获取外部数据统计失败", e);
            return AjaxResult.error("获取外部数据统计失败：" + e.getMessage());
        }
    }

    // ========== 数据源测试功能 ==========

    /**
     * 测试从库连接
     */
    @ApiOperation("测试从库连接")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @GetMapping("/test/slave-connection")
    public AjaxResult testSlaveConnection() {
        try {
            // 直接调用SkinGoodsMapper来测试从库连接
            List<Map<String, Object>> testData = costPriceAnomalyService.testSlaveConnection();

            Map<String, Object> result = new HashMap<>();
            result.put("connectionStatus", "SUCCESS");
            result.put("dataCount", testData.size());
            result.put("sampleData", testData.size() > 0 ? testData.get(0) : null);
            result.put("message", "从库连接正常，skin_goods表可正常访问");

            return AjaxResult.success("从库连接测试成功", result);
        } catch (Exception e) {
            log.error("从库连接测试失败", e);

            Map<String, Object> result = new HashMap<>();
            result.put("connectionStatus", "FAILED");
            result.put("errorMessage", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());

            return AjaxResult.error("从库连接测试失败：" + e.getMessage());
        }
    }

    // ========== 通知功能 ==========

    /**
     * 获取通知列表
     */
    @ApiOperation("获取通知列表")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @GetMapping("/notifications")
    public TableDataInfo getNotifications(
            @ApiParam("通知类型") @RequestParam(required = false) String notificationType,
            @ApiParam("阅读状态") @RequestParam(required = false) Integer readStatus) {
        
        startPage();
        List<NotificationMessage> list = notificationService.getNotificationList(notificationType, readStatus);
        return getDataTable(list);
    }

    /**
     * 标记通知为已读
     */
    @ApiOperation("标记通知为已读")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @Log(title = "通知管理", businessType = BusinessType.UPDATE)
    @PostMapping("/notifications/{id}/read")
    public AjaxResult markNotificationAsRead(@PathVariable Long id) {
        boolean result = notificationService.markNotificationAsRead(id);
        return result ? success() : error("操作失败");
    }

    /**
     * 批量标记通知为已读
     */
    @ApiOperation("批量标记通知为已读")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @Log(title = "通知管理", businessType = BusinessType.UPDATE)
    @PostMapping("/notifications/batch-read")
    public AjaxResult batchMarkNotificationsAsRead(@RequestBody Long[] ids) {
        boolean result = notificationService.batchMarkNotificationsAsRead(ids);
        return result ? success() : error("操作失败");
    }

    /**
     * 获取未读通知数量
     */
    @ApiOperation("获取未读通知数量")
    @PreAuthorize("@ss.hasPermi('priceAlert:anomaly:list')")
    @GetMapping("/notifications/unread-count")
    public AjaxResult getUnreadNotificationCount() {
        int count = notificationService.getUnreadNotificationCount();
        return success(count);
    }
}
