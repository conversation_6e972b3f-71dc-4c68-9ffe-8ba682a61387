package com.ruoyi.project.priceAlert.service.impl;

import com.ruoyi.project.priceAlert.service.ICostPriceAnomalyService;
import com.ruoyi.project.priceAlert.domain.VimCostPriceAnomalyDTO;
import com.ruoyi.project.priceAlert.mapper.CostPriceAnomalyMapper;
import com.ruoyi.project.commoditySys.mapper.SkinGoodsMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 成本价异常商品服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class CostPriceAnomalyServiceImpl implements ICostPriceAnomalyService {

    @Autowired
    private CostPriceAnomalyMapper costPriceAnomalyMapper;

    @Autowired
    private SkinGoodsMapper skinGoodsMapper;

    @Override
    public List<VimCostPriceAnomalyDTO> selectCostPriceAnomalyList(String itemName, String boxName, String itemTag,
            Integer saleStatus, BigDecimal minCost, BigDecimal maxCost,
            BigDecimal minRecycle, BigDecimal maxRecycle,
            BigDecimal minDifference, BigDecimal maxDifference,
            BigDecimal minPercentage, BigDecimal maxPercentage,
            Integer stockStatus, Integer severityLevel, String sortField) {

        try {
            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("itemName", itemName);
            params.put("boxName", boxName);
            params.put("itemTag", itemTag);
            params.put("saleStatus", saleStatus);
            params.put("minCost", minCost);
            params.put("maxCost", maxCost);
            params.put("minRecycle", minRecycle);
            params.put("maxRecycle", maxRecycle);
            params.put("minDifference", minDifference);
            params.put("maxDifference", maxDifference);
            params.put("minPercentage", minPercentage);
            params.put("maxPercentage", maxPercentage);
            params.put("stockStatus", stockStatus);
            params.put("severityLevel", severityLevel);
            params.put("sortField", sortField);

            // 查询异常商品列表
            List<VimCostPriceAnomalyDTO> list = costPriceAnomalyMapper.selectCostPriceAnomalyList(params);

            // 批量获取参考价格并计算增强信息
            enhanceAnomalyListWithReferencePrice(list);

            return list;
        } catch (Exception e) {
            log.error("查询成本价异常商品列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public int countCostPriceAnomalyItems() {
        try {
            return costPriceAnomalyMapper.countCostPriceAnomalyItems();
        } catch (Exception e) {
            log.error("统计成本价异常商品数量失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getCostPriceAnomalyStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 基础统计
            int totalAnomalyCount = countCostPriceAnomalyItems();
            int totalItemCount = costPriceAnomalyMapper.countTotalItems();
            
            statistics.put("totalAnomalyCount", totalAnomalyCount);
            statistics.put("totalItemCount", totalItemCount);
            statistics.put("anomalyPercentage", calculatePercentage(totalAnomalyCount, totalItemCount));

            // 按严重程度统计
            Map<String, Object> severityStats = getAnomalyStatisticsBySeverity();
            statistics.put("severityStatistics", severityStats);

            // 按标签分布统计
            List<Map<String, Object>> tagDistribution = getAnomalyDistributionByTag();
            statistics.put("tagDistribution", tagDistribution);

            // 价格范围统计
            Map<String, Object> priceRangeStats = costPriceAnomalyMapper.getPriceRangeStatistics();
            statistics.put("priceRangeStatistics", priceRangeStats);

            // 最后更新时间
            statistics.put("lastUpdateTime", DateUtils.getNowDate());

        } catch (Exception e) {
            log.error("获取成本价异常商品统计信息失败", e);
        }

        return statistics;
    }

    @Override
    public Map<String, Object> getAnomalyStatisticsBySeverity() {
        Map<String, Object> severityStats = new HashMap<>();
        
        try {
            List<Map<String, Object>> severityList = costPriceAnomalyMapper.getAnomalyStatisticsBySeverity();
            
            int lightCount = 0, moderateCount = 0, severeCount = 0;
            
            for (Map<String, Object> item : severityList) {
                Integer severity = (Integer) item.get("severity_level");
                Integer count = (Integer) item.get("count");
                
                if (severity != null && count != null) {
                    switch (severity) {
                        case 1: lightCount = count; break;
                        case 2: moderateCount = count; break;
                        case 3: severeCount = count; break;
                    }
                }
            }
            
            severityStats.put("light", lightCount);
            severityStats.put("moderate", moderateCount);
            severityStats.put("severe", severeCount);
            severityStats.put("total", lightCount + moderateCount + severeCount);
            
        } catch (Exception e) {
            log.error("获取异常严重程度统计失败", e);
        }
        
        return severityStats;
    }

    @Override
    public List<Map<String, Object>> getAnomalyTrendData(int days) {
        try {
            return costPriceAnomalyMapper.getAnomalyTrendData(days);
        } catch (Exception e) {
            log.error("获取异常商品趋势数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getAnomalyDistributionByTag() {
        try {
            return costPriceAnomalyMapper.getAnomalyDistributionByTag();
        } catch (Exception e) {
            log.error("获取异常商品标签分布失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<VimCostPriceAnomalyDTO> detectNewAnomalyItems(Long lastCheckTime) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("lastCheckTime", lastCheckTime);
            return costPriceAnomalyMapper.detectNewAnomalyItems(params);
        } catch (Exception e) {
            log.error("检测新增异常商品失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public int calculateSeverityLevel(BigDecimal priceDifference, BigDecimal differencePercentage) {
        if (priceDifference == null || differencePercentage == null) {
            return 1; // 默认轻微
        }

        // 严重程度判断逻辑
        if (differencePercentage.compareTo(new BigDecimal("50")) > 0 || 
            priceDifference.compareTo(new BigDecimal("100")) > 0) {
            return 3; // 严重
        } else if (differencePercentage.compareTo(new BigDecimal("20")) > 0 || 
                   priceDifference.compareTo(new BigDecimal("50")) > 0) {
            return 2; // 中等
        } else {
            return 1; // 轻微
        }
    }

    @Override
    public Map<String, Object> getAnomalyItemDetail(Long itemId) {
        Map<String, Object> detail = new HashMap<>();
        
        try {
            // 获取商品基本信息
            VimCostPriceAnomalyDTO item = costPriceAnomalyMapper.getAnomalyItemById(itemId);
            if (item == null) {
                return detail;
            }
            
            detail.put("item", item);
            
            // 获取参考价格
            if (item.getHashname() != null) {
                BigDecimal referencePrice = skinGoodsMapper.getReferencePriceByHashName(item.getHashname());
                detail.put("referencePrice", referencePrice);
                
                // 计算与参考价格的差异
                if (referencePrice != null && item.getPriceCost() != null) {
                    BigDecimal referenceDifference = item.getPriceCost().subtract(referencePrice);
                    BigDecimal referencePercentage = referencePrice.compareTo(BigDecimal.ZERO) > 0 ?
                        referenceDifference.divide(referencePrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")) :
                        BigDecimal.ZERO;
                    
                    detail.put("referenceDifference", referenceDifference);
                    detail.put("referencePercentage", referencePercentage);
                }
                
                // 获取价格历史
                List<Map<String, Object>> priceHistory = skinGoodsMapper.getPriceHistory(item.getHashname(), 168); // 7天
                detail.put("priceHistory", priceHistory);
            }
            
            // 计算严重程度
            int severityLevel = calculateSeverityLevel(item.getPriceDifference(), item.getDifferencePercentage());
            detail.put("severityLevel", severityLevel);
            detail.put("severityText", getSeverityText(severityLevel));
            
        } catch (Exception e) {
            log.error("获取异常商品详情失败，itemId: {}", itemId, e);
        }
        
        return detail;
    }

    @Override
    public Map<String, Object> batchCheckAnomalyStatus(List<Long> itemIds) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Map<String, Object>> statusList = costPriceAnomalyMapper.batchCheckAnomalyStatus(itemIds);
            result.put("statusList", statusList);
            result.put("totalCount", itemIds.size());
            result.put("anomalyCount", statusList.size());
            
        } catch (Exception e) {
            log.error("批量检查商品异常状态失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    // ========== 私有辅助方法 ==========

    /**
     * 批量增强异常商品列表，添加参考价格信息
     */
    private void enhanceAnomalyListWithReferencePrice(List<VimCostPriceAnomalyDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        try {
            // 批量获取参考价格
            List<String> hashNames = new ArrayList<>();
            for (VimCostPriceAnomalyDTO item : list) {
                if (item.getHashname() != null && !item.getHashname().trim().isEmpty()) {
                    hashNames.add(item.getHashname());
                }
            }

            if (!hashNames.isEmpty()) {
                // 使用批量查询获取参考价格
                Map<String, BigDecimal> referencePriceMap = new HashMap<>();
                try {
                    List<Map<String, Object>> priceResults = skinGoodsMapper.getReferencePricesByHashNames(hashNames);
                    for (Map<String, Object> result : priceResults) {
                        String hashName = (String) result.get("hashName");
                        BigDecimal price = (BigDecimal) result.get("price");
                        if (hashName != null && price != null) {
                            referencePriceMap.put(hashName, price);
                        }
                    }
                } catch (Exception e) {
                    log.warn("批量查询参考价格失败，使用单个查询作为备选方案", e);
                    // 备选方案：单个查询
                    for (String hashName : hashNames) {
                        try {
                            BigDecimal price = skinGoodsMapper.getReferencePriceByHashName(hashName);
                            if (price != null) {
                                referencePriceMap.put(hashName, price);
                            }
                        } catch (Exception ex) {
                            log.warn("查询商品 {} 的参考价格失败", hashName, ex);
                        }
                    }
                }
                
                // 为每个商品设置参考价格和计算相关信息
                for (VimCostPriceAnomalyDTO item : list) {
                    if (item.getHashname() != null) {
                        BigDecimal referencePrice = referencePriceMap.get(item.getHashname());
                        item.setReferencePrice(referencePrice);
                        
                        // 计算严重程度
                        int severityLevel = calculateSeverityLevel(item.getPriceDifference(), item.getDifferencePercentage());
                        item.setSeverityLevel(severityLevel);
                        item.setSeverityText(getSeverityText(severityLevel));
                    }
                }
            }
        } catch (Exception e) {
            log.error("批量增强异常商品列表失败", e);
        }
    }

    /**
     * 计算百分比
     */
    private BigDecimal calculatePercentage(int part, int total) {
        if (total == 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(part).divide(new BigDecimal(total), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 获取严重程度文本
     */
    private String getSeverityText(int severityLevel) {
        switch (severityLevel) {
            case 1: return "轻微";
            case 2: return "中等";
            case 3: return "严重";
            default: return "未知";
        }
    }

    @Override
    public List<Map<String, Object>> testSlaveConnection() {
        try {
            log.info("开始测试从库连接...");

            // 测试基本查询
            List<Map<String, Object>> testResults = new ArrayList<>();

            // 测试1：查询skin_goods表的基本信息
            Map<String, Object> basicTest = new HashMap<>();
            basicTest.put("testName", "基本连接测试");
            try {
                BigDecimal testPrice = skinGoodsMapper.getReferencePriceByHashName("AK-47 | Redline (Field-Tested)");
                basicTest.put("status", "SUCCESS");
                basicTest.put("result", testPrice != null ? testPrice.toString() : "null");
                basicTest.put("message", "成功查询到参考价格");
            } catch (Exception e) {
                basicTest.put("status", "FAILED");
                basicTest.put("error", e.getMessage());
                basicTest.put("errorType", e.getClass().getSimpleName());
            }
            testResults.add(basicTest);

            // 测试2：批量查询测试
            Map<String, Object> batchTest = new HashMap<>();
            batchTest.put("testName", "批量查询测试");
            try {
                List<String> testHashNames = Arrays.asList(
                    "AK-47 | Redline (Field-Tested)",
                    "AWP | Dragon Lore (Factory New)",
                    "M4A4 | Howl (Minimal Wear)"
                );
                List<Map<String, Object>> batchResults = skinGoodsMapper.getReferencePricesByHashNames(testHashNames);
                batchTest.put("status", "SUCCESS");
                batchTest.put("result", batchResults.size() + " 条记录");
                batchTest.put("data", batchResults);
                batchTest.put("message", "批量查询成功");
            } catch (Exception e) {
                batchTest.put("status", "FAILED");
                batchTest.put("error", e.getMessage());
                batchTest.put("errorType", e.getClass().getSimpleName());
            }
            testResults.add(batchTest);

            // 测试3：数据库连接信息
            Map<String, Object> connectionInfo = new HashMap<>();
            connectionInfo.put("testName", "数据库连接信息");
            connectionInfo.put("status", "INFO");
            connectionInfo.put("database", "autopw");
            connectionInfo.put("table", "skin_goods");
            connectionInfo.put("dataSource", "SLAVE");
            connectionInfo.put("message", "从库配置信息");
            testResults.add(connectionInfo);

            log.info("从库连接测试完成，共执行 {} 个测试", testResults.size());
            return testResults;

        } catch (Exception e) {
            log.error("从库连接测试失败", e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("testName", "连接测试");
            errorResult.put("status", "FAILED");
            errorResult.put("error", e.getMessage());
            errorResult.put("errorType", e.getClass().getSimpleName());
            errorResult.put("message", "从库连接测试失败");

            return Arrays.asList(errorResult);
        }
    }
}
