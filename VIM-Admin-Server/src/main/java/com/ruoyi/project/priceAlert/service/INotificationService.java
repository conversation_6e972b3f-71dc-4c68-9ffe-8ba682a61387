package com.ruoyi.project.priceAlert.service;

import com.ruoyi.project.priceAlert.domain.NotificationMessage;

import java.util.List;
import java.util.Map;

/**
 * 通知服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface INotificationService {

    /**
     * 获取通知列表
     *
     * @param notificationType 通知类型（可选）
     * @param readStatus 阅读状态（可选）
     * @return 通知列表
     */
    List<NotificationMessage> getNotificationList(String notificationType, Integer readStatus);

    /**
     * 根据用户ID获取通知列表
     *
     * @param userId 用户ID
     * @param readStatus 阅读状态（可选）
     * @return 通知列表
     */
    List<NotificationMessage> getNotificationListByUserId(Long userId, Integer readStatus);

    /**
     * 获取未读通知列表
     *
     * @param userId 用户ID
     * @return 未读通知列表
     */
    List<NotificationMessage> getUnreadNotifications(Long userId);

    /**
     * 根据ID获取通知详情
     *
     * @param id 通知ID
     * @return 通知详情
     */
    NotificationMessage getNotificationById(Long id);

    /**
     * 创建通知
     *
     * @param notificationType 通知类型
     * @param title 通知标题
     * @param content 通知内容
     * @param targetUsers 目标用户ID列表
     * @param extraData 额外数据
     * @return 创建结果
     */
    boolean createNotification(String notificationType, String title, String content, 
                              List<Long> targetUsers, Map<String, Object> extraData);

    /**
     * 创建系统广播通知
     *
     * @param notificationType 通知类型
     * @param title 通知标题
     * @param content 通知内容
     * @param extraData 额外数据
     * @return 创建结果
     */
    boolean createBroadcastNotification(String notificationType, String title, String content, 
                                       Map<String, Object> extraData);

    /**
     * 标记通知为已读
     *
     * @param id 通知ID
     * @return 操作结果
     */
    boolean markNotificationAsRead(Long id);

    /**
     * 批量标记通知为已读
     *
     * @param ids 通知ID列表
     * @return 操作结果
     */
    boolean batchMarkNotificationsAsRead(Long[] ids);

    /**
     * 标记用户所有通知为已读
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean markAllNotificationsAsReadByUserId(Long userId);

    /**
     * 删除通知
     *
     * @param id 通知ID
     * @return 删除结果
     */
    boolean deleteNotification(Long id);

    /**
     * 批量删除通知
     *
     * @param ids 通知ID列表
     * @return 删除结果
     */
    boolean batchDeleteNotifications(Long[] ids);

    /**
     * 获取用户未读通知数量
     *
     * @param userId 用户ID
     * @return 未读通知数量
     */
    int getUnreadNotificationCount(Long userId);

    /**
     * 获取未读通知数量（当前用户）
     *
     * @return 未读通知数量
     */
    int getUnreadNotificationCount();

    /**
     * 获取通知统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getNotificationStatistics();

    /**
     * 根据通知类型统计数量
     *
     * @param notificationType 通知类型
     * @return 通知数量
     */
    int countNotificationsByType(String notificationType);

    /**
     * 获取最近的通知
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近的通知列表
     */
    List<NotificationMessage> getRecentNotifications(Long userId, int limit);

    /**
     * 清理过期的通知
     *
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanExpiredNotifications(int retentionDays);

    /**
     * 发送数据同步通知
     *
     * @param syncType 同步类型
     * @param syncStatus 同步状态
     * @param anomalyCount 异常数量
     * @param totalItems 总商品数量
     * @return 发送结果
     */
    boolean sendDataSyncNotification(String syncType, Integer syncStatus, Integer anomalyCount, Integer totalItems);

    /**
     * 发送价格异常通知
     *
     * @param anomalyCount 异常数量
     * @param severityLevel 严重程度
     * @param affectedItems 受影响的商品列表
     * @return 发送结果
     */
    boolean sendPriceAnomalyNotification(Integer anomalyCount, Integer severityLevel, List<Long> affectedItems);

    /**
     * 发送系统提醒通知
     *
     * @param title 通知标题
     * @param content 通知内容
     * @param extraData 额外数据
     * @return 发送结果
     */
    boolean sendSystemAlertNotification(String title, String content, Map<String, Object> extraData);
}
