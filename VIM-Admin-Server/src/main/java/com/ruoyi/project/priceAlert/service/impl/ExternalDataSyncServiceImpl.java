package com.ruoyi.project.priceAlert.service.impl;

import com.ruoyi.project.priceAlert.service.IExternalDataSyncService;
import com.skin86.starter.dto.SkinGoodsInfo;
import com.skin86.starter.dto.SkinPriceInfo;
import com.skin86.starter.service.Skin86DataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 外部数据同步服务实现类
 * 使用skin86-starter-project JAR包进行数据同步
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class ExternalDataSyncServiceImpl implements IExternalDataSyncService {

    @Autowired
    private Skin86DataService skin86DataService;

    @Override
    public Map<String, Object> fetchDataFromSkin86(String platform, Integer startPage, Integer maxPages) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始从Skin86获取数据，平台: {}, 起始页: {}, 最大页数: {}", platform, startPage, maxPages);

            // 设置默认值
            String targetPlatform = platform != null ? platform : "yp";
            int currentPage = startPage != null ? startPage : 1;
            int pageSize = 20; // 每页数量
            int totalPages = maxPages != null ? maxPages : 50;

            // 检查平台是否支持
            if (!skin86DataService.isPlatformSupported(targetPlatform)) {
                result.put("success", false);
                result.put("error", "不支持的平台: " + targetPlatform);
                result.put("supportedPlatforms", skin86DataService.getSupportedPlatforms());
                return result;
            }

            List<SkinGoodsInfo> allGoods = new ArrayList<>();
            int successPages = 0;
            int failedPages = 0;

            // 分页获取数据
            for (int page = currentPage; page <= currentPage + totalPages - 1; page++) {
                try {
                    CompletableFuture<List<SkinGoodsInfo>> future =
                            skin86DataService.getGoodsList(targetPlatform, page, pageSize);

                    List<SkinGoodsInfo> pageGoods = future.get(); // 同步等待结果

                    if (pageGoods != null && !pageGoods.isEmpty()) {
                        allGoods.addAll(pageGoods);
                        successPages++;
                        log.info("成功获取第{}页数据，商品数量: {}", page, pageGoods.size());
                    } else {
                        log.warn("第{}页没有数据", page);
                        break; // 没有更多数据，停止获取
                    }

                    // 避免请求过于频繁
                    Thread.sleep(100);

                } catch (Exception e) {
                    log.error("获取第{}页数据失败: {}", page, e.getMessage());
                    failedPages++;
                }
            }

            result.put("success", true);
            result.put("message", "数据同步完成");
            result.put("platform", targetPlatform);
            result.put("totalGoods", allGoods.size());
            result.put("successPages", successPages);
            result.put("failedPages", failedPages);
            result.put("goods", allGoods);

            log.info("Skin86数据获取完成，总商品数: {}, 成功页数: {}, 失败页数: {}",
                    allGoods.size(), successPages, failedPages);

        } catch (Exception e) {
            log.error("从Skin86获取数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
        }

        return result;
    }

    @Override
    public Map<String, Object> checkSkin86ServiceStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("检查Skin86服务状态");

            // 使用新的JAR包检查服务状态
            List<String> supportedPlatforms = skin86DataService.getSupportedPlatforms();

            CompletableFuture<List<SkinGoodsInfo>> testFuture =
                skin86DataService.getGoodsList("yp", 1, 1);

            List<SkinGoodsInfo> testGoods = testFuture.get();

            result.put("available", true);
            result.put("status", "HEALTHY");
            result.put("message", "Skin86服务正常");
            result.put("responseTime", System.currentTimeMillis());
            result.put("supportedPlatforms", supportedPlatforms);
            result.put("testResult", testGoods != null ? "SUCCESS" : "NO_DATA");

            log.info("Skin86服务状态正常，支持平台: {}", supportedPlatforms);

        } catch (Exception e) {
            log.error("检查Skin86服务状态失败", e);
            result.put("available", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            result.put("status", "UNHEALTHY");
        }

        return result;
    }

    @Override
    public Map<String, Object> getSkin86TaskStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("获取Skin86任务状态");

            // 使用新的JAR包方式，任务状态简化处理
            List<String> supportedPlatforms = skin86DataService.getSupportedPlatforms();

            result.put("success", true);
            result.put("isRunning", false); // 新实现是同步的，没有长期运行的任务
            result.put("message", "使用新的JAR包实现，同步获取数据");
            result.put("supportedPlatforms", supportedPlatforms);
            result.put("implementationType", "JAR_PACKAGE");

            log.info("获取Skin86任务状态成功，当前使用JAR包实现");

        } catch (Exception e) {
            log.error("获取Skin86任务状态失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> stopSkin86Task() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("停止Skin86任务");
            result.put("success", true);
            result.put("message", "新的JAR包实现是同步的，无需停止任务");
            result.put("implementationType", "JAR_PACKAGE");

            log.info("JAR包实现无需停止任务");

        } catch (Exception e) {
            log.error("停止Skin86任务失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> updateReferencePrices(List<String> hashNames) {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("使用JAR包更新参考价格，商品数量: {}", hashNames.size());

            int updatedCount = 0;
            int failedCount = 0;
            List<String> failedItems = new ArrayList<>();
            List<Map<String, Object>> priceData = new ArrayList<>();

            // 使用JAR包获取价格信息
            for (String hashName : hashNames) {
                try {
                    // 搜索商品获取价格信息
                    CompletableFuture<List<SkinGoodsInfo>> searchFuture =
                        skin86DataService.searchGoods("yp", hashName, 1);

                    List<SkinGoodsInfo> searchResults = searchFuture.get();

                    if (searchResults != null && !searchResults.isEmpty()) {
                        SkinGoodsInfo goodsInfo = searchResults.get(0);
                        Map<String, Object> priceInfo = new HashMap<>();
                        priceInfo.put("hashName", hashName);
                        priceInfo.put("sellMinPrice", goodsInfo.sellMinPrice());
                        priceInfo.put("buyMaxPrice", goodsInfo.buyMaxPrice());
                        priceInfo.put("sellValuation", goodsInfo.sellValuation());
                        priceInfo.put("priceAlterPercentage7d", goodsInfo.priceAlterPercentage7d());
                        priceInfo.put("priceAlterValue7d", goodsInfo.priceAlterValue7d());

                        priceData.add(priceInfo);
                        updatedCount++;
                        log.debug("成功获取商品 {} 的价格信息", hashName);
                    } else {
                        failedCount++;
                        failedItems.add(hashName);
                        log.warn("未找到商品 {} 的价格信息", hashName);
                    }

                    // 避免请求过于频繁
                    Thread.sleep(50);

                } catch (Exception e) {
                    log.warn("获取商品 {} 价格失败: {}", hashName, e.getMessage());
                    failedCount++;
                    failedItems.add(hashName);
                }
            }

            result.put("success", true);
            result.put("totalItems", hashNames.size());
            result.put("updatedCount", updatedCount);
            result.put("failedCount", failedCount);
            result.put("failedItems", failedItems);
            result.put("priceData", priceData);
            result.put("message", String.format("价格更新完成，成功: %d, 失败: %d", updatedCount, failedCount));

            log.info("参考价格更新完成，成功: {}, 失败: {}", updatedCount, failedCount);

        } catch (Exception e) {
            log.error("更新参考价格失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> executeFullExternalSync() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始执行完整的外部数据同步");
            long startTime = System.currentTimeMillis();

            // 检查服务状态
            Map<String, Object> serviceStatus = checkSkin86ServiceStatus();
            if (!(Boolean) serviceStatus.getOrDefault("available", false)) {
                result.put("success", false);
                result.put("error", "Skin86服务不可用");
                result.put("serviceStatus", serviceStatus);
                return result;
            }

            // 获取数据
            Map<String, Object> fetchResult = fetchDataFromSkin86("yp", 1, 10);

            long endTime = System.currentTimeMillis();
            int syncDuration = (int) (endTime - startTime);

            result.put("success", fetchResult.getOrDefault("success", false));
            result.put("syncDuration", syncDuration);
            result.put("serviceStatus", serviceStatus);
            result.put("fetchResult", fetchResult);
            result.put("message", "外部数据同步完成");

            log.info("完整外部数据同步完成，耗时: {}ms", syncDuration);

        } catch (Exception e) {
            log.error("执行完整外部数据同步失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> testExternalApiConnection() {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("测试外部API连接");

            // 使用JAR包测试连接
            List<String> supportedPlatforms = skin86DataService.getSupportedPlatforms();
            CompletableFuture<List<SkinGoodsInfo>> testFuture =
                skin86DataService.getGoodsList("yp", 1, 1);

            List<SkinGoodsInfo> testResult = testFuture.get();

            result.put("success", true);
            result.put("message", "API连接正常");
            result.put("supportedPlatforms", supportedPlatforms);
            result.put("testDataCount", testResult != null ? testResult.size() : 0);

        } catch (Exception e) {
            log.error("测试外部API连接失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        return result;
    }

    @Override
    public Map<String, Object> getExternalDataStatistics() {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("获取外部数据统计信息");

            List<String> supportedPlatforms = skin86DataService.getSupportedPlatforms();

            result.put("success", true);
            result.put("supportedPlatforms", supportedPlatforms);
            result.put("platformCount", supportedPlatforms.size());
            result.put("implementationType", "JAR_PACKAGE");
            result.put("message", "统计信息获取成功");

        } catch (Exception e) {
            log.error("获取外部数据统计信息失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        return result;
    }

    @Override
    public Map<String, Object> syncSingleItemPrice(String hashName) {
        return updateReferencePrices(Collections.singletonList(hashName));
    }

    @Override
    public Map<String, Object> batchSyncItemPrices(List<String> hashNames, Integer batchSize) {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("批量同步商品价格，总数: {}, 批次大小: {}", hashNames.size(), batchSize);

            int actualBatchSize = batchSize != null ? batchSize : 10;
            List<Map<String, Object>> batchResults = new ArrayList<>();

            // 分批处理
            for (int i = 0; i < hashNames.size(); i += actualBatchSize) {
                int endIndex = Math.min(i + actualBatchSize, hashNames.size());
                List<String> batch = hashNames.subList(i, endIndex);

                log.info("处理第{}批，商品数量: {}", (i / actualBatchSize) + 1, batch.size());
                Map<String, Object> batchResult = updateReferencePrices(batch);
                batchResults.add(batchResult);

                // 批次间休息
                if (endIndex < hashNames.size()) {
                    Thread.sleep(1000);
                }
            }

            result.put("success", true);
            result.put("totalItems", hashNames.size());
            result.put("batchSize", actualBatchSize);
            result.put("batchCount", batchResults.size());
            result.put("batchResults", batchResults);
            result.put("message", "批量价格同步完成");

        } catch (Exception e) {
            log.error("批量同步商品价格失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        return result;
    }
}
