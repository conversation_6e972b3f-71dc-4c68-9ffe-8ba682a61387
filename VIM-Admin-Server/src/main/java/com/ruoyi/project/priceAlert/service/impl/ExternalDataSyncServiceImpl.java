package com.ruoyi.project.priceAlert.service.impl;

import com.ruoyi.project.priceAlert.service.IExternalDataSyncService;
import com.ruoyi.project.commoditySys.mapper.SkinGoodsMapper;
import com.ruoyi.common.utils.http.HttpUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 外部数据同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class ExternalDataSyncServiceImpl implements IExternalDataSyncService {

    @Autowired
    private SkinGoodsMapper skinGoodsMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // Skin86 API配置
    @Value("${external.skin86.base-url:http://localhost:8989}")
    private String skin86BaseUrl;

    @Value("${external.skin86.api-key:sk-skin86-dev-12345678}")
    private String skin86ApiKey;

    @Value("${external.skin86.timeout:30000}")
    private int requestTimeout;

    // API路径常量
    private static final String HEALTH_PATH = "/api/v1/tasks/health";
    private static final String STATUS_PATH = "/api/v1/tasks/status";
    private static final String FETCH_PATH = "/api/v1/tasks/goods/fetch";
    private static final String STOP_PATH = "/api/v1/tasks/stop";
    private static final String STATS_PATH = "/api/v1/tasks/database/stats";

    @Override
    public Map<String, Object> fetchDataFromSkin86(String platform, Integer startPage, Integer maxPages) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始从Skin86获取数据，平台: {}, 起始页: {}, 最大页数: {}", platform, startPage, maxPages);
            
            // 构建请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("platform", platform != null ? platform : "yp");
            requestBody.put("startPage", startPage != null ? startPage : 1);
            requestBody.put("maxPages", maxPages != null ? maxPages : 50);
            
            // 发送HTTP请求
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            String url = skin86BaseUrl + FETCH_PATH;
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                
                result.put("success", true);
                result.put("taskId", responseBody.get("taskId"));
                result.put("status", responseBody.get("status"));
                result.put("message", "数据获取任务启动成功");
                result.put("platform", platform);
                result.put("requestParams", requestBody);
                result.put("apiResponse", responseBody);
                
                log.info("Skin86数据获取任务启动成功，任务ID: {}", responseBody.get("taskId"));
            } else {
                throw new RuntimeException("API响应状态异常: " + response.getStatusCode());
            }
            
        } catch (ResourceAccessException e) {
            log.error("Skin86服务连接失败", e);
            result.put("success", false);
            result.put("error", "Skin86服务不可用，请检查服务状态");
            result.put("errorType", "CONNECTION_FAILED");
        } catch (Exception e) {
            log.error("从Skin86获取数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> checkSkin86ServiceStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("检查Skin86服务状态");
            
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            String url = skin86BaseUrl + HEALTH_PATH;
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                
                result.put("available", true);
                result.put("status", responseBody.get("status"));
                result.put("message", "Skin86服务正常");
                result.put("responseTime", System.currentTimeMillis());
                result.put("apiResponse", responseBody);
                
                log.info("Skin86服务状态正常");
            } else {
                result.put("available", false);
                result.put("error", "服务响应异常: " + response.getStatusCode());
            }
            
        } catch (ResourceAccessException e) {
            log.warn("Skin86服务连接失败", e);
            result.put("available", false);
            result.put("error", "服务连接失败");
            result.put("errorType", "CONNECTION_FAILED");
        } catch (Exception e) {
            log.error("检查Skin86服务状态失败", e);
            result.put("available", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getSkin86TaskStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("获取Skin86任务状态");
            
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            String url = skin86BaseUrl + STATUS_PATH;
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                
                result.put("success", true);
                result.put("isRunning", responseBody.get("isRunning"));
                result.put("currentPage", responseBody.get("currentPage"));
                result.put("totalPages", responseBody.get("totalPages"));
                result.put("processedCount", responseBody.get("processedCount"));
                result.put("taskInfo", responseBody);
                
                log.info("获取Skin86任务状态成功，运行中: {}", responseBody.get("isRunning"));
            } else {
                throw new RuntimeException("获取任务状态失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("获取Skin86任务状态失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> stopSkin86Task() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("停止Skin86任务");
            
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            String url = skin86BaseUrl + STOP_PATH;
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                result.put("success", true);
                result.put("message", "任务停止成功");
                log.info("Skin86任务停止成功");
            } else {
                throw new RuntimeException("停止任务失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("停止Skin86任务失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> updateReferencePrices(List<String> hashNames) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("更新参考价格，商品数量: {}", hashNames.size());
            
            int updatedCount = 0;
            int failedCount = 0;
            List<String> failedItems = new ArrayList<>();
            
            // 这里应该调用实际的价格更新API
            // 目前作为示例，我们模拟更新过程
            for (String hashName : hashNames) {
                try {
                    // 模拟API调用获取最新价格
                    BigDecimal newPrice = fetchLatestPriceFromApi(hashName);
                    if (newPrice != null) {
                        // 更新到数据库
                        // skinGoodsMapper.updatePrice(hashName, newPrice);
                        updatedCount++;
                    } else {
                        failedCount++;
                        failedItems.add(hashName);
                    }
                } catch (Exception e) {
                    log.warn("更新商品 {} 价格失败", hashName, e);
                    failedCount++;
                    failedItems.add(hashName);
                }
            }
            
            result.put("success", true);
            result.put("totalItems", hashNames.size());
            result.put("updatedCount", updatedCount);
            result.put("failedCount", failedCount);
            result.put("failedItems", failedItems);
            result.put("message", String.format("价格更新完成，成功: %d, 失败: %d", updatedCount, failedCount));
            
            log.info("参考价格更新完成，成功: {}, 失败: {}", updatedCount, failedCount);
            
        } catch (Exception e) {
            log.error("更新参考价格失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> executeFullExternalSync() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始执行完整的外部数据同步");
            long startTime = System.currentTimeMillis();
            
            // 第一步：检查服务状态
            Map<String, Object> serviceStatus = checkSkin86ServiceStatus();
            if (!(Boolean) serviceStatus.getOrDefault("available", false)) {
                result.put("success", false);
                result.put("error", "Skin86服务不可用");
                result.put("serviceStatus", serviceStatus);
                return result;
            }
            
            // 第二步：启动数据获取任务
            Map<String, Object> fetchResult = fetchDataFromSkin86("yp", 1, 50);
            if (!(Boolean) fetchResult.getOrDefault("success", false)) {
                result.put("success", false);
                result.put("error", "启动数据获取任务失败");
                result.put("fetchResult", fetchResult);
                return result;
            }
            
            // 第三步：监控任务进度
            boolean taskCompleted = waitForTaskCompletion(300); // 5分钟超时
            
            // 第四步：获取更新统计
            Map<String, Object> stats = getExternalDataStatistics();
            
            long endTime = System.currentTimeMillis();
            int syncDuration = (int) (endTime - startTime);
            
            result.put("success", taskCompleted);
            result.put("syncDuration", syncDuration);
            result.put("serviceStatus", serviceStatus);
            result.put("fetchResult", fetchResult);
            result.put("statistics", stats);
            result.put("message", taskCompleted ? "外部数据同步完成" : "外部数据同步超时");
            
            log.info("完整外部数据同步完成，耗时: {}ms, 成功: {}", syncDuration, taskCompleted);
            
        } catch (Exception e) {
            log.error("执行完整外部数据同步失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    // ========== 私有辅助方法 ==========

    /**
     * 创建HTTP请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-Key", skin86ApiKey);
        headers.set("User-Agent", "VIM-System/1.0");
        return headers;
    }

    /**
     * 等待任务完成
     */
    private boolean waitForTaskCompletion(int timeoutSeconds) {
        int checkInterval = 5; // 5秒检查一次
        int maxChecks = timeoutSeconds / checkInterval;
        
        for (int i = 0; i < maxChecks; i++) {
            try {
                Map<String, Object> taskStatus = getSkin86TaskStatus();
                Boolean isRunning = (Boolean) taskStatus.get("isRunning");
                
                if (isRunning == null || !isRunning) {
                    log.info("任务已完成，检查次数: {}", i + 1);
                    return true;
                }
                
                log.info("任务进行中，第 {} 次检查", i + 1);
                TimeUnit.SECONDS.sleep(checkInterval);
                
            } catch (Exception e) {
                log.warn("检查任务状态失败，第 {} 次", i + 1, e);
            }
        }
        
        log.warn("任务等待超时，超时时间: {}秒", timeoutSeconds);
        return false;
    }

    /**
     * 从API获取最新价格（示例实现）
     */
    private BigDecimal fetchLatestPriceFromApi(String hashName) {
        try {
            // 这里应该调用实际的价格查询API
            // 目前返回模拟数据
            return new BigDecimal("100.00");
        } catch (Exception e) {
            log.warn("获取商品 {} 最新价格失败", hashName, e);
            return null;
        }
    }

    @Override
    public Map<String, Object> testExternalApiConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("测试外部API连接");

            // 测试1：基本连接测试
            Map<String, Object> healthCheck = checkSkin86ServiceStatus();

            // 测试2：获取统计信息
            Map<String, Object> stats = getExternalDataStatistics();

            // 测试3：获取任务状态
            Map<String, Object> taskStatus = getSkin86TaskStatus();

            boolean allTestsPassed = (Boolean) healthCheck.getOrDefault("available", false) &&
                                   (Boolean) stats.getOrDefault("success", false) &&
                                   (Boolean) taskStatus.getOrDefault("success", false);

            result.put("success", allTestsPassed);
            result.put("healthCheck", healthCheck);
            result.put("statistics", stats);
            result.put("taskStatus", taskStatus);
            result.put("message", allTestsPassed ? "外部API连接测试通过" : "外部API连接测试失败");

        } catch (Exception e) {
            log.error("测试外部API连接失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getExternalDataStatistics() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("获取外部数据统计信息");

            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            String url = skin86BaseUrl + STATS_PATH;
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();

                result.put("success", true);
                result.put("totalRecords", responseBody.get("totalRecords"));
                result.put("lastUpdateTime", responseBody.get("lastUpdateTime"));
                result.put("platformStats", responseBody.get("platformStats"));
                result.put("apiResponse", responseBody);

                log.info("获取外部数据统计信息成功");
            } else {
                throw new RuntimeException("获取统计信息失败: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取外部数据统计信息失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> syncSingleItemPrice(String hashName) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("同步单个商品价格: {}", hashName);

            BigDecimal newPrice = fetchLatestPriceFromApi(hashName);
            if (newPrice != null) {
                // 更新到数据库
                // skinGoodsMapper.updatePrice(hashName, newPrice);

                result.put("success", true);
                result.put("hashName", hashName);
                result.put("newPrice", newPrice);
                result.put("message", "商品价格同步成功");
            } else {
                result.put("success", false);
                result.put("error", "获取最新价格失败");
            }

        } catch (Exception e) {
            log.error("同步单个商品价格失败: {}", hashName, e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> batchSyncItemPrices(List<String> hashNames, Integer batchSize) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("批量同步商品价格，数量: {}, 批次大小: {}", hashNames.size(), batchSize);

            int actualBatchSize = batchSize != null ? batchSize : 10;
            int totalItems = hashNames.size();
            int processedItems = 0;
            int successCount = 0;
            int failedCount = 0;
            List<String> failedItems = new ArrayList<>();

            // 分批处理
            for (int i = 0; i < totalItems; i += actualBatchSize) {
                int endIndex = Math.min(i + actualBatchSize, totalItems);
                List<String> batch = hashNames.subList(i, endIndex);

                log.info("处理批次 {}-{}/{}", i + 1, endIndex, totalItems);

                for (String hashName : batch) {
                    try {
                        Map<String, Object> syncResult = syncSingleItemPrice(hashName);
                        if ((Boolean) syncResult.getOrDefault("success", false)) {
                            successCount++;
                        } else {
                            failedCount++;
                            failedItems.add(hashName);
                        }
                        processedItems++;
                    } catch (Exception e) {
                        log.warn("批量同步中处理商品 {} 失败", hashName, e);
                        failedCount++;
                        failedItems.add(hashName);
                        processedItems++;
                    }
                }

                // 批次间暂停，避免API频率限制
                if (i + actualBatchSize < totalItems) {
                    try {
                        TimeUnit.MILLISECONDS.sleep(500); // 500ms暂停
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            result.put("success", true);
            result.put("totalItems", totalItems);
            result.put("processedItems", processedItems);
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("failedItems", failedItems);
            result.put("batchSize", actualBatchSize);
            result.put("message", String.format("批量同步完成，成功: %d, 失败: %d", successCount, failedCount));

            log.info("批量同步商品价格完成，成功: {}, 失败: {}", successCount, failedCount);

        } catch (Exception e) {
            log.error("批量同步商品价格失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
}
