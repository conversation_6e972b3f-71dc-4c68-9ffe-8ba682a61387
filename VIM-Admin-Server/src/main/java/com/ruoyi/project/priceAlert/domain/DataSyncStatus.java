package com.ruoyi.project.priceAlert.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.framework.web.domain.BaseEntity;

import java.util.Date;

/**
 * 数据同步状态对象 vim_data_sync_status
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DataSyncStatus", description = "数据同步状态对象")
public class DataSyncStatus extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    /** 同步类型：price_check,anomaly_scan */
    @ApiModelProperty(value = "同步类型", example = "price_check", required = true, allowableValues = "price_check,anomaly_scan")
    private String syncType;

    /** 最后同步时间戳（秒） */
    @ApiModelProperty(value = "最后同步时间戳", example = "1693123200")
    private Long lastSyncTime;

    /** 最后数据更新时间戳（从skin_goods获取） */
    @ApiModelProperty(value = "最后数据更新时间戳", example = "1693123200")
    private Long lastDataUpdate;

    /** 同步状态：1=正常，2=异常 */
    @ApiModelProperty(value = "同步状态", example = "1", allowableValues = "1,2", notes = "1=正常，2=异常")
    private Integer syncStatus;

    /** 异常商品数量 */
    @ApiModelProperty(value = "异常商品数量", example = "10")
    private Integer anomalyCount;

    /** 总商品数量 */
    @ApiModelProperty(value = "总商品数量", example = "1000")
    private Integer totalItems;

    /** 同步耗时（毫秒） */
    @ApiModelProperty(value = "同步耗时", example = "5000", notes = "单位：毫秒")
    private Integer syncDuration;

    /** 错误信息 */
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    /** 创建时间戳（秒） */
    @ApiModelProperty(value = "创建时间戳", example = "1693123200")
    private Long createTimeStamp;

    /** 更新时间戳（秒） */
    @ApiModelProperty(value = "更新时间戳", example = "1693123200")
    private Long updateTimeStamp;

    // ========== 辅助字段 ==========

    /** 最后同步时间（格式化显示） */
    @ApiModelProperty(value = "最后同步时间", example = "2025-08-25 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastSyncTimeFormatted;

    /** 最后数据更新时间（格式化显示） */
    @ApiModelProperty(value = "最后数据更新时间", example = "2025-08-25 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastDataUpdateFormatted;

    /** 同步状态文本 */
    @ApiModelProperty(value = "同步状态文本", example = "正常")
    private String syncStatusText;

    /** 异常比例 */
    @ApiModelProperty(value = "异常比例", example = "1.5", notes = "异常商品数量/总商品数量*100")
    private Double anomalyPercentage;

    /** 是否有新数据 */
    @ApiModelProperty(value = "是否有新数据", example = "true")
    private Boolean hasNewData;

    // ========== 枚举定义 ==========

    /**
     * 同步类型枚举
     */
    public enum SyncType {
        PRICE_CHECK("price_check", "价格检查"),
        ANOMALY_SCAN("anomaly_scan", "异常扫描");

        private final String code;
        private final String description;

        SyncType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum Status {
        NORMAL(1, "正常"),
        ERROR(2, "异常");

        private final Integer code;
        private final String description;

        Status(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    // ========== 辅助方法 ==========

    /**
     * 获取同步状态文本
     */
    public String getSyncStatusText() {
        if (syncStatus == null) {
            return "未知";
        }
        return syncStatus == 1 ? "正常" : "异常";
    }

    /**
     * 计算异常比例
     */
    public Double getAnomalyPercentage() {
        if (totalItems == null || totalItems == 0 || anomalyCount == null) {
            return 0.0;
        }
        return Math.round((anomalyCount.doubleValue() / totalItems.doubleValue() * 100) * 100.0) / 100.0;
    }

    /**
     * 判断是否有新数据
     */
    public Boolean getHasNewData() {
        if (lastSyncTime == null || lastDataUpdate == null) {
            return false;
        }
        return lastDataUpdate > lastSyncTime;
    }

    /**
     * 获取格式化的最后同步时间
     */
    public Date getLastSyncTimeFormatted() {
        if (lastSyncTime == null) {
            return null;
        }
        return new Date(lastSyncTime * 1000);
    }

    /**
     * 获取格式化的最后数据更新时间
     */
    public Date getLastDataUpdateFormatted() {
        if (lastDataUpdate == null) {
            return null;
        }
        return new Date(lastDataUpdate * 1000);
    }
}
