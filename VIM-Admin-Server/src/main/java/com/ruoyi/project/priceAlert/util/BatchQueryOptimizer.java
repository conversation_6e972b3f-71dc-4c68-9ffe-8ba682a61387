package com.ruoyi.project.priceAlert.util;

import com.ruoyi.project.commoditySys.mapper.SkinGoodsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 批量查询优化器
 * 
 * 用于优化成本价异常商品管理中的批量查询操作，减少N+1查询问题
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class BatchQueryOptimizer {

    @Autowired
    private SkinGoodsMapper skinGoodsMapper;

    // 批量查询的默认大小
    private static final int DEFAULT_BATCH_SIZE = 100;
    
    // 缓存配置
    private static final int CACHE_EXPIRE_MINUTES = 30;
    
    // 内存缓存
    private final Map<String, BigDecimal> priceCache = new ConcurrentHashMap<>();
    private final Map<String, Long> cacheTimestamps = new ConcurrentHashMap<>();

    /**
     * 批量获取参考价格
     * 
     * @param hashNames 商品hashName列表
     * @return hashName -> 参考价格的映射
     */
    public Map<String, BigDecimal> batchGetReferencePrices(List<String> hashNames) {
        if (hashNames == null || hashNames.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, BigDecimal> result = new HashMap<>();
        List<String> needQueryHashNames = new ArrayList<>();
        
        // 检查缓存
        long currentTime = System.currentTimeMillis();
        for (String hashName : hashNames) {
            BigDecimal cachedPrice = getCachedPrice(hashName, currentTime);
            if (cachedPrice != null) {
                result.put(hashName, cachedPrice);
            } else {
                needQueryHashNames.add(hashName);
            }
        }

        // 批量查询未缓存的数据
        if (!needQueryHashNames.isEmpty()) {
            try {
                List<Map<String, Object>> priceResults = skinGoodsMapper.getReferencePricesByHashNames(needQueryHashNames);
                for (Map<String, Object> priceResult : priceResults) {
                    String hashName = (String) priceResult.get("hashName");
                    BigDecimal price = (BigDecimal) priceResult.get("price");
                    if (hashName != null && price != null) {
                        result.put(hashName, price);
                        // 更新缓存
                        updateCache(hashName, price, currentTime);
                    }
                }
            } catch (Exception e) {
                log.error("批量查询参考价格失败", e);
                // 备选方案：单个查询
                for (String hashName : needQueryHashNames) {
                    try {
                        BigDecimal price = skinGoodsMapper.getReferencePriceByHashName(hashName);
                        if (price != null) {
                            result.put(hashName, price);
                            updateCache(hashName, price, currentTime);
                        }
                    } catch (Exception ex) {
                        log.warn("查询商品 {} 的参考价格失败", hashName, ex);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 分批处理大数据集
     * 
     * @param dataList 数据列表
     * @param batchSize 批次大小
     * @param processor 处理函数
     * @param <T> 数据类型
     * @param <R> 结果类型
     * @return 处理结果列表
     */
    public <T, R> List<R> processBatch(List<T> dataList, int batchSize, 
                                      java.util.function.Function<List<T>, List<R>> processor) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        List<R> results = new ArrayList<>();
        int totalSize = dataList.size();
        
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<T> batch = dataList.subList(i, endIndex);
            
            try {
                List<R> batchResults = processor.apply(batch);
                if (batchResults != null) {
                    results.addAll(batchResults);
                }
            } catch (Exception e) {
                log.error("批量处理失败，批次范围: {}-{}", i, endIndex, e);
            }
        }

        return results;
    }

    /**
     * 异步批量处理
     * 
     * @param dataList 数据列表
     * @param processor 处理函数
     * @param <T> 数据类型
     * @param <R> 结果类型
     * @return 异步结果
     */
    public <T, R> CompletableFuture<List<R>> processAsync(List<T> dataList, 
                                                         java.util.function.Function<List<T>, List<R>> processor) {
        return CompletableFuture.supplyAsync(() -> 
            processBatch(dataList, DEFAULT_BATCH_SIZE, processor));
    }

    /**
     * 清理过期缓存
     */
    public void cleanExpiredCache() {
        long currentTime = System.currentTimeMillis();
        long expireTime = CACHE_EXPIRE_MINUTES * 60 * 1000;
        
        Set<String> expiredKeys = cacheTimestamps.entrySet().stream()
            .filter(entry -> currentTime - entry.getValue() > expireTime)
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
            
        for (String key : expiredKeys) {
            priceCache.remove(key);
            cacheTimestamps.remove(key);
        }
        
        if (!expiredKeys.isEmpty()) {
            log.debug("清理过期缓存，数量: {}", expiredKeys.size());
        }
    }

    /**
     * 获取缓存的价格
     */
    private BigDecimal getCachedPrice(String hashName, long currentTime) {
        Long timestamp = cacheTimestamps.get(hashName);
        if (timestamp != null) {
            long expireTime = CACHE_EXPIRE_MINUTES * 60 * 1000;
            if (currentTime - timestamp <= expireTime) {
                return priceCache.get(hashName);
            } else {
                // 清理过期缓存
                priceCache.remove(hashName);
                cacheTimestamps.remove(hashName);
            }
        }
        return null;
    }

    /**
     * 更新缓存
     */
    private void updateCache(String hashName, BigDecimal price, long timestamp) {
        priceCache.put(hashName, price);
        cacheTimestamps.put(hashName, timestamp);
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", priceCache.size());
        stats.put("cacheHitRate", calculateCacheHitRate());
        stats.put("lastCleanTime", System.currentTimeMillis());
        return stats;
    }

    /**
     * 计算缓存命中率（简化实现）
     */
    private double calculateCacheHitRate() {
        // 这里可以实现更复杂的命中率计算逻辑
        return priceCache.size() > 0 ? 0.8 : 0.0;
    }

    /**
     * 清空所有缓存
     */
    public void clearAllCache() {
        priceCache.clear();
        cacheTimestamps.clear();
        log.info("已清空所有缓存");
    }
}
