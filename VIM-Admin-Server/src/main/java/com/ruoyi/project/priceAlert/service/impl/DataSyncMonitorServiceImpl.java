package com.ruoyi.project.priceAlert.service.impl;

import com.ruoyi.project.priceAlert.service.IDataSyncMonitorService;
import com.ruoyi.project.priceAlert.service.INotificationService;
import com.ruoyi.project.priceAlert.service.IExternalDataSyncService;
import com.ruoyi.project.priceAlert.domain.DataSyncStatus;
import com.ruoyi.project.priceAlert.mapper.DataSyncStatusMapper;
import com.ruoyi.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 数据同步监控服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class DataSyncMonitorServiceImpl implements IDataSyncMonitorService {

    @Autowired
    private DataSyncStatusMapper dataSyncStatusMapper;

    @Autowired
    private INotificationService notificationService;

    @Autowired
    private IExternalDataSyncService externalDataSyncService;

    @Override
    public List<DataSyncStatus> getDataSyncStatusList() {
        try {
            return dataSyncStatusMapper.selectDataSyncStatusList(new DataSyncStatus());
        } catch (Exception e) {
            log.error("获取数据同步状态列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public DataSyncStatus getDataSyncStatusBySyncType(String syncType) {
        try {
            return dataSyncStatusMapper.selectDataSyncStatusBySyncType(syncType);
        } catch (Exception e) {
            log.error("根据同步类型获取数据同步状态失败，syncType: {}", syncType, e);
            return null;
        }
    }

    @Override
    public Map<String, Object> checkDataFreshness() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取最新的数据更新时间
            Long lastDataUpdate = dataSyncStatusMapper.getLastDataUpdateFromSkinGoods();
            
            // 获取上次检查时间
            DataSyncStatus priceCheckStatus = getDataSyncStatusBySyncType("price_check");
            Long lastCheckTime = priceCheckStatus != null ? priceCheckStatus.getLastSyncTime() : null;
            
            // 判断是否有新数据
            boolean hasNewData = false;
            if (lastDataUpdate != null && lastCheckTime != null) {
                hasNewData = lastDataUpdate > lastCheckTime;
            }
            
            result.put("lastDataUpdate", lastDataUpdate);
            result.put("lastCheckTime", lastCheckTime);
            result.put("hasNewData", hasNewData);
            result.put("checkTime", System.currentTimeMillis() / 1000);
            
            // 如果有新数据，更新检查时间
            if (hasNewData) {
                updateDataSyncStatus("price_check", 1, null, null, null, null);
                
                // 发送通知
                notificationService.sendDataSyncNotification("price_check", 1, null, null);
            }
            
        } catch (Exception e) {
            log.error("检查数据新鲜度失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> triggerManualSync() {
        Map<String, Object> result = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();
            log.info("开始手动触发数据同步，包含外部API调用");

            // 第一步：执行外部数据同步
            log.info("步骤1: 执行外部数据同步");
            Map<String, Object> externalSyncResult = externalDataSyncService.executeFullExternalSync();
            boolean externalSyncSuccess = (Boolean) externalSyncResult.getOrDefault("success", false);

            // 第二步：检查数据新鲜度
            log.info("步骤2: 检查数据新鲜度");
            Map<String, Object> freshnessResult = checkDataFreshness();

            // 第三步：统计异常商品数量（在外部数据更新后重新统计）
            log.info("步骤3: 统计异常商品数量");
            int anomalyCount = dataSyncStatusMapper.countAnomalyItems();
            int totalItems = dataSyncStatusMapper.countTotalItems();

            long endTime = System.currentTimeMillis();
            int syncDuration = (int) (endTime - startTime);

            // 第四步：更新同步状态
            log.info("步骤4: 更新同步状态");
            updateDataSyncStatus("anomaly_scan", externalSyncSuccess ? 1 : 2,
                               anomalyCount, totalItems, syncDuration,
                               externalSyncSuccess ? null : "外部数据同步失败");

            // 构建结果
            result.put("success", true);
            result.put("externalSyncSuccess", externalSyncSuccess);
            result.put("externalSyncResult", externalSyncResult);
            result.put("anomalyCount", anomalyCount);
            result.put("totalItems", totalItems);
            result.put("syncDuration", syncDuration);
            result.put("freshnessResult", freshnessResult);
            result.put("syncTime", System.currentTimeMillis() / 1000);
            result.put("syncSteps", Arrays.asList(
                "外部数据同步: " + (externalSyncSuccess ? "成功" : "失败"),
                "数据新鲜度检查: 完成",
                "异常商品统计: " + anomalyCount + "/" + totalItems,
                "同步状态更新: 完成"
            ));

            // 发送同步完成通知
            notificationService.sendDataSyncNotification("anomaly_scan",
                externalSyncSuccess ? 1 : 2, anomalyCount, totalItems);

            log.info("手动数据同步完成，外部同步: {}, 异常商品: {}/{}, 耗时: {}ms",
                    externalSyncSuccess, anomalyCount, totalItems, syncDuration);

        } catch (Exception e) {
            log.error("手动触发数据同步失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());

            // 更新同步状态为异常
            updateDataSyncStatus("anomaly_scan", 2, null, null, null, e.getMessage());
        }

        return result;
    }

    @Override
    @Async
    public CompletableFuture<Map<String, Object>> executeAsyncSync(String syncType) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            if ("price_check".equals(syncType)) {
                result = checkDataFreshness();
            } else if ("anomaly_scan".equals(syncType)) {
                result = triggerManualSync();
            } else {
                result.put("error", "不支持的同步类型: " + syncType);
                return CompletableFuture.completedFuture(result);
            }
            
            long endTime = System.currentTimeMillis();
            result.put("asyncSyncDuration", (int) (endTime - startTime));
            result.put("asyncCompleted", true);
            
        } catch (Exception e) {
            log.error("异步执行数据同步失败，syncType: {}", syncType, e);
            result.put("error", e.getMessage());
            result.put("asyncCompleted", false);
        }
        
        return CompletableFuture.completedFuture(result);
    }

    @Override
    public boolean updateDataSyncStatus(String syncType, Integer syncStatus, Integer anomalyCount, 
                                       Integer totalItems, Integer syncDuration, String errorMessage) {
        try {
            DataSyncStatus status = new DataSyncStatus();
            status.setSyncType(syncType);
            status.setLastSyncTime(System.currentTimeMillis() / 1000);
            status.setSyncStatus(syncStatus);
            status.setAnomalyCount(anomalyCount);
            status.setTotalItems(totalItems);
            status.setSyncDuration(syncDuration);
            status.setErrorMessage(errorMessage);
            status.setUpdateTimeStamp(System.currentTimeMillis() / 1000);
            
            // 获取最新的数据更新时间
            Long lastDataUpdate = dataSyncStatusMapper.getLastDataUpdateFromSkinGoods();
            status.setLastDataUpdate(lastDataUpdate);
            
            int result = dataSyncStatusMapper.updateDataSyncStatusBySyncType(status);
            return result > 0;
            
        } catch (Exception e) {
            log.error("更新数据同步状态失败，syncType: {}", syncType, e);
            return false;
        }
    }

    @Override
    public Long getLastDataUpdateFromSkinGoods() {
        try {
            return dataSyncStatusMapper.getLastDataUpdateFromSkinGoods();
        } catch (Exception e) {
            log.error("获取最后数据更新时间失败", e);
            return null;
        }
    }

    @Override
    public boolean hasNewDataUpdate(Long lastCheckTime) {
        try {
            return dataSyncStatusMapper.hasNewDataUpdate(lastCheckTime);
        } catch (Exception e) {
            log.error("检查是否有新数据更新失败", e);
            return false;
        }
    }

    @Override
    public int countAnomalyItems() {
        try {
            return dataSyncStatusMapper.countAnomalyItems();
        } catch (Exception e) {
            log.error("统计异常商品数量失败", e);
            return 0;
        }
    }

    @Override
    public int countTotalItems() {
        try {
            return dataSyncStatusMapper.countTotalItems();
        } catch (Exception e) {
            log.error("统计总商品数量失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getSyncStatusOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        try {
            List<DataSyncStatus> statusList = getDataSyncStatusList();
            
            overview.put("statusList", statusList);
            overview.put("totalSyncTypes", statusList.size());
            
            // 统计正常和异常的同步类型数量
            long normalCount = statusList.stream().filter(s -> s.getSyncStatus() != null && s.getSyncStatus() == 1).count();
            long errorCount = statusList.stream().filter(s -> s.getSyncStatus() != null && s.getSyncStatus() == 2).count();
            
            overview.put("normalCount", normalCount);
            overview.put("errorCount", errorCount);
            
            // 获取最新的异常统计
            int currentAnomalyCount = countAnomalyItems();
            int currentTotalItems = countTotalItems();
            
            overview.put("currentAnomalyCount", currentAnomalyCount);
            overview.put("currentTotalItems", currentTotalItems);
            overview.put("anomalyPercentage", currentTotalItems > 0 ? 
                Math.round((double) currentAnomalyCount / currentTotalItems * 100 * 100.0) / 100.0 : 0.0);
            
            overview.put("lastUpdateTime", DateUtils.getNowDate());
            
        } catch (Exception e) {
            log.error("获取同步状态概览失败", e);
            overview.put("error", e.getMessage());
        }
        
        return overview;
    }

    @Override
    public List<DataSyncStatus> getSyncHistory(String syncType, int limit) {
        try {
            DataSyncStatus query = new DataSyncStatus();
            if (syncType != null && !syncType.trim().isEmpty()) {
                query.setSyncType(syncType);
            }
            
            List<DataSyncStatus> history = dataSyncStatusMapper.selectDataSyncStatusList(query);
            
            // 限制返回数量
            if (history.size() > limit) {
                return history.subList(0, limit);
            }
            
            return history;
            
        } catch (Exception e) {
            log.error("获取同步历史记录失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public int cleanExpiredSyncStatus(int retentionDays) {
        try {
            return dataSyncStatusMapper.cleanExpiredSyncStatus(retentionDays);
        } catch (Exception e) {
            log.error("清理过期同步状态记录失败", e);
            return 0;
        }
    }

    @Override
    public boolean initializeDataSyncStatus() {
        try {
            long currentTime = System.currentTimeMillis() / 1000;
            
            // 初始化价格检查状态
            DataSyncStatus priceCheckStatus = getDataSyncStatusBySyncType("price_check");
            if (priceCheckStatus == null) {
                DataSyncStatus status = new DataSyncStatus();
                status.setSyncType("price_check");
                status.setLastSyncTime(currentTime);
                status.setLastDataUpdate(getLastDataUpdateFromSkinGoods());
                status.setSyncStatus(1);
                status.setAnomalyCount(0);
                status.setTotalItems(0);
                status.setSyncDuration(0);
                status.setCreateTimeStamp(currentTime);
                status.setUpdateTimeStamp(currentTime);
                
                dataSyncStatusMapper.insertDataSyncStatus(status);
            }
            
            // 初始化异常扫描状态
            DataSyncStatus anomalyScanStatus = getDataSyncStatusBySyncType("anomaly_scan");
            if (anomalyScanStatus == null) {
                DataSyncStatus status = new DataSyncStatus();
                status.setSyncType("anomaly_scan");
                status.setLastSyncTime(currentTime);
                status.setLastDataUpdate(getLastDataUpdateFromSkinGoods());
                status.setSyncStatus(1);
                status.setAnomalyCount(countAnomalyItems());
                status.setTotalItems(countTotalItems());
                status.setSyncDuration(0);
                status.setCreateTimeStamp(currentTime);
                status.setUpdateTimeStamp(currentTime);
                
                dataSyncStatusMapper.insertDataSyncStatus(status);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("初始化数据同步状态失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getSyncPerformanceStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            List<DataSyncStatus> recentSyncs = getSyncHistory(null, 10);
            
            if (!recentSyncs.isEmpty()) {
                // 计算平均同步时间
                double avgDuration = recentSyncs.stream()
                    .filter(s -> s.getSyncDuration() != null)
                    .mapToInt(DataSyncStatus::getSyncDuration)
                    .average()
                    .orElse(0.0);
                
                // 计算成功率
                long successCount = recentSyncs.stream()
                    .filter(s -> s.getSyncStatus() != null && s.getSyncStatus() == 1)
                    .count();
                
                double successRate = (double) successCount / recentSyncs.size() * 100;
                
                stats.put("avgSyncDuration", Math.round(avgDuration));
                stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
                stats.put("totalSyncs", recentSyncs.size());
                stats.put("successCount", successCount);
                stats.put("failureCount", recentSyncs.size() - successCount);
            } else {
                stats.put("avgSyncDuration", 0);
                stats.put("successRate", 0.0);
                stats.put("totalSyncs", 0);
                stats.put("successCount", 0);
                stats.put("failureCount", 0);
            }
            
        } catch (Exception e) {
            log.error("获取同步性能统计失败", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    @Override
    public Map<String, Object> detectSyncAnomalies() {
        Map<String, Object> anomalies = new HashMap<>();
        
        try {
            List<String> issues = new ArrayList<>();
            
            // 检查是否有长时间未同步的类型
            List<DataSyncStatus> statusList = getDataSyncStatusList();
            long currentTime = System.currentTimeMillis() / 1000;
            long oneHourAgo = currentTime - 3600; // 1小时前
            
            for (DataSyncStatus status : statusList) {
                if (status.getLastSyncTime() != null && status.getLastSyncTime() < oneHourAgo) {
                    issues.add("同步类型 " + status.getSyncType() + " 超过1小时未同步");
                }
                
                if (status.getSyncStatus() != null && status.getSyncStatus() == 2) {
                    issues.add("同步类型 " + status.getSyncType() + " 状态异常: " + status.getErrorMessage());
                }
            }
            
            // 检查异常商品数量是否异常增长
            int currentAnomalyCount = countAnomalyItems();
            DataSyncStatus lastAnomalyScan = getDataSyncStatusBySyncType("anomaly_scan");
            
            if (lastAnomalyScan != null && lastAnomalyScan.getAnomalyCount() != null) {
                int lastAnomalyCount = lastAnomalyScan.getAnomalyCount();
                if (currentAnomalyCount > lastAnomalyCount * 1.5) { // 增长超过50%
                    issues.add("异常商品数量异常增长: 从 " + lastAnomalyCount + " 增长到 " + currentAnomalyCount);
                }
            }
            
            anomalies.put("issues", issues);
            anomalies.put("hasAnomalies", !issues.isEmpty());
            anomalies.put("checkTime", currentTime);
            
        } catch (Exception e) {
            log.error("检测同步异常失败", e);
            anomalies.put("error", e.getMessage());
        }
        
        return anomalies;
    }
}
