package com.ruoyi.project.priceAlert.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * 外部API配置类
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Configuration
public class ExternalApiConfig {

    @Value("${external.api.connect-timeout:10000}")
    private int connectTimeout;

    @Value("${external.api.read-timeout:30000}")
    private int readTimeout;

    /**
     * 配置RestTemplate用于外部API调用
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        
        RestTemplate restTemplate = new RestTemplate(factory);
        
        // 可以添加拦截器、错误处理器等
        // restTemplate.setInterceptors(Arrays.asList(new LoggingInterceptor()));
        // restTemplate.setErrorHandler(new CustomErrorHandler());
        
        return restTemplate;
    }
}
