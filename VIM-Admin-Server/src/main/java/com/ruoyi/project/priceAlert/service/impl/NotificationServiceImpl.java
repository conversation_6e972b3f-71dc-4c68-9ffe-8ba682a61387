package com.ruoyi.project.priceAlert.service.impl;

import com.ruoyi.project.priceAlert.service.INotificationService;
import com.ruoyi.project.priceAlert.domain.NotificationMessage;
import com.ruoyi.project.priceAlert.mapper.NotificationMessageMapper;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 通知服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Service
public class NotificationServiceImpl implements INotificationService {

    @Autowired
    private NotificationMessageMapper notificationMessageMapper;

    @Override
    public List<NotificationMessage> getNotificationList(String notificationType, Integer readStatus) {
        try {
            NotificationMessage query = new NotificationMessage();
            query.setNotificationType(notificationType);
            query.setReadStatus(readStatus);
            
            return notificationMessageMapper.selectNotificationMessageList(query);
        } catch (Exception e) {
            log.error("获取通知列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<NotificationMessage> getNotificationListByUserId(Long userId, Integer readStatus) {
        try {
            return notificationMessageMapper.selectNotificationMessageByUserId(userId, readStatus);
        } catch (Exception e) {
            log.error("根据用户ID获取通知列表失败，userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<NotificationMessage> getUnreadNotifications(Long userId) {
        try {
            return notificationMessageMapper.selectUnreadNotificationMessages(userId);
        } catch (Exception e) {
            log.error("获取未读通知列表失败，userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public NotificationMessage getNotificationById(Long id) {
        try {
            return notificationMessageMapper.selectNotificationMessageById(id);
        } catch (Exception e) {
            log.error("根据ID获取通知详情失败，id: {}", id, e);
            return null;
        }
    }

    @Override
    public boolean createNotification(String notificationType, String title, String content, 
                                     List<Long> targetUsers, Map<String, Object> extraData) {
        try {
            NotificationMessage notification = new NotificationMessage();
            notification.setNotificationType(notificationType);
            notification.setTitle(title);
            notification.setContent(content);
            notification.setSendStatus(1); // 默认发送成功
            notification.setReadStatus(0); // 默认未读
            notification.setCreateTimeStamp(System.currentTimeMillis() / 1000);
            
            // 设置目标用户
            if (targetUsers != null && !targetUsers.isEmpty()) {
                notification.setTargetUsers(JSON.toJSONString(targetUsers));
            } else {
                notification.setTargetUsers("all"); // 广播给所有用户
            }
            
            // 设置额外数据
            if (extraData != null && !extraData.isEmpty()) {
                notification.setExtraData(JSON.toJSONString(extraData));
            }
            
            int result = notificationMessageMapper.insertNotificationMessage(notification);
            
            if (result > 0) {
                log.info("创建通知成功，类型: {}, 标题: {}, 目标用户数: {}", 
                    notificationType, title, targetUsers != null ? targetUsers.size() : "全部");
                return true;
            }
            
        } catch (Exception e) {
            log.error("创建通知失败", e);
        }
        
        return false;
    }

    @Override
    public boolean createBroadcastNotification(String notificationType, String title, String content, 
                                              Map<String, Object> extraData) {
        return createNotification(notificationType, title, content, null, extraData);
    }

    @Override
    public boolean markNotificationAsRead(Long id) {
        try {
            int result = notificationMessageMapper.markNotificationAsRead(id);
            return result > 0;
        } catch (Exception e) {
            log.error("标记通知为已读失败，id: {}", id, e);
            return false;
        }
    }

    @Override
    public boolean batchMarkNotificationsAsRead(Long[] ids) {
        try {
            int result = notificationMessageMapper.batchMarkNotificationsAsRead(ids);
            return result > 0;
        } catch (Exception e) {
            log.error("批量标记通知为已读失败", e);
            return false;
        }
    }

    @Override
    public boolean markAllNotificationsAsReadByUserId(Long userId) {
        try {
            int result = notificationMessageMapper.markAllNotificationsAsReadByUserId(userId);
            return result > 0;
        } catch (Exception e) {
            log.error("标记用户所有通知为已读失败，userId: {}", userId, e);
            return false;
        }
    }

    @Override
    public boolean deleteNotification(Long id) {
        try {
            int result = notificationMessageMapper.deleteNotificationMessageById(id);
            return result > 0;
        } catch (Exception e) {
            log.error("删除通知失败，id: {}", id, e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteNotifications(Long[] ids) {
        try {
            int result = notificationMessageMapper.deleteNotificationMessageByIds(ids);
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除通知失败", e);
            return false;
        }
    }

    @Override
    public int getUnreadNotificationCount(Long userId) {
        try {
            return notificationMessageMapper.countUnreadNotificationsByUserId(userId);
        } catch (Exception e) {
            log.error("获取用户未读通知数量失败，userId: {}", userId, e);
            return 0;
        }
    }

    @Override
    public int getUnreadNotificationCount() {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            if (currentUserId != null) {
                return getUnreadNotificationCount(currentUserId);
            }
            return 0;
        } catch (Exception e) {
            log.error("获取当前用户未读通知数量失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getNotificationStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 总通知数量
            int totalCount = notificationMessageMapper.countTotalNotifications();
            statistics.put("totalCount", totalCount);
            
            // 按类型统计
            Map<String, Integer> typeStats = new HashMap<>();
            typeStats.put("data_sync", notificationMessageMapper.countNotificationsByType("data_sync"));
            typeStats.put("price_anomaly", notificationMessageMapper.countNotificationsByType("price_anomaly"));
            typeStats.put("system_alert", notificationMessageMapper.countNotificationsByType("system_alert"));
            statistics.put("typeStatistics", typeStats);
            
            // 当前用户的未读数量
            Long currentUserId = SecurityUtils.getUserId();
            if (currentUserId != null) {
                int unreadCount = getUnreadNotificationCount(currentUserId);
                statistics.put("currentUserUnreadCount", unreadCount);
            }
            
        } catch (Exception e) {
            log.error("获取通知统计信息失败", e);
            statistics.put("error", e.getMessage());
        }
        
        return statistics;
    }

    @Override
    public int countNotificationsByType(String notificationType) {
        try {
            return notificationMessageMapper.countNotificationsByType(notificationType);
        } catch (Exception e) {
            log.error("根据类型统计通知数量失败，type: {}", notificationType, e);
            return 0;
        }
    }

    @Override
    public List<NotificationMessage> getRecentNotifications(Long userId, int limit) {
        try {
            return notificationMessageMapper.selectRecentNotifications(userId, limit);
        } catch (Exception e) {
            log.error("获取最近通知失败，userId: {}, limit: {}", userId, limit, e);
            return new ArrayList<>();
        }
    }

    @Override
    public int cleanExpiredNotifications(int retentionDays) {
        try {
            return notificationMessageMapper.cleanExpiredNotifications(retentionDays);
        } catch (Exception e) {
            log.error("清理过期通知失败", e);
            return 0;
        }
    }

    @Override
    public boolean sendDataSyncNotification(String syncType, Integer syncStatus, Integer anomalyCount, Integer totalItems) {
        try {
            String title;
            String content;
            String notificationType = "data_sync";
            
            if (syncStatus == 1) {
                title = "数据同步完成";
                if ("price_check".equals(syncType)) {
                    content = "价格数据检查完成，系统数据已更新";
                } else if ("anomaly_scan".equals(syncType)) {
                    content = String.format("异常商品扫描完成，发现 %d 个异常商品（总计 %d 个商品）", 
                        anomalyCount != null ? anomalyCount : 0, 
                        totalItems != null ? totalItems : 0);
                } else {
                    content = "数据同步完成";
                }
            } else {
                title = "数据同步异常";
                content = String.format("同步类型 %s 执行失败，请检查系统状态", syncType);
            }
            
            Map<String, Object> extraData = new HashMap<>();
            extraData.put("syncType", syncType);
            extraData.put("syncStatus", syncStatus);
            extraData.put("anomalyCount", anomalyCount);
            extraData.put("totalItems", totalItems);
            extraData.put("syncTime", System.currentTimeMillis() / 1000);
            
            return createBroadcastNotification(notificationType, title, content, extraData);
            
        } catch (Exception e) {
            log.error("发送数据同步通知失败", e);
            return false;
        }
    }

    @Override
    public boolean sendPriceAnomalyNotification(Integer anomalyCount, Integer severityLevel, List<Long> affectedItems) {
        try {
            String title = "价格异常提醒";
            String content;
            String severityText;
            
            switch (severityLevel != null ? severityLevel : 1) {
                case 3:
                    severityText = "严重";
                    break;
                case 2:
                    severityText = "中等";
                    break;
                default:
                    severityText = "轻微";
                    break;
            }
            
            content = String.format("检测到 %d 个%s价格异常商品，请及时处理", 
                anomalyCount != null ? anomalyCount : 0, severityText);
            
            Map<String, Object> extraData = new HashMap<>();
            extraData.put("anomalyCount", anomalyCount);
            extraData.put("severityLevel", severityLevel);
            extraData.put("severityText", severityText);
            extraData.put("affectedItems", affectedItems);
            extraData.put("alertTime", System.currentTimeMillis() / 1000);
            
            return createBroadcastNotification("price_anomaly", title, content, extraData);
            
        } catch (Exception e) {
            log.error("发送价格异常通知失败", e);
            return false;
        }
    }

    @Override
    public boolean sendSystemAlertNotification(String title, String content, Map<String, Object> extraData) {
        try {
            if (StringUtils.isEmpty(title)) {
                title = "系统提醒";
            }
            
            if (extraData == null) {
                extraData = new HashMap<>();
            }
            extraData.put("alertTime", System.currentTimeMillis() / 1000);
            
            return createBroadcastNotification("system_alert", title, content, extraData);
            
        } catch (Exception e) {
            log.error("发送系统提醒通知失败", e);
            return false;
        }
    }
}
