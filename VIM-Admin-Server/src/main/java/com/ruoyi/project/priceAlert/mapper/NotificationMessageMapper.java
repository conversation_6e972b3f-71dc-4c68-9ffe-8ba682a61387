package com.ruoyi.project.priceAlert.mapper;

import com.ruoyi.project.priceAlert.domain.NotificationMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Mapper
public interface NotificationMessageMapper {

    /**
     * 查询通知消息列表
     * 
     * @param notificationMessage 通知消息
     * @return 通知消息集合
     */
    List<NotificationMessage> selectNotificationMessageList(NotificationMessage notificationMessage);

    /**
     * 根据用户ID查询通知消息列表
     * 
     * @param userId 用户ID
     * @param readStatus 阅读状态（可选）
     * @return 通知消息集合
     */
    List<NotificationMessage> selectNotificationMessageByUserId(@Param("userId") Long userId, 
                                                               @Param("readStatus") Integer readStatus);

    /**
     * 查询未读通知消息列表
     * 
     * @param userId 用户ID
     * @return 未读通知消息集合
     */
    List<NotificationMessage> selectUnreadNotificationMessages(@Param("userId") Long userId);

    /**
     * 根据ID查询通知消息
     * 
     * @param id 通知消息主键
     * @return 通知消息
     */
    NotificationMessage selectNotificationMessageById(Long id);

    /**
     * 新增通知消息
     * 
     * @param notificationMessage 通知消息
     * @return 结果
     */
    int insertNotificationMessage(NotificationMessage notificationMessage);

    /**
     * 修改通知消息
     * 
     * @param notificationMessage 通知消息
     * @return 结果
     */
    int updateNotificationMessage(NotificationMessage notificationMessage);

    /**
     * 标记通知为已读
     * 
     * @param id 通知消息主键
     * @return 结果
     */
    int markNotificationAsRead(@Param("id") Long id);

    /**
     * 批量标记通知为已读
     * 
     * @param ids 通知消息主键集合
     * @return 结果
     */
    int batchMarkNotificationsAsRead(@Param("ids") Long[] ids);

    /**
     * 根据用户ID标记所有通知为已读
     * 
     * @param userId 用户ID
     * @return 结果
     */
    int markAllNotificationsAsReadByUserId(@Param("userId") Long userId);

    /**
     * 删除通知消息
     * 
     * @param id 通知消息主键
     * @return 结果
     */
    int deleteNotificationMessageById(Long id);

    /**
     * 批量删除通知消息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteNotificationMessageByIds(Long[] ids);

    /**
     * 统计用户未读通知数量
     * 
     * @param userId 用户ID
     * @return 未读通知数量
     */
    int countUnreadNotificationsByUserId(@Param("userId") Long userId);

    /**
     * 统计通知消息总数
     * 
     * @return 通知消息总数
     */
    int countTotalNotifications();

    /**
     * 根据通知类型统计数量
     * 
     * @param notificationType 通知类型
     * @return 通知数量
     */
    int countNotificationsByType(@Param("notificationType") String notificationType);

    /**
     * 清理过期的通知消息
     * 
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanExpiredNotifications(@Param("retentionDays") int retentionDays);

    /**
     * 获取最近的通知消息
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近的通知消息列表
     */
    List<NotificationMessage> selectRecentNotifications(@Param("userId") Long userId, 
                                                        @Param("limit") int limit);
}
