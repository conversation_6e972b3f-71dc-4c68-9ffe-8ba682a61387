<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.priceAlert.mapper.DataSyncStatusMapper">
    
    <resultMap type="DataSyncStatus" id="DataSyncStatusResult">
        <result property="id"                    column="id" />
        <result property="syncType"              column="sync_type" />
        <result property="lastSyncTime"          column="last_sync_time" />
        <result property="lastDataUpdate"        column="last_data_update" />
        <result property="syncStatus"            column="sync_status" />
        <result property="anomalyCount"          column="anomaly_count" />
        <result property="totalItems"            column="total_items" />
        <result property="syncDuration"          column="sync_duration" />
        <result property="errorMessage"          column="error_message" />
        <result property="createTimeStamp"       column="create_time" />
        <result property="updateTimeStamp"       column="update_time" />
    </resultMap>

    <sql id="selectDataSyncStatusVo">
        select id, sync_type, last_sync_time, last_data_update, sync_status, 
               anomaly_count, total_items, sync_duration, error_message, 
               create_time, update_time
        from vim_data_sync_status
    </sql>

    <select id="selectDataSyncStatusList" parameterType="DataSyncStatus" resultMap="DataSyncStatusResult">
        <include refid="selectDataSyncStatusVo"/>
        <where>  
            <if test="syncType != null and syncType != ''"> and sync_type = #{syncType}</if>
            <if test="syncStatus != null"> and sync_status = #{syncStatus}</if>
            <if test="lastSyncTime != null"> and last_sync_time >= #{lastSyncTime}</if>
        </where>
        order by update_time desc
    </select>
    
    <select id="selectDataSyncStatusBySyncType" parameterType="String" resultMap="DataSyncStatusResult">
        <include refid="selectDataSyncStatusVo"/>
        where sync_type = #{syncType}
        order by update_time desc
        limit 1
    </select>

    <select id="selectLatestDataSyncStatus" resultMap="DataSyncStatusResult">
        <include refid="selectDataSyncStatusVo"/>
        order by update_time desc
        limit 10
    </select>

    <insert id="insertDataSyncStatus" parameterType="DataSyncStatus" useGeneratedKeys="true" keyProperty="id">
        insert into vim_data_sync_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="syncType != null and syncType != ''">sync_type,</if>
            <if test="lastSyncTime != null">last_sync_time,</if>
            <if test="lastDataUpdate != null">last_data_update,</if>
            <if test="syncStatus != null">sync_status,</if>
            <if test="anomalyCount != null">anomaly_count,</if>
            <if test="totalItems != null">total_items,</if>
            <if test="syncDuration != null">sync_duration,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="createTimeStamp != null">create_time,</if>
            <if test="updateTimeStamp != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="syncType != null and syncType != ''">#{syncType},</if>
            <if test="lastSyncTime != null">#{lastSyncTime},</if>
            <if test="lastDataUpdate != null">#{lastDataUpdate},</if>
            <if test="syncStatus != null">#{syncStatus},</if>
            <if test="anomalyCount != null">#{anomalyCount},</if>
            <if test="totalItems != null">#{totalItems},</if>
            <if test="syncDuration != null">#{syncDuration},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="createTimeStamp != null">#{createTimeStamp},</if>
            <if test="updateTimeStamp != null">#{updateTimeStamp},</if>
         </trim>
    </insert>

    <update id="updateDataSyncStatus" parameterType="DataSyncStatus">
        update vim_data_sync_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="syncType != null and syncType != ''">sync_type = #{syncType},</if>
            <if test="lastSyncTime != null">last_sync_time = #{lastSyncTime},</if>
            <if test="lastDataUpdate != null">last_data_update = #{lastDataUpdate},</if>
            <if test="syncStatus != null">sync_status = #{syncStatus},</if>
            <if test="anomalyCount != null">anomaly_count = #{anomalyCount},</if>
            <if test="totalItems != null">total_items = #{totalItems},</if>
            <if test="syncDuration != null">sync_duration = #{syncDuration},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="updateTimeStamp != null">update_time = #{updateTimeStamp},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateDataSyncStatusBySyncType" parameterType="DataSyncStatus">
        update vim_data_sync_status
        <trim prefix="SET" suffixOverrides=",">
            <if test="lastSyncTime != null">last_sync_time = #{lastSyncTime},</if>
            <if test="lastDataUpdate != null">last_data_update = #{lastDataUpdate},</if>
            <if test="syncStatus != null">sync_status = #{syncStatus},</if>
            <if test="anomalyCount != null">anomaly_count = #{anomalyCount},</if>
            <if test="totalItems != null">total_items = #{totalItems},</if>
            <if test="syncDuration != null">sync_duration = #{syncDuration},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="updateTimeStamp != null">update_time = #{updateTimeStamp},</if>
        </trim>
        where sync_type = #{syncType}
    </update>

    <delete id="deleteDataSyncStatusById" parameterType="Long">
        delete from vim_data_sync_status where id = #{id}
    </delete>

    <delete id="deleteDataSyncStatusByIds" parameterType="String">
        delete from vim_data_sync_status where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 从skin_goods表获取最后的数据更新时间 -->
    <select id="getLastDataUpdateFromSkinGoods" resultType="Long">
        SELECT UNIX_TIMESTAMP(MAX(last_price_update)) 
        FROM skin_goods 
        WHERE deleted = 0 AND status = 1
    </select>

    <!-- 统计异常商品数量 -->
    <select id="countAnomalyItems" resultType="int">
        SELECT COUNT(*)
        FROM vim_item
        WHERE delete_time IS NULL
            AND price_cost > price_recycle
    </select>

    <!-- 统计总商品数量 -->
    <select id="countTotalItems" resultType="int">
        SELECT COUNT(*)
        FROM vim_item
        WHERE delete_time IS NULL
    </select>

    <!-- 检查是否有新的数据更新 -->
    <select id="hasNewDataUpdate" parameterType="Long" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM skin_goods 
        WHERE deleted = 0 AND status = 1
            AND UNIX_TIMESTAMP(last_price_update) > #{lastCheckTime}
    </select>

    <!-- 清理过期的同步状态记录 -->
    <delete id="cleanExpiredSyncStatus" parameterType="int">
        DELETE FROM vim_data_sync_status 
        WHERE create_time &lt; (UNIX_TIMESTAMP() - #{retentionDays} * 24 * 3600)
            AND sync_type NOT IN ('price_check', 'anomaly_scan')
    </delete>

</mapper>
