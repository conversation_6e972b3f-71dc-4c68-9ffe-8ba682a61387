<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.priceAlert.mapper.CostPriceAnomalyMapper">
    
    <resultMap type="VimCostPriceAnomalyDTO" id="CostPriceAnomalyResult">
        <result property="itemId"                column="item_id" />
        <result property="itemName"              column="item_name" />
        <result property="hashname"              column="hashname" />
        <result property="itemTag"               column="item_tag" />
        <result property="itemImage"             column="item_image" />
        <result property="priceCost"             column="price_cost" />
        <result property="priceRecycle"          column="price_recycle" />
        <result property="priceShow"             column="price_show" />
        <result property="priceBuy"              column="price_buy" />
        <result property="itemSale"              column="item_sale" />
        <result property="itemStock"             column="item_stock" />
        <result property="priceDifference"       column="price_difference" />
        <result property="differencePercentage"  column="difference_percentage" />
        <result property="createTime"            column="create_time" />
        <result property="updateTime"            column="update_time" />
        <result property="boxName"               column="box_name" />
        <result property="boxId"                 column="box_id" />
        <result property="referencePrice"        column="reference_price" />
        <result property="severityLevel"         column="severity_level" />
        <result property="severityText"          column="severity_text" />
    </resultMap>

    <sql id="selectCostPriceAnomalyVo">
        SELECT 
            vi.id as item_id,
            vi.name as item_name,
            vi.hashname,
            vi.tag as item_tag,
            vi.image as item_image,
            vi.price_cost,
            vi.price_recycle,
            vi.price_show,
            vi.price_buy,
            vi.sale as item_sale,
            vi.stock as item_stock,
            (vi.price_cost - vi.price_recycle) as price_difference,
            CASE
                WHEN vi.price_recycle > 0 THEN
                    ROUND(((vi.price_cost - vi.price_recycle) / vi.price_recycle) * 100, 2)
                ELSE 0
            END as difference_percentage,
            vi.create_time,
            vi.update_time,
            vb.name as box_name,
            vb.id as box_id,
            CASE
                WHEN ((vi.price_cost - vi.price_recycle) / vi.price_recycle) * 100 > 50 
                     OR (vi.price_cost - vi.price_recycle) > 100 THEN 3
                WHEN ((vi.price_cost - vi.price_recycle) / vi.price_recycle) * 100 > 20 
                     OR (vi.price_cost - vi.price_recycle) > 50 THEN 2
                ELSE 1
            END as severity_level
        FROM vim_item vi
        LEFT JOIN vim_box_item vbi ON vi.id = vbi.id_item
        LEFT JOIN vim_box vb ON vbi.id_box = vb.id AND vb.delete_time IS NULL
        WHERE vi.delete_time IS NULL
            AND vi.price_cost > vi.price_recycle
    </sql>

    <select id="selectCostPriceAnomalyList" parameterType="map" resultMap="CostPriceAnomalyResult">
        <include refid="selectCostPriceAnomalyVo"/>
        <if test="itemName != null and itemName != ''">
            AND vi.name LIKE CONCAT('%', #{itemName}, '%')
        </if>
        <if test="boxName != null and boxName != ''">
            AND vb.name LIKE CONCAT('%', #{boxName}, '%')
        </if>
        <if test="itemTag != null and itemTag != ''">
            AND vi.tag LIKE CONCAT('%', #{itemTag}, '%')
        </if>
        <if test="saleStatus != null">
            AND vi.sale = #{saleStatus}
        </if>
        <if test="minCost != null">
            AND vi.price_cost >= #{minCost}
        </if>
        <if test="maxCost != null">
            AND vi.price_cost &lt;= #{maxCost}
        </if>
        <if test="minRecycle != null">
            AND vi.price_recycle >= #{minRecycle}
        </if>
        <if test="maxRecycle != null">
            AND vi.price_recycle &lt;= #{maxRecycle}
        </if>
        <if test="minDifference != null">
            AND (vi.price_cost - vi.price_recycle) >= #{minDifference}
        </if>
        <if test="maxDifference != null">
            AND (vi.price_cost - vi.price_recycle) &lt;= #{maxDifference}
        </if>
        <if test="minPercentage != null">
            AND ((vi.price_cost - vi.price_recycle) / vi.price_recycle) * 100 >= #{minPercentage}
        </if>
        <if test="maxPercentage != null">
            AND ((vi.price_cost - vi.price_recycle) / vi.price_recycle) * 100 &lt;= #{maxPercentage}
        </if>
        <if test="stockStatus != null">
            <choose>
                <when test="stockStatus == 1">AND vi.stock > 0</when>
                <when test="stockStatus == 0">AND vi.stock = 0</when>
            </choose>
        </if>
        <if test="severityLevel != null">
            AND (
                CASE
                    WHEN ((vi.price_cost - vi.price_recycle) / vi.price_recycle) * 100 > 50 
                         OR (vi.price_cost - vi.price_recycle) > 100 THEN 3
                    WHEN ((vi.price_cost - vi.price_recycle) / vi.price_recycle) * 100 > 20 
                         OR (vi.price_cost - vi.price_recycle) > 50 THEN 2
                    ELSE 1
                END
            ) = #{severityLevel}
        </if>
        <choose>
            <when test="sortField == 'priceDifference'">
                ORDER BY price_difference DESC
            </when>
            <when test="sortField == 'differencePercentage'">
                ORDER BY difference_percentage DESC
            </when>
            <when test="sortField == 'priceCost'">
                ORDER BY vi.price_cost DESC
            </when>
            <when test="sortField == 'priceRecycle'">
                ORDER BY vi.price_recycle DESC
            </when>
            <when test="sortField == 'updateTime'">
                ORDER BY vi.update_time DESC
            </when>
            <otherwise>
                ORDER BY price_difference DESC, vi.update_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="countCostPriceAnomalyItems" resultType="int">
        SELECT COUNT(*)
        FROM vim_item
        WHERE delete_time IS NULL
            AND price_cost > price_recycle
    </select>

    <select id="countTotalItems" resultType="int">
        SELECT COUNT(*)
        FROM vim_item
        WHERE delete_time IS NULL
    </select>

    <select id="getAnomalyItemById" parameterType="Long" resultMap="CostPriceAnomalyResult">
        <include refid="selectCostPriceAnomalyVo"/>
        AND vi.id = #{itemId}
        LIMIT 1
    </select>

    <select id="getAnomalyStatisticsBySeverity" resultType="map">
        SELECT 
            CASE
                WHEN ((price_cost - price_recycle) / price_recycle) * 100 > 50 
                     OR (price_cost - price_recycle) > 100 THEN 3
                WHEN ((price_cost - price_recycle) / price_recycle) * 100 > 20 
                     OR (price_cost - price_recycle) > 50 THEN 2
                ELSE 1
            END as severity_level,
            COUNT(*) as count
        FROM vim_item
        WHERE delete_time IS NULL
            AND price_cost > price_recycle
        GROUP BY severity_level
        ORDER BY severity_level
    </select>

    <select id="getPriceRangeStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_count,
            AVG(price_cost - price_recycle) as avg_difference,
            MIN(price_cost - price_recycle) as min_difference,
            MAX(price_cost - price_recycle) as max_difference,
            AVG(((price_cost - price_recycle) / price_recycle) * 100) as avg_percentage,
            MIN(((price_cost - price_recycle) / price_recycle) * 100) as min_percentage,
            MAX(((price_cost - price_recycle) / price_recycle) * 100) as max_percentage
        FROM vim_item
        WHERE delete_time IS NULL
            AND price_cost > price_recycle
    </select>

    <select id="getAnomalyTrendData" parameterType="int" resultType="map">
        SELECT 
            DATE(FROM_UNIXTIME(update_time)) as date,
            COUNT(*) as anomaly_count,
            AVG(price_cost - price_recycle) as avg_difference
        FROM vim_item
        WHERE delete_time IS NULL
            AND price_cost > price_recycle
            AND update_time >= (UNIX_TIMESTAMP() - #{days} * 24 * 3600)
        GROUP BY DATE(FROM_UNIXTIME(update_time))
        ORDER BY date DESC
    </select>

    <select id="getAnomalyDistributionByTag" resultType="map">
        SELECT 
            tag as item_tag,
            COUNT(*) as count,
            AVG(price_cost - price_recycle) as avg_difference,
            AVG(((price_cost - price_recycle) / price_recycle) * 100) as avg_percentage
        FROM vim_item
        WHERE delete_time IS NULL
            AND price_cost > price_recycle
            AND tag IS NOT NULL
            AND tag != ''
        GROUP BY tag
        ORDER BY count DESC
        LIMIT 20
    </select>

    <select id="detectNewAnomalyItems" parameterType="map" resultMap="CostPriceAnomalyResult">
        <include refid="selectCostPriceAnomalyVo"/>
        <if test="lastCheckTime != null">
            AND vi.update_time > #{lastCheckTime}
        </if>
        ORDER BY vi.update_time DESC
    </select>

    <select id="batchCheckAnomalyStatus" parameterType="list" resultType="map">
        SELECT 
            id as item_id,
            name as item_name,
            price_cost,
            price_recycle,
            (price_cost - price_recycle) as price_difference,
            CASE
                WHEN price_recycle > 0 THEN
                    ROUND(((price_cost - price_recycle) / price_recycle) * 100, 2)
                ELSE 0
            END as difference_percentage,
            CASE
                WHEN price_cost > price_recycle THEN 1
                ELSE 0
            END as is_anomaly
        FROM vim_item
        WHERE delete_time IS NULL
            AND id IN
        <foreach item="itemId" collection="list" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>

    <select id="getAnomalyItemBoxRelations" parameterType="Long" resultType="map">
        SELECT 
            vb.id as box_id,
            vb.name as box_name,
            vb.price as box_price,
            vbi.probability,
            vbi.create_time as relation_create_time
        FROM vim_box_item vbi
        JOIN vim_box vb ON vbi.id_box = vb.id AND vb.delete_time IS NULL
        WHERE vbi.id_item = #{itemId}
        ORDER BY vbi.create_time DESC
    </select>

    <select id="getAnomalyStockDistribution" resultType="map">
        SELECT 
            CASE
                WHEN stock = 0 THEN '无库存'
                WHEN stock BETWEEN 1 AND 10 THEN '1-10'
                WHEN stock BETWEEN 11 AND 50 THEN '11-50'
                WHEN stock BETWEEN 51 AND 100 THEN '51-100'
                ELSE '100+'
            END as stock_range,
            COUNT(*) as count
        FROM vim_item
        WHERE delete_time IS NULL
            AND price_cost > price_recycle
        GROUP BY stock_range
        ORDER BY 
            CASE stock_range
                WHEN '无库存' THEN 1
                WHEN '1-10' THEN 2
                WHEN '11-50' THEN 3
                WHEN '51-100' THEN 4
                WHEN '100+' THEN 5
            END
    </select>

    <select id="getAnomalySaleStatusDistribution" resultType="map">
        SELECT 
            CASE sale
                WHEN 1 THEN '在售'
                WHEN 0 THEN '下架'
                ELSE '未知'
            END as sale_status,
            COUNT(*) as count
        FROM vim_item
        WHERE delete_time IS NULL
            AND price_cost > price_recycle
        GROUP BY sale
    </select>

</mapper>
