<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.priceAlert.mapper.NotificationMessageMapper">
    
    <resultMap type="NotificationMessage" id="NotificationMessageResult">
        <result property="id"                    column="id" />
        <result property="notificationType"      column="notification_type" />
        <result property="title"                 column="title" />
        <result property="content"               column="content" />
        <result property="targetUsers"           column="target_users" />
        <result property="sendStatus"            column="send_status" />
        <result property="readStatus"            column="read_status" />
        <result property="extraData"             column="extra_data" />
        <result property="createTimeStamp"       column="create_time" />
    </resultMap>

    <sql id="selectNotificationMessageVo">
        select id, notification_type, title, content, target_users, 
               send_status, read_status, extra_data, create_time
        from vim_notification_log
    </sql>

    <select id="selectNotificationMessageList" parameterType="NotificationMessage" resultMap="NotificationMessageResult">
        <include refid="selectNotificationMessageVo"/>
        <where>  
            <if test="notificationType != null and notificationType != ''"> and notification_type = #{notificationType}</if>
            <if test="title != null and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="sendStatus != null"> and send_status = #{sendStatus}</if>
            <if test="readStatus != null"> and read_status = #{readStatus}</if>
            <if test="createTimeStamp != null"> and create_time >= #{createTimeStamp}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectNotificationMessageByUserId" resultMap="NotificationMessageResult">
        <include refid="selectNotificationMessageVo"/>
        where (target_users = 'all' or JSON_CONTAINS(target_users, #{userId}, '$'))
        <if test="readStatus != null"> and read_status = #{readStatus}</if>
        order by create_time desc
    </select>

    <select id="selectUnreadNotificationMessages" parameterType="Long" resultMap="NotificationMessageResult">
        <include refid="selectNotificationMessageVo"/>
        where (target_users = 'all' or JSON_CONTAINS(target_users, #{userId}, '$'))
            and read_status = 0
        order by create_time desc
    </select>
    
    <select id="selectNotificationMessageById" parameterType="Long" resultMap="NotificationMessageResult">
        <include refid="selectNotificationMessageVo"/>
        where id = #{id}
    </select>

    <insert id="insertNotificationMessage" parameterType="NotificationMessage" useGeneratedKeys="true" keyProperty="id">
        insert into vim_notification_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="notificationType != null and notificationType != ''">notification_type,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="targetUsers != null">target_users,</if>
            <if test="sendStatus != null">send_status,</if>
            <if test="readStatus != null">read_status,</if>
            <if test="extraData != null">extra_data,</if>
            <if test="createTimeStamp != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="notificationType != null and notificationType != ''">#{notificationType},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="targetUsers != null">#{targetUsers},</if>
            <if test="sendStatus != null">#{sendStatus},</if>
            <if test="readStatus != null">#{readStatus},</if>
            <if test="extraData != null">#{extraData},</if>
            <if test="createTimeStamp != null">#{createTimeStamp},</if>
         </trim>
    </insert>

    <update id="updateNotificationMessage" parameterType="NotificationMessage">
        update vim_notification_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="notificationType != null and notificationType != ''">notification_type = #{notificationType},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="targetUsers != null">target_users = #{targetUsers},</if>
            <if test="sendStatus != null">send_status = #{sendStatus},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
            <if test="extraData != null">extra_data = #{extraData},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="markNotificationAsRead" parameterType="Long">
        update vim_notification_log set read_status = 1 where id = #{id}
    </update>

    <update id="batchMarkNotificationsAsRead" parameterType="String">
        update vim_notification_log set read_status = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="markAllNotificationsAsReadByUserId" parameterType="Long">
        update vim_notification_log 
        set read_status = 1 
        where (target_users = 'all' or JSON_CONTAINS(target_users, #{userId}, '$'))
            and read_status = 0
    </update>

    <delete id="deleteNotificationMessageById" parameterType="Long">
        delete from vim_notification_log where id = #{id}
    </delete>

    <delete id="deleteNotificationMessageByIds" parameterType="String">
        delete from vim_notification_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countUnreadNotificationsByUserId" parameterType="Long" resultType="int">
        SELECT COUNT(*) 
        FROM vim_notification_log 
        WHERE (target_users = 'all' or JSON_CONTAINS(target_users, #{userId}, '$'))
            AND read_status = 0
    </select>

    <select id="countTotalNotifications" resultType="int">
        SELECT COUNT(*) FROM vim_notification_log
    </select>

    <select id="countNotificationsByType" parameterType="String" resultType="int">
        SELECT COUNT(*) 
        FROM vim_notification_log 
        WHERE notification_type = #{notificationType}
    </select>

    <delete id="cleanExpiredNotifications" parameterType="int">
        DELETE FROM vim_notification_log 
        WHERE create_time &lt; (UNIX_TIMESTAMP() - #{retentionDays} * 24 * 3600)
    </delete>

    <select id="selectRecentNotifications" resultMap="NotificationMessageResult">
        <include refid="selectNotificationMessageVo"/>
        where (target_users = 'all' or JSON_CONTAINS(target_users, #{userId}, '$'))
        order by create_time desc
        limit #{limit}
    </select>

</mapper>
