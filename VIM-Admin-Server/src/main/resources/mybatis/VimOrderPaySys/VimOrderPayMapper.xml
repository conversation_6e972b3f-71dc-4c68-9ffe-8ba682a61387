<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.VimOrderPaySys.mapper.VimOrderPayMapper">
    
    <resultMap type="VimOrderPay" id="VimOrderPayResult">
        <result property="id"    column="id"    />
        <result property="uid"    column="uid"    />
        <result property="amount"    column="amount"    />
        <result property="balance"    column="balance"    />
        <result property="time"    column="time"    />
        <result property="info"    column="info"    />
    </resultMap>

    <!-- 用户电能消费扩展结果映射，包含用户信息 -->
    <resultMap type="com.ruoyi.project.VimOrderPaySys.domain.vo.VimOrderPayVO" id="VimOrderPayVOResult" extends="VimOrderPayResult">
        <result property="userNickname"    column="user_nickname"    />
        <result property="userPhoneRaw"    column="user_phone"    />
    </resultMap>

    <sql id="selectVimOrderPayVo">
        select id, uid, amount, balance, time, info from vim_order_pay
    </sql>

    <!-- 包含用户信息的用户电能消费查询SQL -->
    <sql id="selectVimOrderPayWithUserVo">
        SELECT
            vop.id,
            vop.uid,
            vop.amount,
            vop.balance,
            vop.time,
            vop.info,
            vu.nickname AS user_nickname,
            vu.phone AS user_phone
        FROM
            vim_order_pay vop
                LEFT JOIN vim_user vu ON vop.uid = vu.id
    </sql>

    <select id="selectVimOrderPayList" parameterType="VimOrderPay" resultMap="VimOrderPayResult">
        <include refid="selectVimOrderPayVo"/>
        <where>
            <if test="id != null and id != ''"> and id like concat('%', #{id}, '%')</if>
            <if test="uid != null "> and uid = #{uid}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="time != null "> and time = #{time}</if>
            <if test="info != null  and info != ''"> and info like concat('%', #{info}, '%')</if>
        </where>
        order by time desc
    </select>

    <!-- 查询用户电能消费列表（包含用户信息） -->
    <select id="selectVimOrderPayWithUserList" parameterType="VimOrderPay" resultMap="VimOrderPayVOResult">
        <include refid="selectVimOrderPayWithUserVo"/>
        <where>
            <if test="id != null and id != ''"> and vop.id like concat('%', #{id}, '%')</if>
            <if test="uid != null "> and vop.uid = #{uid}</if>
            <if test="amount != null "> and vop.amount = #{amount}</if>
            <if test="balance != null "> and vop.balance = #{balance}</if>
            <if test="time != null "> and vop.time = #{time}</if>
            <if test="info != null  and info != ''"> and vop.info like concat('%', #{info}, '%')</if>
        </where>
        order by vop.time desc
    </select>
    
    <select id="selectVimOrderPayById" parameterType="String" resultMap="VimOrderPayResult">
        <include refid="selectVimOrderPayVo"/>
        where id = #{id}
    </select>

    <insert id="insertVimOrderPay" parameterType="VimOrderPay">
        insert into vim_order_pay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="uid != null">uid,</if>
            <if test="amount != null">amount,</if>
            <if test="balance != null">balance,</if>
            <if test="time != null">time,</if>
            <if test="info != null">info,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="uid != null">#{uid},</if>
            <if test="amount != null">#{amount},</if>
            <if test="balance != null">#{balance},</if>
            <if test="time != null">#{time},</if>
            <if test="info != null">#{info},</if>
         </trim>
    </insert>

    <update id="updateVimOrderPay" parameterType="VimOrderPay">
        update vim_order_pay
        <trim prefix="SET" suffixOverrides=",">
            <if test="uid != null">uid = #{uid},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="time != null">time = #{time},</if>
            <if test="info != null">info = #{info},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVimOrderPayById" parameterType="String">
        delete from vim_order_pay where id = #{id}
    </delete>

    <delete id="deleteVimOrderPayByIds" parameterType="String">
        delete from vim_order_pay where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
