-- ========================================
-- VIM系统成本异常商品筛选功能性能优化索引
-- 创建时间：2025-01-30
-- 说明：为成本异常商品筛选功能添加必要的数据库索引
-- ========================================

-- 1. 成本价和回收价复合索引（用于价格范围筛选）
CREATE INDEX idx_vim_item_cost_recycle_delete 
ON vim_item (delete_time, price_cost, price_recycle);

-- 2. 商品状态和库存复合索引（用于状态筛选）
CREATE INDEX idx_vim_item_sale_stock_delete 
ON vim_item (delete_time, sale, stock);

-- 3. 商品名称和标签复合索引（用于文本搜索）
CREATE INDEX idx_vim_item_name_tag_delete 
ON vim_item (delete_time, name(50), tag(50));

-- 4. 盲盒关联查询优化索引
CREATE INDEX idx_vim_box_item_item_box 
ON vim_box_item (id_item, id_box);

-- 5. 盲盒状态索引（用于盲盒筛选）
CREATE INDEX idx_vim_box_sale_delete_name 
ON vim_box (sale, delete_time, name(50));

-- 6. 价格差异计算优化索引（覆盖索引）
CREATE INDEX idx_vim_item_price_analysis 
ON vim_item (delete_time, price_cost, price_recycle, sale, stock, name(50), tag(50));

-- ========================================
-- 查询性能分析建议
-- ========================================

-- 使用以下SQL分析查询性能：
-- EXPLAIN SELECT ... FROM vim_item WHERE ...;

-- 监控慢查询日志：
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 2;

-- ========================================
-- 索引使用说明
-- ========================================

/*
1. idx_vim_item_cost_recycle_delete
   - 用途：价格范围筛选查询
   - 覆盖条件：WHERE delete_time IS NULL AND price_cost BETWEEN ? AND ? AND price_recycle BETWEEN ? AND ?

2. idx_vim_item_sale_stock_delete
   - 用途：商品状态和库存筛选
   - 覆盖条件：WHERE delete_time IS NULL AND sale = ? AND stock > 0

3. idx_vim_item_name_tag_delete
   - 用途：商品名称和标签模糊搜索
   - 覆盖条件：WHERE delete_time IS NULL AND name LIKE '%?%' AND tag LIKE '%?%'

4. idx_vim_box_item_item_box
   - 用途：盲盒商品关联查询优化
   - 覆盖条件：JOIN vim_box_item ON id_item = ? AND id_box = ?

5. idx_vim_box_sale_delete_name
   - 用途：盲盒名称筛选查询
   - 覆盖条件：WHERE sale = 1 AND delete_time IS NULL AND name LIKE '%?%'

6. idx_vim_item_price_analysis
   - 用途：综合查询覆盖索引，减少回表查询
   - 覆盖字段：所有常用查询字段
*/

-- ========================================
-- 性能监控查询
-- ========================================

-- 1. 检查索引使用情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'vim_item'
    AND INDEX_NAME LIKE 'idx_vim_item%'
ORDER BY TABLE_NAME, INDEX_NAME;

-- 2. 分析表统计信息
ANALYZE TABLE vim_item;
ANALYZE TABLE vim_box;
ANALYZE TABLE vim_box_item;

-- 3. 检查表大小和索引大小
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'DB Size in MB',
    ROUND((DATA_LENGTH / 1024 / 1024), 2) AS 'Data Size in MB',
    ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS 'Index Size in MB'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN ('vim_item', 'vim_box', 'vim_box_item')
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- ========================================
-- 注意事项
-- ========================================

/*
1. 索引维护成本：
   - 新增索引会增加INSERT/UPDATE/DELETE的开销
   - 建议在业务低峰期创建索引
   - 定期监控索引使用情况，删除未使用的索引

2. 查询优化建议：
   - 避免在WHERE子句中使用函数
   - 合理使用LIMIT限制结果集大小
   - 考虑使用分页查询减少内存占用

3. 监控指标：
   - 查询响应时间应控制在500ms以内
   - 索引命中率应保持在95%以上
   - 慢查询日志应定期分析和优化
*/
