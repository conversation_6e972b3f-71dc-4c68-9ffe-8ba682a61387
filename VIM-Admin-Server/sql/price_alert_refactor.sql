-- ========================================
-- VIM系统预警模块重构SQL脚本
-- 创建时间: 2025-08-25
-- 说明: 删除旧的预警表，创建新的数据同步和通知表
-- ========================================

-- 删除旧的预警相关表
DROP TABLE IF EXISTS vim_price_alert_config;
DROP TABLE IF EXISTS vim_price_alert_record_simple;
DROP TABLE IF EXISTS vim_price_alert_execution_log;
DROP TABLE IF EXISTS vim_price_alert_system_config;
DROP TABLE IF EXISTS vim_price_snapshot;

-- 创建数据同步状态表
CREATE TABLE vim_data_sync_status (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    sync_type VARCHAR(50) NOT NULL COMMENT '同步类型：price_check,anomaly_scan',
    last_sync_time BIGINT NOT NULL COMMENT '最后同步时间戳（秒）',
    last_data_update BIGINT COMMENT '最后数据更新时间戳（从skin_goods获取）',
    sync_status TINYINT DEFAULT 1 COMMENT '同步状态：1=正常，2=异常',
    anomaly_count INT DEFAULT 0 COMMENT '异常商品数量',
    total_items INT DEFAULT 0 COMMENT '总商品数量',
    sync_duration INT DEFAULT 0 COMMENT '同步耗时（毫秒）',
    error_message TEXT COMMENT '错误信息',
    create_time BIGINT NOT NULL COMMENT '创建时间戳（秒）',
    update_time BIGINT NOT NULL COMMENT '更新时间戳（秒）',
    INDEX idx_sync_type (sync_type),
    INDEX idx_last_sync_time (last_sync_time),
    INDEX idx_sync_status (sync_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='数据同步状态表';

-- 创建通知记录表
CREATE TABLE vim_notification_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    notification_type VARCHAR(50) NOT NULL COMMENT '通知类型：data_sync,price_anomaly,system_alert',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT COMMENT '通知内容',
    target_users VARCHAR(500) COMMENT '目标用户ID列表（JSON格式）',
    send_status TINYINT DEFAULT 1 COMMENT '发送状态：1=成功，2=失败',
    read_status TINYINT DEFAULT 0 COMMENT '阅读状态：0=未读，1=已读',
    extra_data JSON COMMENT '额外数据（JSON格式）',
    create_time BIGINT NOT NULL COMMENT '创建时间戳（秒）',
    INDEX idx_notification_type (notification_type),
    INDEX idx_create_time (create_time),
    INDEX idx_read_status (read_status),
    INDEX idx_target_users (target_users(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知记录表';

-- 初始化数据同步状态记录
INSERT INTO vim_data_sync_status (
    sync_type, 
    last_sync_time, 
    last_data_update, 
    sync_status, 
    anomaly_count, 
    total_items, 
    sync_duration, 
    create_time, 
    update_time
) VALUES 
(
    'price_check', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    1, 
    0, 
    0, 
    0, 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP()
),
(
    'anomaly_scan', 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP(), 
    1, 
    0, 
    0, 
    0, 
    UNIX_TIMESTAMP(), 
    UNIX_TIMESTAMP()
);

-- 创建成本价异常商品视图（可选，用于快速查询）
CREATE OR REPLACE VIEW v_cost_price_anomaly AS
SELECT 
    vi.id as item_id,
    vi.name as item_name,
    vi.hashname as item_hashname,
    vi.tag as item_tag,
    vi.image as item_image,
    vi.price_cost,
    vi.price_recycle,
    vi.price_show,
    vi.price_buy,
    vi.sale as item_sale,
    vi.stock as item_stock,
    (vi.price_cost - vi.price_recycle) as price_difference,
    CASE
        WHEN vi.price_recycle > 0 THEN
            ROUND(((vi.price_cost - vi.price_recycle) / vi.price_recycle) * 100, 2)
        ELSE 0
    END as difference_percentage,
    vi.create_time,
    vi.update_time
FROM vim_item vi
WHERE vi.delete_time = 0 
    AND vi.price_cost > vi.price_recycle
ORDER BY price_difference DESC;
