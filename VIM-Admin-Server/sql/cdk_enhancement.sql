-- CDK管理功能增强SQL脚本
-- 添加info字段到vim_cdk表

-- 1. 为vim_cdk表添加info字段（如果字段已存在，先删除再重新创建）
-- ALTER TABLE `vim_cdk` DROP COLUMN IF EXISTS `info`;
ALTER TABLE `vim_cdk` ADD COLUMN `info` TEXT NULL DEFAULT NULL COMMENT 'CDK信息快照：JSON格式存储用户详细信息和CDK类型' AFTER `foruser`;

-- 如果字段已存在但类型不对，修改字段类型
-- ALTER TABLE `vim_cdk` MODIFY COLUMN `info` TEXT NULL DEFAULT NULL COMMENT 'CDK信息快照：JSON格式存储用户详细信息和CDK类型';

-- 2. 创建用户详细信息视图（用于CDK管理页面显示用户信息）
CREATE OR REPLACE VIEW `v_user_detail_info` AS
SELECT 
    u.id,
    u.nickname,
    u.username,
    u.phone,
    u.userimage,
    u.coin as balance,
    u.`key` as key_amount,
    COALESCE((
        SELECT SUM(i.price_show * item_counts.count)
        FROM (
            SELECT 
                ob.itemid,
                COUNT(*) as count
            FROM vim_order_box ob
            WHERE ob.uid = u.id 
            AND ob.state = 1
            GROUP BY ob.itemid
        ) item_counts
        JOIN vim_item i ON item_counts.itemid = i.id
        WHERE i.delete_time IS NULL
    ), 0) as backpack_value,
    u.level,
    u.exp,
    u.identity,
    u.state,
    u.create_time,
    u.last_login_time
FROM vim_user u
WHERE u.state = 1;

-- 3. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_vim_cdk_info` ON `vim_cdk` (`info`);
CREATE INDEX IF NOT EXISTS `idx_vim_order_box_uid_state` ON `vim_order_box` (`uid`, `state`);

-- 4. 插入测试数据（可选）
-- INSERT INTO vim_cdk (id, cdk, type, value, state, create_time, foruser, info) 
-- VALUES (999999, 'TEST-CDK-INFO-0001', 1, 100.00, 0, UNIX_TIMESTAMP(), 1, '幸运id');
