package com.ruoyi.project.priceAlert.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 性能监控工具类
 * 用于监控价格预警模块的性能指标
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class PerformanceMonitor {

    /**
     * 查询次数统计
     */
    private final ConcurrentHashMap<String, AtomicLong> queryCountMap = new ConcurrentHashMap<>();

    /**
     * 查询耗时统计
     */
    private final ConcurrentHashMap<String, AtomicLong> queryTimeMap = new ConcurrentHashMap<>();

    /**
     * 批量查询统计
     */
    private final ConcurrentHashMap<String, BatchQueryStats> batchQueryStatsMap = new ConcurrentHashMap<>();

    /**
     * 慢查询记录
     */
    private final ConcurrentHashMap<String, SlowQueryRecord> slowQueryMap = new ConcurrentHashMap<>();

    /**
     * 系统性能指标
     */
    private final AtomicReference<SystemMetrics> systemMetrics = new AtomicReference<>(new SystemMetrics());

    /**
     * 记录查询开始时间
     */
    public long startQuery(String queryType) {
        return System.currentTimeMillis();
    }

    /**
     * 记录查询结束时间并统计
     */
    public void endQuery(String queryType, long startTime, int recordCount) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 统计查询次数
        queryCountMap.computeIfAbsent(queryType, k -> new AtomicLong(0)).incrementAndGet();

        // 统计查询耗时
        queryTimeMap.computeIfAbsent(queryType, k -> new AtomicLong(0)).addAndGet(duration);

        // 记录批量查询统计
        if (recordCount > 1) {
            BatchQueryStats stats = batchQueryStatsMap.computeIfAbsent(queryType, k -> new BatchQueryStats());
            stats.addBatchQuery(recordCount, duration);
        }

        // 如果查询耗时超过阈值，记录警告日志
        // 5秒
        if (duration > 5000) { 
            log.warn("慢查询检测 - 查询类型: {}, 耗时: {}ms, 记录数: {}", queryType, duration, recordCount);
            // 1秒
        } else if (duration > 1000) {
            log.info("查询性能监控 - 查询类型: {}, 耗时: {}ms, 记录数: {}", queryType, duration, recordCount);
        }
    }

    /**
     * 获取查询统计信息
     */
    public QueryStats getQueryStats(String queryType) {
        AtomicLong count = queryCountMap.get(queryType);
        AtomicLong totalTime = queryTimeMap.get(queryType);
        BatchQueryStats batchStats = batchQueryStatsMap.get(queryType);

        QueryStats stats = new QueryStats();
        stats.setQueryType(queryType);
        stats.setTotalCount(count != null ? count.get() : 0);
        stats.setTotalTime(totalTime != null ? totalTime.get() : 0);
        stats.setAverageTime(stats.getTotalCount() > 0 ? stats.getTotalTime() / stats.getTotalCount() : 0);

        if (batchStats != null) {
            stats.setBatchQueryCount(batchStats.getBatchCount());
            stats.setTotalBatchRecords(batchStats.getTotalRecords());
            stats.setAverageBatchSize(batchStats.getAverageBatchSize());
            stats.setAverageBatchTime(batchStats.getAverageBatchTime());
        }

        return stats;
    }

    /**
     * 获取所有查询统计信息
     */
    public ConcurrentHashMap<String, QueryStats> getAllQueryStats() {
        ConcurrentHashMap<String, QueryStats> allStats = new ConcurrentHashMap<>();
        
        for (String queryType : queryCountMap.keySet()) {
            allStats.put(queryType, getQueryStats(queryType));
        }
        
        return allStats;
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        queryCountMap.clear();
        queryTimeMap.clear();
        batchQueryStatsMap.clear();
        log.info("性能监控统计信息已重置");
    }

    /**
     * 打印性能报告
     */
    public void printPerformanceReport() {
        log.info("========== 价格预警性能监控报告 ==========");
        
        ConcurrentHashMap<String, QueryStats> allStats = getAllQueryStats();
        
        for (QueryStats stats : allStats.values()) {
            log.info("查询类型: {}", stats.getQueryType());
            log.info("  总查询次数: {}", stats.getTotalCount());
            log.info("  总耗时: {}ms", stats.getTotalTime());
            log.info("  平均耗时: {}ms", stats.getAverageTime());
            
            if (stats.getBatchQueryCount() > 0) {
                log.info("  批量查询次数: {}", stats.getBatchQueryCount());
                log.info("  批量查询总记录数: {}", stats.getTotalBatchRecords());
                log.info("  平均批量大小: {}", stats.getAverageBatchSize());
                log.info("  平均批量耗时: {}ms", stats.getAverageBatchTime());
            }
            
            log.info("  ----------------------------------------");
        }
        
        log.info("========================================");
    }

    /**
     * 批量查询统计信息
     */
    private static class BatchQueryStats {
        private final AtomicLong batchCount = new AtomicLong(0);
        private final AtomicLong totalRecords = new AtomicLong(0);
        private final AtomicLong totalBatchTime = new AtomicLong(0);

        public void addBatchQuery(int recordCount, long duration) {
            batchCount.incrementAndGet();
            totalRecords.addAndGet(recordCount);
            totalBatchTime.addAndGet(duration);
        }

        public long getBatchCount() {
            return batchCount.get();
        }

        public long getTotalRecords() {
            return totalRecords.get();
        }

        public double getAverageBatchSize() {
            long count = batchCount.get();
            return count > 0 ? (double) totalRecords.get() / count : 0;
        }

        public double getAverageBatchTime() {
            long count = batchCount.get();
            return count > 0 ? (double) totalBatchTime.get() / count : 0;
        }
    }

    /**
     * 查询统计信息
     */
    public static class QueryStats {
        private String queryType;
        private long totalCount;
        private long totalTime;
        private long averageTime;
        private long batchQueryCount;
        private long totalBatchRecords;
        private double averageBatchSize;
        private double averageBatchTime;

        // Getters and Setters
        public String getQueryType() { return queryType; }
        public void setQueryType(String queryType) { this.queryType = queryType; }

        public long getTotalCount() { return totalCount; }
        public void setTotalCount(long totalCount) { this.totalCount = totalCount; }

        public long getTotalTime() { return totalTime; }
        public void setTotalTime(long totalTime) { this.totalTime = totalTime; }

        public long getAverageTime() { return averageTime; }
        public void setAverageTime(long averageTime) { this.averageTime = averageTime; }

        public long getBatchQueryCount() { return batchQueryCount; }
        public void setBatchQueryCount(long batchQueryCount) { this.batchQueryCount = batchQueryCount; }

        public long getTotalBatchRecords() { return totalBatchRecords; }
        public void setTotalBatchRecords(long totalBatchRecords) { this.totalBatchRecords = totalBatchRecords; }

        public double getAverageBatchSize() { return averageBatchSize; }
        public void setAverageBatchSize(double averageBatchSize) { this.averageBatchSize = averageBatchSize; }

        public double getAverageBatchTime() { return averageBatchTime; }
        public void setAverageBatchTime(double averageBatchTime) { this.averageBatchTime = averageBatchTime; }
    }

    /**
     * 记录慢查询
     */
    public void recordSlowQuery(String queryType, long duration, int recordCount, String details) {
        SlowQueryRecord record = new SlowQueryRecord();
        record.setQueryType(queryType);
        record.setDuration(duration);
        record.setRecordCount(recordCount);
        record.setDetails(details);
        record.setTimestamp(LocalDateTime.now());

        slowQueryMap.put(queryType + "_" + System.currentTimeMillis(), record);

        // 保持慢查询记录数量在合理范围内
        if (slowQueryMap.size() > 1000) {
            // 清理最旧的记录
            String oldestKey = slowQueryMap.keySet().iterator().next();
            slowQueryMap.remove(oldestKey);
        }
    }

    /**
     * 更新系统性能指标
     */
    public void updateSystemMetrics() {
        SystemMetrics metrics = systemMetrics.get();
        Runtime runtime = Runtime.getRuntime();

        metrics.setUsedMemory(runtime.totalMemory() - runtime.freeMemory());
        metrics.setTotalMemory(runtime.totalMemory());
        metrics.setMaxMemory(runtime.maxMemory());
        metrics.setFreeMemory(runtime.freeMemory());
        metrics.setAvailableProcessors(runtime.availableProcessors());
        metrics.setLastUpdateTime(LocalDateTime.now());

        // 计算内存使用率
        double memoryUsagePercent = (double) metrics.getUsedMemory() / metrics.getTotalMemory() * 100;
        metrics.setMemoryUsagePercent(memoryUsagePercent);

        // 如果内存使用率过高，记录警告
        if (memoryUsagePercent > 80) {
            log.warn("内存使用率过高: {:.2f}%, 已用内存: {}MB, 总内存: {}MB",
                memoryUsagePercent,
                metrics.getUsedMemory() / 1024 / 1024,
                metrics.getTotalMemory() / 1024 / 1024);
        }
    }

    /**
     * 获取慢查询统计
     */
    public Map<String, Object> getSlowQueryStats() {
        Map<String, Object> stats = new HashMap<>();

        List<SlowQueryRecord> recentSlowQueries = slowQueryMap.values().stream()
            .sorted((a, b) -> b.getTimestamp().compareTo(a.getTimestamp()))
            .limit(10)
            .collect(java.util.stream.Collectors.toList());

        stats.put("totalSlowQueries", slowQueryMap.size());
        stats.put("recentSlowQueries", recentSlowQueries);

        // 按查询类型分组统计
        Map<String, Long> slowQueryByType = slowQueryMap.values().stream()
            .collect(java.util.stream.Collectors.groupingBy(
                SlowQueryRecord::getQueryType,
                java.util.stream.Collectors.counting()
            ));

        stats.put("slowQueryByType", slowQueryByType);

        return stats;
    }

    /**
     * 获取系统性能指标
     */
    public SystemMetrics getSystemMetrics() {
        updateSystemMetrics();
        return systemMetrics.get();
    }

    /**
     * 慢查询记录类
     */
    public static class SlowQueryRecord {
        private String queryType;
        private long duration;
        private int recordCount;
        private String details;
        private LocalDateTime timestamp;

        public String getQueryType() { return queryType; }
        public void setQueryType(String queryType) { this.queryType = queryType; }
        public long getDuration() { return duration; }
        public void setDuration(long duration) { this.duration = duration; }
        public int getRecordCount() { return recordCount; }
        public void setRecordCount(int recordCount) { this.recordCount = recordCount; }
        public String getDetails() { return details; }
        public void setDetails(String details) { this.details = details; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }

        public String getFormattedTimestamp() {
            return timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        public String getFormattedDuration() {
            return duration + "ms";
        }
    }

    /**
     * 系统性能指标类
     */
    public static class SystemMetrics {
        private long usedMemory;
        private long totalMemory;
        private long maxMemory;
        private long freeMemory;
        private double memoryUsagePercent;
        private int availableProcessors;
        private LocalDateTime lastUpdateTime;

        // Getters and Setters
        public long getUsedMemory() { return usedMemory; }
        public void setUsedMemory(long usedMemory) { this.usedMemory = usedMemory; }
        public long getTotalMemory() { return totalMemory; }
        public void setTotalMemory(long totalMemory) { this.totalMemory = totalMemory; }
        public long getMaxMemory() { return maxMemory; }
        public void setMaxMemory(long maxMemory) { this.maxMemory = maxMemory; }
        public long getFreeMemory() { return freeMemory; }
        public void setFreeMemory(long freeMemory) { this.freeMemory = freeMemory; }
        public double getMemoryUsagePercent() { return memoryUsagePercent; }
        public void setMemoryUsagePercent(double memoryUsagePercent) { this.memoryUsagePercent = memoryUsagePercent; }
        public int getAvailableProcessors() { return availableProcessors; }
        public void setAvailableProcessors(int availableProcessors) { this.availableProcessors = availableProcessors; }
        public LocalDateTime getLastUpdateTime() { return lastUpdateTime; }
        public void setLastUpdateTime(LocalDateTime lastUpdateTime) { this.lastUpdateTime = lastUpdateTime; }

        public String getFormattedUsedMemory() { return formatBytes(usedMemory); }
        public String getFormattedTotalMemory() { return formatBytes(totalMemory); }
        public String getFormattedMaxMemory() { return formatBytes(maxMemory); }
        public String getFormattedFreeMemory() { return formatBytes(freeMemory); }

        private String formatBytes(long bytes) {
            if (bytes < 1024) {
                return bytes + " B";
            }
            if (bytes < 1024 * 1024) {
                return String.format("%.2f KB", bytes / 1024.0);
            }
            if (bytes < 1024 * 1024 * 1024) {
                return String.format("%.2f MB", bytes / (1024.0 * 1024));
            }
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }
}
