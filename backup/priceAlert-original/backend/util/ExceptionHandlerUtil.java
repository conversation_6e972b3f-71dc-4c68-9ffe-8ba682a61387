package com.ruoyi.project.priceAlert.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;

/**
 * 异常处理工具类
 * 
 * 统一处理价格预警模块的异常
 * 提供友好的错误信息和恢复建议
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class ExceptionHandlerUtil {

    /**
     * 处理数据库相关异常
     */
    public Map<String, Object> handleDatabaseException(Exception e, String operation) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        
        if (e instanceof SQLException) {
            return handleSQLException((SQLException) e, operation);
        } else if (e.getCause() instanceof SQLException) {
            return handleSQLException((SQLException) e.getCause(), operation);
        } else {
            result.put("message", "数据库操作失败: " + operation);
            result.put("error", e.getMessage());
            result.put("suggestion", "请检查数据库连接状态");
            
            log.error("数据库操作异常 - 操作: {}", operation, e);
        }
        
        return result;
    }

    /**
     * 处理SQL异常
     */
    private Map<String, Object> handleSQLException(SQLException e, String operation) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        
        String errorCode = String.valueOf(e.getErrorCode());
        String sqlState = e.getSQLState();
        
        switch (errorCode) {
            case "1045":
                result.put("message", "数据库认证失败");
                result.put("suggestion", "请检查数据库用户名和密码");
                break;
            case "1146":
                result.put("message", "数据表不存在");
                result.put("suggestion", "请检查数据表是否已创建");
                break;
            case "1062":
                result.put("message", "数据重复插入");
                result.put("suggestion", "请检查唯一约束字段");
                break;
            case "1205":
                result.put("message", "数据库锁等待超时");
                result.put("suggestion", "请稍后重试或检查长事务");
                break;
            default:
                result.put("message", "SQL执行失败: " + operation);
                result.put("suggestion", "请检查SQL语句和数据完整性");
                break;
        }
        
        result.put("errorCode", errorCode);
        result.put("sqlState", sqlState);
        result.put("error", e.getMessage());
        
        log.error("SQL异常 - 操作: {}, 错误码: {}, SQL状态: {}", operation, errorCode, sqlState, e);
        
        return result;
    }

    /**
     * 处理超时异常
     */
    public Map<String, Object> handleTimeoutException(TimeoutException e, String operation) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "操作超时: " + operation);
        result.put("error", e.getMessage());
        result.put("suggestion", "请检查网络连接或增加超时时间");
        
        log.error("操作超时异常 - 操作: {}", operation, e);
        
        return result;
    }

    /**
     * 处理并发异常
     */
    public Map<String, Object> handleConcurrencyException(Exception e, String operation) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "并发操作冲突: " + operation);
        result.put("error", e.getMessage());
        result.put("suggestion", "请稍后重试");
        
        log.error("并发异常 - 操作: {}", operation, e);
        
        return result;
    }

    /**
     * 处理价格计算异常
     */
    public Map<String, Object> handlePriceCalculationException(Exception e, String itemName) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "价格计算失败: " + itemName);
        result.put("error", e.getMessage());
        result.put("suggestion", "请检查价格数据的有效性");
        
        log.error("价格计算异常 - 商品: {}", itemName, e);
        
        return result;
    }

    /**
     * 处理网络连接异常
     */
    public Map<String, Object> handleNetworkException(Exception e, String operation) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "网络连接失败: " + operation);
        result.put("error", e.getMessage());
        result.put("suggestion", "请检查网络连接状态");
        
        log.error("网络连接异常 - 操作: {}", operation, e);
        
        return result;
    }

    /**
     * 处理配置异常
     */
    public Map<String, Object> handleConfigurationException(Exception e, String configName) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "配置错误: " + configName);
        result.put("error", e.getMessage());
        result.put("suggestion", "请检查配置文件的正确性");
        
        log.error("配置异常 - 配置: {}", configName, e);
        
        return result;
    }

    /**
     * 处理数据验证异常
     */
    public Map<String, Object> handleValidationException(Exception e, String fieldName) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "数据验证失败: " + fieldName);
        result.put("error", e.getMessage());
        result.put("suggestion", "请检查输入数据的格式和范围");
        
        log.error("数据验证异常 - 字段: {}", fieldName, e);
        
        return result;
    }

    /**
     * 处理通用异常
     */
    public Map<String, Object> handleGenericException(Exception e, String operation) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "操作失败: " + operation);
        result.put("error", e.getMessage());
        result.put("suggestion", "请联系系统管理员");
        
        log.error("通用异常 - 操作: {}", operation, e);
        
        return result;
    }

    /**
     * 创建成功结果
     */
    public Map<String, Object> createSuccessResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", message);
        return result;
    }

    /**
     * 创建成功结果（带数据）
     */
    public Map<String, Object> createSuccessResult(String message, Object data) {
        Map<String, Object> result = createSuccessResult(message);
        result.put("data", data);
        return result;
    }

    /**
     * 创建错误结果
     */
    public Map<String, Object> createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        return result;
    }

    /**
     * 创建错误结果（带建议）
     */
    public Map<String, Object> createErrorResult(String message, String suggestion) {
        Map<String, Object> result = createErrorResult(message);
        result.put("suggestion", suggestion);
        return result;
    }

    /**
     * 重试机制包装器
     */
    public <T> T executeWithRetry(RetryableOperation<T> operation, int maxRetries, long retryInterval) {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return operation.execute();
            } catch (Exception e) {
                lastException = e;
                
                if (attempt < maxRetries) {
                    log.warn("操作失败，第{}次重试，剩余重试次数: {}", attempt, maxRetries - attempt, e);
                    
                    try {
                        Thread.sleep(retryInterval);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                } else {
                    log.error("操作失败，已达到最大重试次数: {}", maxRetries, e);
                }
            }
        }
        
        throw new RuntimeException("操作失败，已达到最大重试次数", lastException);
    }

    /**
     * 可重试操作接口
     */
    @FunctionalInterface
    public interface RetryableOperation<T> {
        T execute() throws Exception;
    }

    /**
     * 安全执行操作（不抛出异常）
     */
    public <T> T safeExecute(RetryableOperation<T> operation, T defaultValue) {
        try {
            return operation.execute();
        } catch (Exception e) {
            log.warn("安全执行操作失败，返回默认值", e);
            return defaultValue;
        }
    }

    /**
     * 记录性能警告
     */
    public void logPerformanceWarning(String operation, long duration, long threshold) {
        if (duration > threshold) {
            log.warn("性能警告 - 操作: {}, 耗时: {}ms, 阈值: {}ms", operation, duration, threshold);
        }
    }

    /**
     * 记录业务警告
     */
    public void logBusinessWarning(String operation, String details) {
        log.warn("业务警告 - 操作: {}, 详情: {}", operation, details);
    }

    /**
     * 记录系统错误
     */
    public void logSystemError(String operation, Exception e) {
        log.error("系统错误 - 操作: {}", operation, e);
    }

    /**
     * 检查并处理内存不足异常
     */
    public boolean handleOutOfMemoryError(OutOfMemoryError e) {
        log.error("内存不足错误", e);
        
        // 强制垃圾回收
        System.gc();
        
        // 记录内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        log.error("内存使用情况 - 总内存: {}MB, 已用内存: {}MB, 空闲内存: {}MB", 
            totalMemory / 1024 / 1024, usedMemory / 1024 / 1024, freeMemory / 1024 / 1024);
        
        return freeMemory > totalMemory * 0.1; // 如果空闲内存大于10%，认为可以继续运行
    }
}
