package com.ruoyi.project.priceAlert.util;

import com.ruoyi.project.commoditySys.domain.VimItem;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * 价格计算工具类
 * <p>
 * 提取公共的价格计算逻辑，减少代码重复
 * 使用Java 21新特性优化代码
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class PriceCalculationUtil {

    /**
     * 精度常量
     */
    private static final int PRICE_SCALE = 2;
    private static final int PERCENTAGE_SCALE = 4;


    /**
     * 商城展示价格
     */
    public static final BigDecimal PRICE_BUY_MULTIPLIER = new BigDecimal("1.2");
    /**
     * 展示价格
     */
    public static final BigDecimal PRICE_SHOW_MULTIPLIER = new BigDecimal("1.02");
    /**
     * 回收价格
     */
    public static final BigDecimal PRICE_RECYCLE_MULTIPLIER = new BigDecimal("1.02");

    /**
     * 百分比
     */
    private static final BigDecimal HUNDRED = new BigDecimal("100");

    /**
     * 从价格信息中获取当前价格
     *
     * @param priceInfo 价格信息Map
     * @param config    预警配置
     * @return 当前价格
     */
    public BigDecimal getCurrentPriceFromInfo(Map<String, Object> priceInfo, VimPriceAlertConfig config) {
        if (priceInfo == null || priceInfo.isEmpty()) {
            return null;
        }

        try {
            // 根据数据源优先级获取价格
            String[] dataSources = config.getDataSourcePriority().split(",");
            for (String dataSource : dataSources) {
                BigDecimal price = extractPriceByDataSource(priceInfo, dataSource.trim());
                if (price != null && price.compareTo(BigDecimal.ZERO) > 0) {
                    return price;
                }
            }

            // 如果优先级数据源都没有价格，尝试获取默认价格
            return extractDefaultPrice(priceInfo);

        } catch (Exception e) {
            log.warn("获取当前价格失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 根据数据源提取价格
     */
    private BigDecimal extractPriceByDataSource(Map<String, Object> priceInfo, String dataSource) {
        return switch (dataSource.toLowerCase()) {
            case "buff" -> (BigDecimal) priceInfo.get("sell_min_price");
            case "steam" -> (BigDecimal) priceInfo.get("steam_price");
            case "c5game" -> (BigDecimal) priceInfo.get("c5game_price");
            default -> null;
        };
    }

    /**
     * 提取默认价格（sell_min_price）
     */
    private BigDecimal extractDefaultPrice(Map<String, Object> priceInfo) {
        Object price = priceInfo.get("sell_min_price");
        return price instanceof BigDecimal ? (BigDecimal) price : null;
    }

    /**
     * 检查价格预警
     *
     * @param item         商品信息
     * @param config       预警配置
     * @param currentPrice 当前价格
     * @return 是否触发预警
     */
    public boolean checkPriceAlert(VimItem item, VimPriceAlertConfig config, BigDecimal currentPrice) {
        try {
            BigDecimal basePrice = item.getPriceShow();
            if (basePrice == null || basePrice.compareTo(BigDecimal.ZERO) <= 0) {
                log.debug("商品 {} 基准价格无效: {}", item.getName(), basePrice);
                return false;
            }

            // 计算价格变化
            BigDecimal[] changes = calculatePriceChange(basePrice, currentPrice);
            BigDecimal changeAmount = changes[0];
            BigDecimal changePercentage = changes[1];

            log.debug("商品 {} 价格变化分析: 基准价格={}, 当前价格={}, 变化金额={}, 变化百分比={}%",
                    item.getName(), basePrice, currentPrice, changeAmount, changePercentage);

            // 根据规则类型和阈值类型判断是否触发预警
            return isAlertTriggered(config, changeAmount, changePercentage);

        } catch (Exception e) {
            log.warn("检查价格预警失败，商品: {}", item.getName(), e);
            return false;
        }
    }

    /**
     * 计算价格变化
     * 使用记忆中的公式：(reference_price - display_price) / display_price * 100
     *
     * @param basePrice    基准价格
     * @param currentPrice 当前价格
     * @return [变化金额, 变化百分比]
     */
    public BigDecimal[] calculatePriceChange(BigDecimal basePrice, BigDecimal currentPrice) {
        if (basePrice == null || currentPrice == null || basePrice.compareTo(BigDecimal.ZERO) <= 0) {
            return new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO};
        }

        // 变化金额 = 当前价格 - 基准价格
        BigDecimal changeAmount = currentPrice.subtract(basePrice);

        // 变化百分比 = (当前价格 - 基准价格) / 基准价格 * 100
        BigDecimal changePercentage = changeAmount
                .divide(basePrice, PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                .multiply(HUNDRED);

        return new BigDecimal[]{
                changeAmount.setScale(PRICE_SCALE, RoundingMode.HALF_UP),
                changePercentage.setScale(PERCENTAGE_SCALE, RoundingMode.HALF_UP)
        };
    }

    /**
     * 判断是否触发预警
     */
    private boolean isAlertTriggered(VimPriceAlertConfig config, BigDecimal changeAmount, BigDecimal changePercentage) {
        BigDecimal threshold = config.getThresholdValue();
        if (threshold == null) {
            return false;
        }

        return switch (config.getRuleType()) {
            case "price_up" -> isPriceUpTriggered(config, changeAmount, changePercentage, threshold);
            case "price_down" -> isPriceDownTriggered(config, changeAmount, changePercentage, threshold);
            default -> {
                log.warn("未知的规则类型: {}", config.getRuleType());
                yield false;
            }
        };
    }

    /**
     * 判断价格上涨预警
     */
    private boolean isPriceUpTriggered(VimPriceAlertConfig config, BigDecimal changeAmount,
                                       BigDecimal changePercentage, BigDecimal threshold) {
        // 百分比
        if (config.getThresholdType() == 1) {
            return changePercentage.compareTo(threshold) >= 0;
        } else { // 固定金额
            return changeAmount.compareTo(threshold) >= 0;
        }
    }

    /**
     * 判断价格下跌预警
     */
    private boolean isPriceDownTriggered(VimPriceAlertConfig config, BigDecimal changeAmount,
                                         BigDecimal changePercentage, BigDecimal threshold) {
        // 百分比
        if (config.getThresholdType() == 1) {
            return changePercentage.abs().compareTo(threshold) >= 0 && changePercentage.compareTo(BigDecimal.ZERO) < 0;
        } else { // 固定金额
            return changeAmount.abs().compareTo(threshold) >= 0 && changeAmount.compareTo(BigDecimal.ZERO) < 0;
        }
    }

    /**
     * 更新商品价格 - 使用记忆中的价格更新公式
     * priceCost = referenceSellMinPrice
     * priceBuy = referenceSellMinPrice * 1.2
     * priceShow = referenceSellMinPrice * 1.05
     * priceRecycle = referenceSellMinPrice * 1.05
     *
     * @param item                  商品对象
     * @param referenceSellMinPrice 参考最低售价
     */
    public void updateItemPrices(VimItem item, BigDecimal referenceSellMinPrice) {
        if (referenceSellMinPrice == null || referenceSellMinPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("参考价格无效，跳过价格更新，商品: {}", item.getName());
            return;
        }

        try {
            // 使用BigDecimal确保精度
            item.setPriceCost(referenceSellMinPrice.setScale(PRICE_SCALE, RoundingMode.HALF_UP));

            item.setPriceBuy(referenceSellMinPrice
                    .multiply(PRICE_BUY_MULTIPLIER)
                    .setScale(PRICE_SCALE, RoundingMode.HALF_UP));

            item.setPriceShow(referenceSellMinPrice
                    .multiply(PRICE_SHOW_MULTIPLIER)
                    .setScale(PRICE_SCALE, RoundingMode.HALF_UP));

            item.setPriceRecycle(referenceSellMinPrice
                    .multiply(PRICE_RECYCLE_MULTIPLIER)
                    .setScale(PRICE_SCALE, RoundingMode.HALF_UP));

            log.debug("商品 {} 价格更新完成: 成本={}, 购买={}, 展示={}, 回收={}",
                    item.getName(), item.getPriceCost(), item.getPriceBuy(),
                    item.getPriceShow(), item.getPriceRecycle());

        } catch (Exception e) {
            log.error("更新商品价格失败，商品: {}", item.getName(), e);
        }
    }

    /**
     * 验证价格数据有效性
     */
    public boolean isValidPrice(BigDecimal price) {
        return price != null && price.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 格式化价格显示
     */
    public String formatPrice(BigDecimal price) {
        if (price == null) {
            return "N/A";
        }
        return price.setScale(PRICE_SCALE, RoundingMode.HALF_UP).toString();
    }

    /**
     * 格式化百分比显示
     */
    public String formatPercentage(BigDecimal percentage) {
        if (percentage == null) {
            return "N/A";
        }
        return percentage.setScale(2, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 计算价格差异百分比（用于价格分析）
     *
     * @param referencePrice 参考价格
     * @param displayPrice   展示价格
     * @return 差异百分比
     */
    public BigDecimal calculatePriceDifferencePercentage(BigDecimal referencePrice, BigDecimal displayPrice) {
        if (!isValidPrice(referencePrice) || !isValidPrice(displayPrice)) {
            return BigDecimal.ZERO;
        }

        // 使用记忆中的公式：(reference_price - display_price) / display_price * 100
        return referencePrice.subtract(displayPrice)
                .divide(displayPrice, PERCENTAGE_SCALE, RoundingMode.HALF_UP)
                .multiply(HUNDRED);
    }

    /**
     * 批量计算价格变化
     */
    public Map<Long, BigDecimal[]> batchCalculatePriceChanges(Map<Long, VimItem> items,
                                                              Map<String, BigDecimal> currentPrices) {

        return items.entrySet().stream()
                .filter(entry -> {
                    VimItem item = entry.getValue();
                    return currentPrices.containsKey(item.getHashname()) &&
                            isValidPrice(item.getPriceShow());
                })
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            VimItem item = entry.getValue();
                            BigDecimal currentPrice = currentPrices.get(item.getHashname());
                            return calculatePriceChange(item.getPriceShow(), currentPrice);
                        }
                ));
    }
}
