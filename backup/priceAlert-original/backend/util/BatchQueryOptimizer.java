package com.ruoyi.project.priceAlert.util;

import com.ruoyi.project.priceAlert.config.PriceAlertPerformanceConfig;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertRecordSimple;
import com.ruoyi.project.priceAlert.mapper.VimPriceAlertRecordSimpleMapper;
import com.ruoyi.project.commoditySys.domain.VimItem;
import com.ruoyi.project.commoditySys.mapper.SkinGoodsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 批量查询优化器
 * 主要功能：
 * 1. 解决N+1查询问题
 * 2. 批量数据库操作优化
 * 3. 缓存机制
 * 4. 分批处理大数据集
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class BatchQueryOptimizer {

    @Autowired
    private SkinGoodsMapper skinGoodsMapper;

    @Autowired
    private VimPriceAlertRecordSimpleMapper recordMapper;

    @Autowired
    private PriceAlertPerformanceConfig performanceConfig;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    /**
     * 价格信息缓存
     */
    private final Map<String, Map<String, Object>> priceInfoCache = new ConcurrentHashMap<>();

    /**
     * 批量跨库获取价格信息 - 优化版本
     * 
     * 解决N+1查询问题的核心方法
     * 
     * @param items 商品列表
     * @param dataSources 数据源
     * @return hashname -> 价格信息的映射
     */
    public Map<String, Map<String, Object>> getBatchCrossDatabasePrice(List<VimItem> items, String dataSources) {
        if (items == null || items.isEmpty()) {
            return new HashMap<>();
        }

        long startTime = performanceMonitor.startQuery("batch_cross_database_price_optimized");
        Map<String, Map<String, Object>> result = new HashMap<>();

        try {
            // 提取所有hashname
            List<String> hashNames = items.stream()
                .map(VimItem::getHashname)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

            if (hashNames.isEmpty()) {
                return result;
            }

            log.info("开始批量查询商品价格，hashname数量: {}, dataSources: {}", hashNames.size(), dataSources);

            // 检查缓存
            Map<String, Map<String, Object>> cachedResults = getCachedPriceInfo(hashNames);
            result.putAll(cachedResults);
            // 获取未缓存的hashname
            List<String> uncachedHashNames = hashNames.stream()
                .filter(hashName -> !cachedResults.containsKey(hashName))
                .collect(Collectors.toList());

            if (!uncachedHashNames.isEmpty()) {
                // 分批查询未缓存的数据
                Map<String, Map<String, Object>> freshResults = batchQueryPriceInfo(uncachedHashNames);
                result.putAll(freshResults);

                // 更新缓存
                updatePriceInfoCache(freshResults);
            }

            log.info("批量查询价格信息完成，总数: {}, 缓存命中: {}, 新查询: {}", 
                hashNames.size(), cachedResults.size(), uncachedHashNames.size());

            performanceMonitor.endQuery("batch_cross_database_price_optimized", startTime, result.size());

        } catch (Exception e) {
            log.error("批量查询价格信息失败", e);
            performanceMonitor.endQuery("batch_cross_database_price_optimized", startTime, 0);
        }

        return result;
    }

    /**
     * 分批查询价格信息
     */
    private Map<String, Map<String, Object>> batchQueryPriceInfo(List<String> hashNames) {
        Map<String, Map<String, Object>> result = new HashMap<>();
        int batchSize = performanceConfig.getBatchQuery().getBatchSize();

        // 分批处理，避免单次查询数据量过大
        for (int i = 0; i < hashNames.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, hashNames.size());
            List<String> batchHashNames = hashNames.subList(i, endIndex);

            try {
                long batchStartTime = System.currentTimeMillis();

                // 执行批量查询
                List<Map<String, Object>> batchResults = skinGoodsMapper.getBatchPriceInfoByHashNames(batchHashNames);

                // 转换为Map结构
                if (batchResults != null) {
                    for (Map<String, Object> priceInfo : batchResults) {
                        String marketHashName = (String) priceInfo.get("market_hash_name");
                        if (marketHashName != null) {
                            result.put(marketHashName, priceInfo);
                        }
                    }
                }

                long batchTime = System.currentTimeMillis() - batchStartTime;
                log.debug("批次查询完成，批次大小: {}, 结果数量: {}, 耗时: {}ms",
                    batchHashNames.size(), batchResults != null ? batchResults.size() : 0, batchTime);

            } catch (Exception e) {
                log.error("批次查询失败，批次: {}-{}", i, endIndex, e);
            }
        }

        return result;
    }

    /**
     * 获取缓存的价格信息
     */
    @Cacheable(value = "priceInfo", key = "#hashNames.toString()")
    private Map<String, Map<String, Object>> getCachedPriceInfo(List<String> hashNames) {
        Map<String, Map<String, Object>> cachedResults = new HashMap<>();
        
        for (String hashName : hashNames) {
            Map<String, Object> cached = priceInfoCache.get(hashName);
            if (cached != null && !isCacheExpired(cached)) {
                cachedResults.put(hashName, cached);
            }
        }
        
        return cachedResults;
    }

    /**
     * 更新价格信息缓存
     */
    private void updatePriceInfoCache(Map<String, Map<String, Object>> priceInfoMap) {
        long currentTime = System.currentTimeMillis();
        
        priceInfoMap.forEach((hashName, priceInfo) -> {
            // 添加缓存时间戳
            Map<String, Object> cachedInfo = new HashMap<>(priceInfo);
            cachedInfo.put("_cache_time", currentTime);
            priceInfoCache.put(hashName, cachedInfo);
        });

        // 清理过期缓存
        cleanExpiredCache();
    }

    /**
     * 检查缓存是否过期
     */
    private boolean isCacheExpired(Map<String, Object> cachedInfo) {
        Object cacheTime = cachedInfo.get("_cache_time");
        if (cacheTime instanceof Long) {
            long cached = (Long) cacheTime;
            long ttl = performanceConfig.getCache().getPriceInfoTtl() * 1000; // 转换为毫秒
            return System.currentTimeMillis() - cached > ttl;
        }
        return true; // 没有时间戳的缓存视为过期
    }

    /**
     * 清理过期缓存
     */
    private void cleanExpiredCache() {
        if (priceInfoCache.size() > performanceConfig.getCache().getMaxSize()) {
            priceInfoCache.entrySet().removeIf(entry -> isCacheExpired(entry.getValue()));
            log.debug("清理过期缓存完成，当前缓存大小: {}", priceInfoCache.size());
        }
    }

    /**
     * 批量插入预警记录
     */
    @Transactional
    public void batchInsertAlertRecords(List<VimPriceAlertRecordSimple> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        long startTime = performanceMonitor.startQuery("batch_insert_alert_records");
        
        try {
            int batchSize = performanceConfig.getBatchQuery().getBatchInsertSize();
            
            // 分批插入
            for (int i = 0; i < records.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, records.size());
                List<VimPriceAlertRecordSimple> batchRecords = records.subList(i, endIndex);
                // 执行批量插入
                batchInsertRecordsBatch(batchRecords);
                log.debug("批量插入预警记录，批次: {}-{}, 数量: {}", i, endIndex, batchRecords.size());
            }
            
            performanceMonitor.endQuery("batch_insert_alert_records", startTime, records.size());
            log.info("批量插入预警记录完成，总数量: {}", records.size());
            
        } catch (Exception e) {
            log.error("批量插入预警记录失败", e);
            performanceMonitor.endQuery("batch_insert_alert_records", startTime, 0);
            throw e;
        }
    }

    /**
     * 批量插入
     */
    private void batchInsertRecordsBatch(List<VimPriceAlertRecordSimple> batchRecords) {
        if (batchRecords == null || batchRecords.isEmpty()) {
            return;
        }

        try {
            // 使用批量插入方法，避免N+1查询问题
            recordMapper.batchInsertRecords(batchRecords);
            log.debug("批量插入预警记录成功，数量: {}", batchRecords.size());
        } catch (Exception e) {
            log.error("批量插入预警记录失败，回退到逐个插入", e);
            // 如果批量插入失败，回退到逐个插入
            for (VimPriceAlertRecordSimple record : batchRecords) {
                try {
                    recordMapper.insertRecord(record);
                } catch (Exception ex) {
                    log.error("插入单条预警记录失败: {}", record, ex);
                }
            }
        }
    }

    /**
     * 异步批量查询价格信息
     */
    public CompletableFuture<Map<String, Map<String, Object>>> getBatchCrossDatabasePriceAsync(
            List<VimItem> items, String dataSources) {
        
        return CompletableFuture.supplyAsync(() -> getBatchCrossDatabasePrice(items, dataSources));
    }

    /**
     * 预热缓存
     */
    public void warmUpCache(List<String> hashNames) {
        log.info("开始预热价格信息缓存，数量: {}", hashNames.size());
        
        try {
            Map<String, Map<String, Object>> priceInfoMap = batchQueryPriceInfo(hashNames);
            updatePriceInfoCache(priceInfoMap);
            
            log.info("价格信息缓存预热完成，缓存数量: {}", priceInfoMap.size());
            
        } catch (Exception e) {
            log.error("缓存预热失败", e);
        }
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        priceInfoCache.clear();
        log.info("价格信息缓存已清空");
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", priceInfoCache.size());
        stats.put("maxSize", performanceConfig.getCache().getMaxSize());
        stats.put("ttl", performanceConfig.getCache().getPriceInfoTtl());
        
        // 计算缓存命中率等统计信息
        long expiredCount = priceInfoCache.values().stream()
            .mapToLong(info -> isCacheExpired(info) ? 1 : 0)
            .sum();
        
        stats.put("expiredCount", expiredCount);
        stats.put("validCount", priceInfoCache.size() - expiredCount);
        
        return stats;
    }

    /**
     * 批量更新商品价格
     */
    @Transactional
    public void batchUpdateItemPrices(List<VimItem> items) {
        if (items == null || items.isEmpty()) {
            return;
        }

        long startTime = performanceMonitor.startQuery("batch_update_item_prices");
        
        try {
            int batchSize = performanceConfig.getBatchQuery().getBatchSize();
            
            // 分批更新
            for (int i = 0; i < items.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, items.size());
                List<VimItem> batchItems = items.subList(i, endIndex);
                
                // 执行批量更新
                batchUpdateItemPricesBatch(batchItems);
                
                log.debug("批量更新商品价格，批次: {}-{}, 数量: {}", i, endIndex, batchItems.size());
            }
            
            performanceMonitor.endQuery("batch_update_item_prices", startTime, items.size());
            log.info("批量更新商品价格完成，总数量: {}", items.size());
            
        } catch (Exception e) {
            log.error("批量更新商品价格失败", e);
            performanceMonitor.endQuery("batch_update_item_prices", startTime, 0);
            throw e;
        }
    }

    /**
     * 执行单批次价格更新
     */
    private void batchUpdateItemPricesBatch(List<VimItem> batchItems) {
        // 这里可以使用MyBatis的批量更新
        // 调用相应的Mapper方法进行更新
        // vimItemMapper.updateVimItem(item);
    }
}
