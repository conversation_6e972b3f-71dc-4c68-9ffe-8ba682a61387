package com.ruoyi.project.priceAlert.scheduler;

import com.ruoyi.project.priceAlert.service.ISimplePriceAlertService;
import com.ruoyi.project.priceAlert.util.PerformanceMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 价格预警定时任务调度器
 * 
 * 使用Spring的@Scheduled注解实现定时执行
 * 支持动态配置执行时间
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "price-alert.scheduler.enabled", havingValue = "true", matchIfMissing = true)
public class PriceAlertScheduler {

    @Autowired
    private ISimplePriceAlertService priceAlertService; // 自动使用@Primary标记的优化实现

    @Autowired
    private PerformanceMonitor performanceMonitor;

    /**
     * 防止重复执行的标志
     */
    private final AtomicBoolean isExecuting = new AtomicBoolean(false);

    /**
     * 上次执行时间
     */
    private volatile long lastExecutionTime = 0;

    /**
     * 最小执行间隔（毫秒）- 防止频繁执行
     */
    private static final long MIN_EXECUTION_INTERVAL = 30 * 60 * 1000; // 30分钟

    /**
     * 定时执行价格预警检查
     * 
     * 每小时的第5分钟执行一次
     * 例如：09:05, 10:05, 11:05...
     */
    @Scheduled(cron = "0 5 * * * ?")
    public void executeScheduledPriceAlert() {
        // 检查是否在配置的执行时间内
        if (!isInExecutionTime()) {
            log.debug("当前时间不在配置的执行时间范围内，跳过预警检查");
            return;
        }

        // 防止重复执行
        if (!isExecuting.compareAndSet(false, true)) {
            log.warn("价格预警检查正在执行中，跳过本次调度");
            return;
        }

        // 检查最小执行间隔
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastExecutionTime < MIN_EXECUTION_INTERVAL) {
            log.debug("距离上次执行时间过短，跳过本次调度");
            isExecuting.set(false);
            return;
        }

        try {
            log.info("开始执行定时价格预警检查");
            long startTime = System.currentTimeMillis();

            // 更新系统性能指标
            performanceMonitor.updateSystemMetrics();

            // 执行预警检查
            Map<String, Object> result = priceAlertService.executeScheduledCheck();

            long executionTime = System.currentTimeMillis() - startTime;
            lastExecutionTime = currentTime;

            // 记录执行结果
            logExecutionResult(result, executionTime);

            // 如果执行时间过长，记录慢查询
            // 1分钟
            if (executionTime > 60000) {
                performanceMonitor.recordSlowQuery("scheduled_price_alert", executionTime, 
                    (Integer) result.getOrDefault("totalChecked", 0), 
                    "定时价格预警检查执行时间过长");
            }

        } catch (Exception e) {
            log.error("定时价格预警检查执行失败", e);
        } finally {
            isExecuting.set(false);
        }
    }

    /**
     * 每日性能报告
     * 
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateDailyPerformanceReport() {
        try {
            log.info("开始生成每日性能报告");
            
            // 打印性能报告
            performanceMonitor.printPerformanceReport();
            
            // 获取系统性能指标
            PerformanceMonitor.SystemMetrics metrics = performanceMonitor.getSystemMetrics();
            log.info("系统性能指标 - 内存使用率: {:.2f}%, 可用处理器: {}", 
                metrics.getMemoryUsagePercent(), metrics.getAvailableProcessors());
            
            // 获取慢查询统计
            Map<String, Object> slowQueryStats = performanceMonitor.getSlowQueryStats();
            log.info("慢查询统计 - 总数: {}", slowQueryStats.get("totalSlowQueries"));
            
        } catch (Exception e) {
            log.error("生成每日性能报告失败", e);
        }
    }

    /**
     * 每周缓存清理
     * 
     * 每周日凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 ? * SUN")
    public void weeklyMaintenanceTasks() {
        try {
            log.info("开始执行每周维护任务");
            
            // 重置性能统计
            performanceMonitor.resetStats();
            log.info("性能统计信息已重置");
            
            // 强制垃圾回收（谨慎使用）
            System.gc();
            log.info("已建议JVM执行垃圾回收");
            
        } catch (Exception e) {
            log.error("每周维护任务执行失败", e);
        }
    }

    /**
     * 健康检查
     * 
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void healthCheck() {
        try {
            // 更新系统性能指标
            performanceMonitor.updateSystemMetrics();
            
            PerformanceMonitor.SystemMetrics metrics = performanceMonitor.getSystemMetrics();
            
            // 检查内存使用率
            if (metrics.getMemoryUsagePercent() > 90) {
                log.error("系统内存使用率过高: {:.2f}%", metrics.getMemoryUsagePercent());
            } else if (metrics.getMemoryUsagePercent() > 80) {
                log.warn("系统内存使用率较高: {:.2f}%", metrics.getMemoryUsagePercent());
            }
            
            // 检查是否有长时间运行的任务
            if (isExecuting.get() && System.currentTimeMillis() - lastExecutionTime > 600000) { // 10分钟
                log.warn("检测到长时间运行的价格预警任务，可能存在性能问题");
            }
            
        } catch (Exception e) {
            log.error("健康检查执行失败", e);
        }
    }

    /**
     * 检查当前时间是否在配置的执行时间内
     */
    private boolean isInExecutionTime() {
        LocalTime currentTime = LocalTime.now();
        
        // 默认执行时间：09:00, 15:00, 21:00
        // 允许在目标时间前后5分钟内执行
        LocalTime[] executionTimes = {
            LocalTime.of(9, 0),   // 09:00
            LocalTime.of(15, 0),  // 15:00
            LocalTime.of(21, 0)   // 21:00
        };
        
        for (LocalTime executionTime : executionTimes) {
            LocalTime startWindow = executionTime.minusMinutes(5);
            LocalTime endWindow = executionTime.plusMinutes(5);
            
            if (currentTime.isAfter(startWindow) && currentTime.isBefore(endWindow)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 记录执行结果
     */
    private void logExecutionResult(Map<String, Object> result, long executionTime) {
        boolean success = (Boolean) result.getOrDefault("success", false);
        int totalChecked = (Integer) result.getOrDefault("totalChecked", 0);
        int totalTriggered = (Integer) result.getOrDefault("totalTriggered", 0);
        
        if (success) {
            log.info("定时价格预警检查完成 - 检查商品: {}, 触发预警: {}, 耗时: {}ms", 
                totalChecked, totalTriggered, executionTime);
        } else {
            String message = (String) result.getOrDefault("message", "未知错误");
            log.error("定时价格预警检查失败 - 错误信息: {}, 耗时: {}ms", message, executionTime);
        }
        
        // 记录详细统计信息
        if (totalChecked > 0) {
            double efficiency = (double) totalChecked / (executionTime / 1000.0); // 商品/秒
            double triggerRate = (double) totalTriggered / totalChecked * 100; // 触发率
            
            log.info("执行效率: {:.2f}商品/秒, 预警触发率: {:.2f}%", efficiency, triggerRate);
        }
    }

    /**
     * 获取调度器状态
     */
    public Map<String, Object> getSchedulerStatus() {
        Map<String, Object> status = new java.util.HashMap<>();
        status.put("isExecuting", isExecuting.get());
        status.put("lastExecutionTime", lastExecutionTime);
        status.put("currentTime", System.currentTimeMillis());
        status.put("inExecutionTime", isInExecutionTime());
        
        if (lastExecutionTime > 0) {
            status.put("timeSinceLastExecution", System.currentTimeMillis() - lastExecutionTime);
        }
        
        return status;
    }

    /**
     * 手动触发预警检查（用于测试）
     */
    public Map<String, Object> manualTrigger() {
        if (isExecuting.get()) {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "预警检查正在执行中，请稍后再试");
            return result;
        }
        
        try {
            log.info("手动触发价格预警检查");
            return priceAlertService.executeManualCheck("manual_scheduler");
        } catch (Exception e) {
            log.error("手动触发价格预警检查失败", e);
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("success", false);
            result.put("message", "手动触发失败: " + e.getMessage());
            return result;
        }
    }
}
