package com.ruoyi.project.priceAlert.mapper;

import com.ruoyi.project.priceAlert.domain.VimPriceAlertRecordSimple;
import com.ruoyi.project.priceAlert.domain.VimCostPriceAnomalyDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 简化价格预警记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Mapper
public interface VimPriceAlertRecordSimpleMapper {

    /**
     * 查询预警记录列表
     * 
     * @param record 查询条件
     * @return 预警记录列表
     */
    List<VimPriceAlertRecordSimple> selectRecordList(VimPriceAlertRecordSimple record);

    /**
     * 根据ID查询预警记录
     * 
     * @param id 记录ID
     * @return 预警记录
     */
    VimPriceAlertRecordSimple selectRecordById(@Param("id") Long id);

    /**
     * 新增预警记录
     *
     * @param record 预警记录
     * @return 新增结果
     */
    int insertRecord(VimPriceAlertRecordSimple record);

    /**
     * 批量新增预警记录
     *
     * @param records 预警记录列表
     * @return 新增结果
     */
    int batchInsertRecords(@Param("records") List<VimPriceAlertRecordSimple> records);

    /**
     * 更新预警记录
     * 
     * @param record 预警记录
     * @return 更新结果
     */
    int updateRecord(VimPriceAlertRecordSimple record);

    /**
     * 批量删除预警记录
     * 
     * @param ids 记录ID数组
     * @return 删除结果
     */
    int deleteRecordByIds(@Param("ids") Long[] ids);

    /**
     * 统计所有记录数量
     * 
     * @return 记录总数
     */
    int countAllRecords();

    /**
     * 统计待处理记录数量
     * 
     * @return 待处理记录数量
     */
    int countPendingRecords();

    /**
     * 统计今日记录数量
     * 
     * @return 今日记录数量
     */
    int countTodayRecords();

    /**
     * 根据规则类型统计记录
     * 
     * @return 规则类型统计列表
     */
    List<Map<String, Object>> getRecordStatsByRuleType();

    /**
     * 获取预警趋势数据
     * 
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getAlertTrendData(@Param("days") int days);

    /**
     * 获取商品预警排行
     * 
     * @param limit 返回数量限制
     * @return 排行数据
     */
    List<Map<String, Object>> getItemAlertRanking(@Param("limit") int limit);

    /**
     * 获取数据源统计
     * 
     * @return 数据源统计
     */
    List<Map<String, Object>> getDataSourceStatistics();

    /**
     * 根据商品ID查询预警记录
     * 
     * @param itemId 商品ID
     * @return 预警记录列表
     */
    List<VimPriceAlertRecordSimple> selectRecordsByItemId(@Param("itemId") Long itemId);

    /**
     * 根据规则类型查询预警记录
     * 
     * @param ruleType 规则类型
     * @return 预警记录列表
     */
    List<VimPriceAlertRecordSimple> selectRecordsByRuleType(@Param("ruleType") String ruleType);

    /**
     * 根据时间范围查询预警记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预警记录列表
     */
    List<VimPriceAlertRecordSimple> selectRecordsByTimeRange(
            @Param("startTime") java.util.Date startTime, 
            @Param("endTime") java.util.Date endTime);

    /**
     * 批量更新记录状态
     * 
     * @param ids 记录ID数组
     * @param status 新状态
     * @param handleBy 处理人
     * @param handleTime 处理时间
     * @return 更新结果
     */
    int batchUpdateRecordStatus(@Param("ids") Long[] ids, 
                                @Param("status") Integer status,
                                @Param("handleBy") String handleBy,
                                @Param("handleTime") java.util.Date handleTime);

    /**
     * 清理过期记录
     * 
     * @param retentionDays 保留天数
     * @return 清理数量
     */
    int cleanExpiredRecords(@Param("retentionDays") int retentionDays);

    /**
     * 获取最近的预警记录
     * 
     * @param limit 返回数量限制
     * @return 最近预警记录
     */
    List<VimPriceAlertRecordSimple> selectRecentRecords(@Param("limit") int limit);

    /**
     * 统计指定时间段内的预警数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param ruleType 规则类型（可选）
     * @return 预警数量
     */
    int countRecordsByTimeRange(@Param("startTime") java.util.Date startTime,
                                @Param("endTime") java.util.Date endTime,
                                @Param("ruleType") String ruleType);

    /**
     * 获取预警级别统计
     * 
     * @return 预警级别统计
     */
    List<Map<String, Object>> getAlertLevelStatistics();

    /**
     * 获取处理状态统计
     *
     * @return 处理状态统计
     */
    List<Map<String, Object>> getHandleStatusStatistics();

    // ========== 成本价异常商品查询 ==========

    /**
     * 查询成本价异常商品列表（成本价大于回收价的商品）
     *
     * @param itemName 商品名称（可选）
     * @param boxName 盲盒名称（可选）
     * @param itemTag 商品标签（可选）
     * @param saleStatus 商品状态（可选）
     * @param minCost 最小成本价（可选）
     * @param maxCost 最大成本价（可选）
     * @param minRecycle 最小回收价（可选）
     * @param maxRecycle 最大回收价（可选）
     * @param minDifference 最小价格差异（可选）
     * @param maxDifference 最大价格差异（可选）
     * @param minPercentage 最小差异百分比（可选）
     * @param maxPercentage 最大差异百分比（可选）
     * @param stockStatus 库存状态（可选）
     * @param severityLevel 异常严重程度（可选）
     * @param sortField 排序字段（可选）
     * @return 成本价异常商品列表
     */
    List<VimCostPriceAnomalyDTO> selectCostPriceAnomalyList(@Param("itemName") String itemName,
                                                            @Param("boxName") String boxName,
                                                            @Param("itemTag") String itemTag,
                                                            @Param("saleStatus") Integer saleStatus,
                                                            @Param("minCost") java.math.BigDecimal minCost,
                                                            @Param("maxCost") java.math.BigDecimal maxCost,
                                                            @Param("minRecycle") java.math.BigDecimal minRecycle,
                                                            @Param("maxRecycle") java.math.BigDecimal maxRecycle,
                                                            @Param("minDifference") java.math.BigDecimal minDifference,
                                                            @Param("maxDifference") java.math.BigDecimal maxDifference,
                                                            @Param("minPercentage") java.math.BigDecimal minPercentage,
                                                            @Param("maxPercentage") java.math.BigDecimal maxPercentage,
                                                            @Param("stockStatus") Integer stockStatus,
                                                            @Param("severityLevel") Integer severityLevel,
                                                            @Param("sortField") String sortField);

    /**
     * 统计成本价异常商品数量
     *
     * @return 异常商品数量
     */
    int countCostPriceAnomalyItems();
}
