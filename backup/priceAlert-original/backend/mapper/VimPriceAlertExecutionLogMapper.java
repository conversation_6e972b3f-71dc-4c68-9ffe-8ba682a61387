package com.ruoyi.project.priceAlert.mapper;

import com.ruoyi.project.priceAlert.domain.VimPriceAlertExecutionLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 预警执行日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Mapper
public interface VimPriceAlertExecutionLogMapper {

    /**
     * 查询执行日志列表
     * 
     * @param log 查询条件
     * @return 执行日志列表
     */
    List<VimPriceAlertExecutionLog> selectExecutionLogList(VimPriceAlertExecutionLog log);

    /**
     * 根据ID查询执行日志
     * 
     * @param id 日志ID
     * @return 执行日志
     */
    VimPriceAlertExecutionLog selectExecutionLogById(@Param("id") Long id);

    /**
     * 新增执行日志
     * 
     * @param log 执行日志
     * @return 新增结果
     */
    int insertExecutionLog(VimPriceAlertExecutionLog log);

    /**
     * 更新执行日志
     * 
     * @param log 执行日志
     * @return 更新结果
     */
    int updateExecutionLog(VimPriceAlertExecutionLog log);

    /**
     * 批量删除执行日志
     * 
     * @param ids 日志ID数组
     * @return 删除结果
     */
    int deleteExecutionLogByIds(@Param("ids") Long[] ids);

    /**
     * 清理过期日志
     * 
     * @param retentionDays 保留天数
     * @return 清理数量
     */
    int cleanExpiredLogs(@Param("retentionDays") int retentionDays);

    /**
     * 统计所有执行次数
     * 
     * @return 执行总次数
     */
    int countAllExecutions();

    /**
     * 获取最新的执行日志
     * 
     * @return 最新执行日志
     */
    VimPriceAlertExecutionLog selectLatestExecution();

    /**
     * 根据执行类型查询日志
     * 
     * @param executionType 执行类型
     * @return 执行日志列表
     */
    List<VimPriceAlertExecutionLog> selectLogsByExecutionType(@Param("executionType") String executionType);

    /**
     * 根据规则类型查询日志
     * 
     * @param ruleType 规则类型
     * @return 执行日志列表
     */
    List<VimPriceAlertExecutionLog> selectLogsByRuleType(@Param("ruleType") String ruleType);

    /**
     * 根据时间范围查询日志
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行日志列表
     */
    List<VimPriceAlertExecutionLog> selectLogsByTimeRange(
            @Param("startTime") java.util.Date startTime,
            @Param("endTime") java.util.Date endTime);

    /**
     * 获取执行统计信息
     * 
     * @param days 统计天数
     * @return 执行统计
     */
    List<Map<String, Object>> getExecutionStatistics(@Param("days") int days);

    /**
     * 获取执行成功率统计
     * 
     * @param days 统计天数
     * @return 成功率统计
     */
    Map<String, Object> getExecutionSuccessRate(@Param("days") int days);

    /**
     * 获取平均执行时间统计
     * 
     * @param days 统计天数
     * @return 平均执行时间
     */
    Map<String, Object> getAverageExecutionTime(@Param("days") int days);

    /**
     * 根据执行状态统计日志
     * 
     * @return 执行状态统计
     */
    List<Map<String, Object>> getExecutionStatusStatistics();

    /**
     * 获取最近的执行日志
     * 
     * @param limit 返回数量限制
     * @return 最近执行日志
     */
    List<VimPriceAlertExecutionLog> selectRecentExecutions(@Param("limit") int limit);

    /**
     * 统计指定时间段内的执行次数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param executionType 执行类型（可选）
     * @return 执行次数
     */
    int countExecutionsByTimeRange(@Param("startTime") java.util.Date startTime,
                                   @Param("endTime") java.util.Date endTime,
                                   @Param("executionType") String executionType);

    /**
     * 获取执行效率统计
     * 
     * @param days 统计天数
     * @return 执行效率数据
     */
    List<Map<String, Object>> getExecutionEfficiencyStats(@Param("days") int days);

    /**
     * 获取错误日志统计
     * 
     * @param days 统计天数
     * @return 错误统计
     */
    List<Map<String, Object>> getErrorLogStatistics(@Param("days") int days);

    /**
     * 检查是否存在正在执行的任务
     * 
     * @return 是否存在正在执行的任务
     */
    boolean hasRunningExecution();

    /**
     * 获取执行趋势数据
     * 
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getExecutionTrendData(@Param("days") int days);
}
