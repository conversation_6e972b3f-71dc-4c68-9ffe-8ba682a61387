package com.ruoyi.project.priceAlert.mapper;

import com.ruoyi.project.priceAlert.domain.VimPriceAlertConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 简化价格预警配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Mapper
public interface VimPriceAlertConfigMapper {

    /**
     * 查询所有预警配置
     * 
     * @return 预警配置列表
     */
    List<VimPriceAlertConfig> selectAllConfigs();

    /**
     * 根据规则类型查询预警配置
     * 
     * @param ruleType 规则类型
     * @return 预警配置
     */
    VimPriceAlertConfig selectConfigByRuleType(@Param("ruleType") String ruleType);

    /**
     * 查询启用的预警配置
     * 
     * @return 启用的预警配置列表
     */
    List<VimPriceAlertConfig> selectEnabledConfigs();

    /**
     * 新增预警配置
     *
     * @param config 预警配置
     * @return 新增结果
     */
    int insertConfig(VimPriceAlertConfig config);

    /**
     * 更新预警配置
     *
     * @param config 预警配置
     * @return 更新结果
     */
    int updateConfig(VimPriceAlertConfig config);

    /**
     * 更新执行统计信息
     * 
     * @param config 包含统计信息的配置对象
     * @return 更新结果
     */
    int updateExecutionStats(VimPriceAlertConfig config);

    /**
     * 统计所有配置数量
     * 
     * @return 配置总数
     */
    int countAllConfigs();

    /**
     * 统计启用的配置数量
     * 
     * @return 启用配置数量
     */
    int countEnabledConfigs();

    /**
     * 根据规则类型启用/禁用配置
     * 
     * @param ruleType 规则类型
     * @param enabled 是否启用
     * @return 更新结果
     */
    int toggleConfigStatus(@Param("ruleType") String ruleType, @Param("enabled") Integer enabled);

    /**
     * 批量更新配置
     * 
     * @param configs 配置列表
     * @return 更新结果
     */
    int batchUpdateConfigs(@Param("configs") List<VimPriceAlertConfig> configs);

    /**
     * 重置配置为默认值
     * 
     * @param ruleType 规则类型
     * @return 重置结果
     */
    int resetConfigToDefault(@Param("ruleType") String ruleType);

    /**
     * 获取配置的最后执行时间
     * 
     * @param ruleType 规则类型
     * @return 最后执行时间
     */
    java.util.Date getLastExecutionTime(@Param("ruleType") String ruleType);

    /**
     * 检查配置是否存在
     * 
     * @param ruleType 规则类型
     * @return 是否存在
     */
    boolean existsByRuleType(@Param("ruleType") String ruleType);
}
