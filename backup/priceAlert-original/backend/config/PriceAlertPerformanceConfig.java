package com.ruoyi.project.priceAlert.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 价格预警性能优化配置
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "price-alert.performance")
public class PriceAlertPerformanceConfig {

    /**
     * 批量查询配置
     */
    private BatchQuery batchQuery = new BatchQuery();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 连接池配置
     */
    private ConnectionPool connectionPool = new ConnectionPool();

    /**
     * 执行配置
     */
    private Execution execution = new Execution();

    @Data
    public static class BatchQuery {
        /**
         * 批量查询大小
         */
        private int batchSize = 100;

        /**
         * 最大批量查询大小
         */
        private int maxBatchSize = 500;

        /**
         * 批量插入大小
         */
        private int batchInsertSize = 100;

        /**
         * 是否启用批量查询优化
         */
        private boolean enabled = true;
    }

    @Data
    public static class Cache {
        /**
         * 价格信息缓存时间（秒）
         */
        private long priceInfoTtl = 300;

        /**
         * 商品信息缓存时间（秒）
         */
        private long itemInfoTtl = 600;

        /**
         * 是否启用缓存
         */
        private boolean enabled = true;

        /**
         * 缓存最大大小
         */
        private int maxSize = 10000;
    }

    @Data
    public static class ConnectionPool {
        /**
         * 连接池监控间隔（毫秒）
         */
        private long monitorInterval = 30000;

        /**
         * 连接泄漏检测阈值（毫秒）
         */
        private long leakDetectionThreshold = 60000;

        /**
         * 是否启用连接池监控
         */
        private boolean monitorEnabled = true;
    }

    @Data
    public static class Execution {
        /**
         * 单次执行最大商品数量
         */
        private int maxItemsPerExecution = 1000;

        /**
         * 执行超时时间（秒）
         */
        private int timeoutSeconds = 300;

        /**
         * 线程池大小
         */
        private int threadPoolSize = 4;

        /**
         * 是否启用异步执行
         */
        private boolean asyncEnabled = true;

        /**
         * 重试次数
         */
        private int retryCount = 3;

        /**
         * 重试间隔（毫秒）
         */
        private long retryInterval = 1000;
    }
}
