package com.ruoyi.project.priceAlert.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertConfig;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertRecordSimple;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertExecutionLog;
import com.ruoyi.project.priceAlert.domain.VimCostPriceAnomalyDTO;
import com.ruoyi.project.priceAlert.mapper.VimPriceAlertConfigMapper;
import com.ruoyi.project.priceAlert.mapper.VimPriceAlertRecordSimpleMapper;
import com.ruoyi.project.priceAlert.mapper.VimPriceAlertExecutionLogMapper;
import com.ruoyi.project.priceAlert.service.ISimplePriceAlertService;
import com.ruoyi.project.priceAlert.config.PriceAlertPerformanceConfig;
import com.ruoyi.project.priceAlert.util.PerformanceMonitor;
import com.ruoyi.project.priceAlert.util.PriceCalculationUtil;
import com.ruoyi.project.priceAlert.util.BatchQueryOptimizer;
import com.ruoyi.project.commoditySys.domain.VimItem;
import com.ruoyi.project.commoditySys.mapper.VimItemMapper;
import com.ruoyi.project.commoditySys.mapper.SkinGoodsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.annotation.PreDestroy;
import java.util.Arrays;

/**
 * 优化后的价格预警服务实现
 * 
 * 主要优化：
 * 1. 解决N+1查询问题，使用批量查询
 * 2. 使用Java 21新特性
 * 3. 提取公共方法，减少代码重复
 * 4. 优化异常处理和日志记录
 * 5. 增加缓存机制
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service("optimizedPriceAlertService")
public class OptimizedPriceAlertServiceImpl implements ISimplePriceAlertService {

    @Autowired
    private VimPriceAlertConfigMapper configMapper;

    @Autowired
    private VimPriceAlertRecordSimpleMapper recordMapper;

    @Autowired
    private VimPriceAlertExecutionLogMapper executionLogMapper;

    @Autowired
    private VimItemMapper vimItemMapper;

    @Autowired
    private SkinGoodsMapper skinGoodsMapper;

    @Autowired
    private PriceAlertPerformanceConfig performanceConfig;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    @Autowired
    private PriceCalculationUtil priceCalculationUtil;

    @Autowired
    private BatchQueryOptimizer batchQueryOptimizer;

    /**
     * 动态线程池，根据配置调整大小
     */
    private final ThreadPoolExecutor executorService;

    public OptimizedPriceAlertServiceImpl() {
        this.executorService = new ThreadPoolExecutor(
            4, // 核心线程数
            8, // 最大线程数
            60L, TimeUnit.SECONDS, // 空闲时间
            new LinkedBlockingQueue<>(100), // 队列大小
            new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(0);
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "price-alert-worker-" + counter.incrementAndGet());
                    thread.setDaemon(false); // 确保线程不是守护线程
                    thread.setUncaughtExceptionHandler((t, e) ->
                        log.error("价格预警线程异常: {}", t.getName(), e));
                    return thread;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }

    /**
     * 执行手动预警检查 - 优化版本
     */
    @Override
    public Map<String, Object> executeManualCheck(String triggerBy) {
        // 参数验证
        if (triggerBy == null || triggerBy.trim().isEmpty()) {
            triggerBy = "unknown";
            log.warn("触发者参数为空，使用默认值: {}", triggerBy);
        }

        long startTime = System.currentTimeMillis();
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始执行手动预警检查，触发者: {}", triggerBy);
            
            // 获取所有启用的配置
            List<VimPriceAlertConfig> enabledConfigs = getEnabledConfigs();
            
            if (enabledConfigs.isEmpty()) {
                return createErrorResult("没有启用的预警规则");
            }
            
            // 动态调整线程池大小
            adjustThreadPoolSize();
            
            // 并行执行各个规则检查
            String finalTriggerBy = triggerBy;
            List<CompletableFuture<RuleExecutionResult>> futures = enabledConfigs.stream()
                    .map(config -> CompletableFuture.supplyAsync(() -> 
                        executeRuleCheckOptimized(config, finalTriggerBy), executorService))
                    .collect(Collectors.toList());
            
            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
            
            try {
                allFutures.get(performanceConfig.getExecution().getTimeoutSeconds(), TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.warn("预警检查执行超时，部分任务可能未完成");
            }
            
            // 收集结果
            ExecutionSummary summary = collectExecutionResults(futures);
            
            // 记录执行日志
            recordExecutionLog("manual", triggerBy, summary, startTime);
            
            result.put("success", true);
            result.put("message", "预警检查执行完成");
            result.put("totalChecked", summary.totalChecked());
            result.put("totalTriggered", summary.totalTriggered());
            result.put("executionTime", System.currentTimeMillis() - startTime);
            result.put("errors", summary.errors());
            
            log.info("手动预警检查完成，检查商品: {}, 触发预警: {}, 耗时: {}ms", 
                summary.totalChecked(), summary.totalTriggered(), 
                System.currentTimeMillis() - startTime);
            
        } catch (Exception e) {
            log.error("执行手动预警检查失败", e);
            result.put("success", false);
            result.put("message", "预警检查执行失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 执行单个规则的优化检查逻辑
     */
    private RuleExecutionResult executeRuleCheckOptimized(VimPriceAlertConfig config, String triggerBy) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始执行规则检查: {}", config.getRuleType());
            
            // 获取需要检查的商品列表
            List<VimItem> items = getItemsForCheck(config);
            
            if (items.isEmpty()) {
                return new RuleExecutionResult(config.getRuleType(), 0, 0,
                    System.currentTimeMillis() - startTime, new ArrayList<>());
            }
            
            // 批量获取价格信息 - 解决N+1查询问题
            Map<String, Map<String, Object>> batchPriceInfo = 
                batchQueryOptimizer.getBatchCrossDatabasePrice(items, config.getDataSources());
            
            log.info("规则 {} 批量获取价格信息完成，商品数量: {}, 获取到价格数量: {}", 
                config.getRuleType(), items.size(), batchPriceInfo.size());
            
            // 并行处理商品预警检查
            List<AlertCheckResult> checkResults = items.parallelStream()
                .map(item -> checkItemAlertOptimized(item, config, batchPriceInfo.get(item.getHashname())))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            // 批量创建预警记录
            List<AlertCheckResult> triggeredAlerts = checkResults.stream()
                .filter(AlertCheckResult::isTriggered)
                .collect(Collectors.toList());
            
            if (!triggeredAlerts.isEmpty()) {
                batchCreateAlertRecords(triggeredAlerts, config, triggerBy);
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            log.info("规则 {} 检查完成，检查商品: {}, 触发预警: {}, 耗时: {}ms", 
                config.getRuleType(), items.size(), triggeredAlerts.size(), executionTime);
            
            return new RuleExecutionResult(config.getRuleType(), items.size(),
                triggeredAlerts.size(), executionTime, new ArrayList<>());
            
        } catch (Exception e) {
            log.error("规则 {} 检查失败", config.getRuleType(), e);
            return new RuleExecutionResult(config.getRuleType(), 0, 0,
                System.currentTimeMillis() - startTime, Arrays.asList(e.getMessage()));
        }
    }

    /**
     * 优化的商品预警检查方法
     */
    private AlertCheckResult checkItemAlertOptimized(VimItem item, VimPriceAlertConfig config,
            Map<String, Object> priceInfo) {
        try {
            // 参数验证
            if (item == null) {
                log.warn("商品对象为空，跳过预警检查");
                return null;
            }

            if (config == null) {
                log.warn("配置对象为空，跳过商品 {} 的预警检查", item.getName());
                return null;
            }

            if (priceInfo == null || priceInfo.isEmpty()) {
                log.debug("商品 {} 没有价格信息，跳过预警检查", item.getName());
                return null;
            }

            BigDecimal currentPrice = priceCalculationUtil.getCurrentPriceFromInfo(priceInfo, config);
            if (currentPrice == null) {
                log.debug("商品 {} 无法获取当前价格，跳过预警检查", item.getName());
                return null;
            }

            boolean isTriggered = priceCalculationUtil.checkPriceAlert(item, config, currentPrice);

            return new AlertCheckResult(item, config, currentPrice, priceInfo, isTriggered);

        } catch (Exception e) {
            String itemName = item != null ? item.getName() : "unknown";
            log.warn("商品 {} 预警检查异常: {}", itemName, e.getMessage());
            return null;
        }
    }

    /**
     * 批量创建预警记录
     */
    @Transactional
    private void batchCreateAlertRecords(List<AlertCheckResult> triggeredAlerts, 
            VimPriceAlertConfig config, String triggerBy) {
        try {
            List<VimPriceAlertRecordSimple> records = triggeredAlerts.stream()
                .map(alert -> createAlertRecord(alert, config, triggerBy))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            
            if (!records.isEmpty()) {
                // 批量插入记录
                batchQueryOptimizer.batchInsertAlertRecords(records);
                log.info("批量创建预警记录完成，数量: {}", records.size());
            }
            
        } catch (Exception e) {
            log.error("批量创建预警记录失败", e);
        }
    }

    /**
     * 创建单个预警记录
     */
    private VimPriceAlertRecordSimple createAlertRecord(AlertCheckResult alert, 
            VimPriceAlertConfig config, String triggerBy) {
        try {
            VimItem item = alert.item();
            BigDecimal currentPrice = alert.currentPrice();
            
            VimPriceAlertRecordSimple record = new VimPriceAlertRecordSimple();
            record.setRuleType(config.getRuleType());
            record.setItemId(item.getId());
            record.setItemName(item.getName());
            record.setItemHashname(item.getHashname());
            record.setOldPrice(item.getPriceShow());
            record.setNewPrice(currentPrice);
            
            // 使用工具类计算价格变化
            BigDecimal[] changes = priceCalculationUtil.calculatePriceChange(
                item.getPriceShow(), currentPrice);
            record.setChangeAmount(changes[0]);
            record.setChangePercentage(changes[1]);
            
            record.setThresholdValue(config.getThresholdValue());
            record.setThresholdType(config.getThresholdType());
            record.setAlertLevel(determineAlertLevel(changes[1], config));
            record.setDataSource(config.getDataSources());
            record.setAlertTime(new Date());
            record.setAlertStatus(1); // 待处理
            
            return record;
            
        } catch (Exception e) {
            log.error("创建预警记录失败，商品: {}", alert.item().getName(), e);
            return null;
        }
    }

    /**
     * 资源清理
     */
    @PreDestroy
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    // ========== 接口方法实现 ==========

    @Override
    public List<VimPriceAlertConfig> getAllConfigs() {
        try {
            return configMapper.selectAllConfigs();
        } catch (Exception e) {
            log.error("获取所有预警配置失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public VimPriceAlertConfig getConfigByRuleType(String ruleType) {
        // 参数验证
        if (ruleType == null || ruleType.trim().isEmpty()) {
            log.warn("规则类型参数为空");
            return null;
        }

        try {
            return configMapper.selectConfigByRuleType(ruleType.trim());
        } catch (Exception e) {
            log.error("根据规则类型获取配置失败，ruleType: {}", ruleType, e);
            return null;
        }
    }

    @Override
    @Transactional
    public int updateConfig(VimPriceAlertConfig config) {
        // 参数验证
        if (config == null) {
            log.warn("配置对象为空，无法更新");
            return 0;
        }

        if (config.getId() == null) {
            log.warn("配置ID为空，无法更新");
            return 0;
        }

        // 验证必要字段
        if (config.getRuleType() == null || config.getRuleType().trim().isEmpty()) {
            log.warn("规则类型为空，无法更新配置");
            return 0;
        }

        // 验证阈值相关字段
        if (config.getThresholdValue() == null || config.getThresholdValue().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("阈值数值无效，无法更新配置，configId: {}", config.getId());
            return 0;
        }

        if (config.getThresholdType() == null || (config.getThresholdType() != 1 && config.getThresholdType() != 2)) {
            log.warn("阈值类型无效，无法更新配置，configId: {}, thresholdType: {}",
                config.getId(), config.getThresholdType());
            return 0;
        }

        // 验证数据源字段
        if (config.getDataSources() == null || config.getDataSources().trim().isEmpty()) {
            log.warn("数据源为空，无法更新配置，configId: {}", config.getId());
            return 0;
        }

        try {
            // 数据预处理
            preprocessConfigData(config);

            config.setUpdateTime(DateUtils.getDate());

            log.info("更新价格预警配置，configId: {}, ruleType: {}, thresholdType: {}, thresholdValue: {}, dataSources: {}",
                config.getId(), config.getRuleType(), config.getThresholdType(),
                config.getThresholdValue(), config.getDataSources());

            return configMapper.updateConfig(config);
        } catch (Exception e) {
            log.error("更新预警配置失败，configId: {}", config.getId(), e);
            return 0;
        }
    }

    /**
     * 预处理配置数据
     */
    private void preprocessConfigData(VimPriceAlertConfig config) {
        // 确保数据源优先级字段有值
        if (config.getDataSourcePriority() == null || config.getDataSourcePriority().trim().isEmpty()) {
            config.setDataSourcePriority(config.getDataSources());
        }

        // 标准化数据源格式
        String dataSources = config.getDataSources().trim();
        if ("all".equals(dataSources)) {
            dataSources = "buff,steam,c5game";
            config.setDataSources(dataSources);
            config.setDataSourcePriority(dataSources);
        }

        // 验证阈值范围
        BigDecimal thresholdValue = config.getThresholdValue();
        if (config.getThresholdType() == 1) { // 百分比
            if (thresholdValue.compareTo(new BigDecimal("0.1")) < 0) {
                config.setThresholdValue(new BigDecimal("0.1"));
            } else if (thresholdValue.compareTo(new BigDecimal("100")) > 0) {
                config.setThresholdValue(new BigDecimal("100"));
            }
        } else { // 固定金额
            if (thresholdValue.compareTo(new BigDecimal("0.01")) < 0) {
                config.setThresholdValue(new BigDecimal("0.01"));
            } else if (thresholdValue.compareTo(new BigDecimal("9999")) > 0) {
                config.setThresholdValue(new BigDecimal("9999"));
            }
        }

        // 确保最低价格过滤值有效
        if (config.getMinPriceFilter() == null || config.getMinPriceFilter().compareTo(BigDecimal.ZERO) < 0) {
            config.setMinPriceFilter(BigDecimal.ZERO);
        }
    }

    @Override
    @Transactional
    public int batchUpdateConfigs(List<VimPriceAlertConfig> configs) {
        // 参数验证
        if (configs == null || configs.isEmpty()) {
            log.warn("配置列表为空，无法批量更新");
            return 0;
        }

        try {
            int result = 0;
            int successCount = 0;
            int failCount = 0;

            for (VimPriceAlertConfig config : configs) {
                int updateResult = updateConfig(config);
                result += updateResult;
                if (updateResult > 0) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            log.info("批量更新配置完成，总数: {}, 成功: {}, 失败: {}",
                configs.size(), successCount, failCount);

            return result;
        } catch (Exception e) {
            log.error("批量更新预警配置失败", e);
            return 0;
        }
    }

    @Override
    @Transactional
    public int toggleRuleStatus(String ruleType, boolean enabled) {
        // 参数验证
        if (ruleType == null || ruleType.trim().isEmpty()) {
            log.warn("规则类型参数为空，无法切换状态");
            return 0;
        }

        try {
            int result = configMapper.toggleConfigStatus(ruleType.trim(), enabled ? 1 : 0);
            if (result > 0) {
                log.info("规则状态切换成功，ruleType: {}, enabled: {}", ruleType, enabled);
            } else {
                log.warn("规则状态切换失败，可能规则不存在，ruleType: {}", ruleType);
            }
            return result;
        } catch (Exception e) {
            log.error("切换规则状态失败，ruleType: {}, enabled: {}", ruleType, enabled, e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> executeRuleCheck(String ruleType, String triggerBy) {
        VimPriceAlertConfig config = getConfigByRuleType(ruleType);
        if (config == null || config.getIsEnabled() != 1) {
            return createErrorResult("规则不存在或未启用");
        }

        RuleExecutionResult result = executeRuleCheckOptimized(config, triggerBy);

        Map<String, Object> response = new HashMap<>();
        response.put("success", result.errors().isEmpty());
        response.put("ruleType", result.ruleType());
        response.put("checkedCount", result.checkedCount());
        response.put("triggeredCount", result.triggeredCount());
        response.put("executionTime", result.executionTime());
        response.put("errors", result.errors());

        return response;
    }

    @Override
    public Map<String, Object> executeConfigChangedCheck(String ruleType, String triggerBy) {
        log.info("配置变更触发预警检查，规则类型：{}，触发者：{}", ruleType, triggerBy);
        return executeRuleCheck(ruleType, triggerBy);
    }

    @Override
    public Map<String, Object> executeScheduledCheck() {
        log.info("开始执行定时预警检查");
        return executeManualCheck("system");
    }

    @Override
    public List<VimPriceAlertRecordSimple> selectRecordList(VimPriceAlertRecordSimple record) {
        try {
            return recordMapper.selectRecordList(record);
        } catch (Exception e) {
            log.error("查询预警记录列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public VimPriceAlertRecordSimple selectRecordById(Long id) {
        try {
            return recordMapper.selectRecordById(id);
        } catch (Exception e) {
            log.error("根据ID查询预警记录失败，id: {}", id, e);
            return null;
        }
    }

    @Override
    @Transactional
    public int handleRecord(Long id, String handleBy, String handleRemark, Integer status) {
        // 参数验证
        if (id == null || id <= 0) {
            log.warn("记录ID无效，无法处理记录");
            return 0;
        }

        if (handleBy == null || handleBy.trim().isEmpty()) {
            log.warn("处理人参数为空，记录ID: {}", id);
            return 0;
        }

        if (status == null || (status != 1 && status != 2 && status != 3)) {
            log.warn("状态参数无效，记录ID: {}, status: {}", id, status);
            return 0;
        }

        try {
            VimPriceAlertRecordSimple record = new VimPriceAlertRecordSimple();
            record.setId(id);
            record.setAlertStatus(status);
            record.setHandleBy(handleBy.trim());
            record.setHandleRemark(handleRemark != null ? handleRemark.trim() : "");
            record.setHandleTime(new Date());

            int result = recordMapper.updateRecord(record);
            if (result > 0) {
                log.info("预警记录处理成功，id: {}, handleBy: {}, status: {}", id, handleBy, status);
            }
            return result;
        } catch (Exception e) {
            log.error("处理预警记录失败，id: {}", id, e);
            return 0;
        }
    }

    @Override
    @Transactional
    public int batchHandleRecords(Long[] ids, String handleBy, String handleRemark, Integer status) {
        // 参数验证
        if (ids == null || ids.length == 0) {
            log.warn("记录ID数组为空，无法批量处理");
            return 0;
        }

        if (handleBy == null || handleBy.trim().isEmpty()) {
            log.warn("处理人参数为空，无法批量处理记录");
            return 0;
        }

        try {
            int result = 0;
            int successCount = 0;
            int failCount = 0;

            for (Long id : ids) {
                if (id != null && id > 0) {
                    int handleResult = handleRecord(id, handleBy, handleRemark, status);
                    result += handleResult;
                    if (handleResult > 0) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } else {
                    failCount++;
                    log.warn("跳过无效的记录ID: {}", id);
                }
            }

            log.info("批量处理预警记录完成，总数: {}, 成功: {}, 失败: {}",
                ids.length, successCount, failCount);

            return result;
        } catch (Exception e) {
            log.error("批量处理预警记录失败", e);
            return 0;
        }
    }

    @Override
    @Transactional
    public int deleteRecordByIds(Long[] ids) {
        try {
            return recordMapper.deleteRecordByIds(ids);
        } catch (Exception e) {
            log.error("删除预警记录失败", e);
            return 0;
        }
    }

    // ========== 辅助方法实现 ==========

    /**
     * 获取启用的配置
     */
    private List<VimPriceAlertConfig> getEnabledConfigs() {
        return getAllConfigs().stream()
                .filter(config -> config.getIsEnabled() == 1)
                .collect(Collectors.toList());
    }

    /**
     * 获取需要检查的商品列表
     */
    private List<VimItem> getItemsForCheck(VimPriceAlertConfig config) {
        // 参数验证
        if (config == null) {
            log.warn("配置对象为空，无法获取商品列表");
            return new ArrayList<>();
        }

        try {
            VimItem queryItem = new VimItem();
            List<VimItem> allItems = vimItemMapper.selectVimItemList(queryItem);

            if (allItems == null || allItems.isEmpty()) {
                log.info("没有找到商品数据");
                return new ArrayList<>();
            }

            BigDecimal minPriceFilter = config.getMinPriceFilter();
            if (minPriceFilter == null) {
                minPriceFilter = BigDecimal.ZERO;
            }

            final BigDecimal finalMinPriceFilter = minPriceFilter;

            List<VimItem> filteredItems = allItems.stream()
                    .filter(item -> item != null) // 过滤空对象
                    .filter(item -> item.getDeleteTime() == null) // 未删除
                    .filter(item -> item.getPriceShow() != null &&
                            item.getPriceShow().compareTo(finalMinPriceFilter) >= 0) // 价格过滤
                    .filter(item -> item.getHashname() != null && !item.getHashname().trim().isEmpty()) // 有效hashname
                    .collect(Collectors.toList());

            log.info("获取商品列表完成，总数: {}, 过滤后: {}", allItems.size(), filteredItems.size());
            return filteredItems;

        } catch (Exception e) {
            log.error("获取商品列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 动态调整线程池大小
     */
    private void adjustThreadPoolSize() {
        try {
            int configuredSize = performanceConfig.getExecution().getThreadPoolSize();
            if (executorService.getCorePoolSize() != configuredSize) {
                executorService.setCorePoolSize(configuredSize);
                executorService.setMaximumPoolSize(configuredSize * 2);
                log.info("线程池大小已调整为: {}", configuredSize);
            }
        } catch (Exception e) {
            log.warn("调整线程池大小失败", e);
        }
    }

    /**
     * 收集执行结果
     */
    private ExecutionSummary collectExecutionResults(List<CompletableFuture<RuleExecutionResult>> futures) {
        int totalChecked = 0;
        int totalTriggered = 0;
        List<String> allErrors = new ArrayList<>();

        for (CompletableFuture<RuleExecutionResult> future : futures) {
            try {
                RuleExecutionResult result = future.get();
                totalChecked += result.checkedCount();
                totalTriggered += result.triggeredCount();
                allErrors.addAll(result.errors());
            } catch (Exception e) {
                log.error("收集执行结果失败", e);
                allErrors.add("执行结果收集失败: " + e.getMessage());
            }
        }

        return new ExecutionSummary(totalChecked, totalTriggered, 0, allErrors);
    }

    /**
     * 记录执行日志
     */
    private void recordExecutionLog(String executionType, String triggerBy,
            ExecutionSummary summary, long startTime) {
        try {
            VimPriceAlertExecutionLog log = new VimPriceAlertExecutionLog();
            log.setExecutionType(executionType);
            log.setExecutionTime(new Date());
            log.setTotalItemsChecked(summary.totalChecked());
            log.setTriggeredAlerts(summary.totalTriggered());
            log.setExecutionDuration((int) (System.currentTimeMillis() - startTime));
            log.setExecutionStatus(summary.errors().isEmpty() ? 1 : 2);
            log.setErrorMessage(summary.errors().isEmpty() ? null :
                String.join("; ", summary.errors()));
            log.setTriggerBy(triggerBy);

            executionLogMapper.insertExecutionLog(log);
        } catch (Exception e) {
            log.error("记录执行日志失败", e);
        }
    }

    /**
     * 确定预警级别
     */
    private Integer determineAlertLevel(BigDecimal changePercentage, VimPriceAlertConfig config) {
        if (changePercentage == null) {
            return 1; // 低级别
        }

        BigDecimal absChange = changePercentage.abs();
        BigDecimal threshold = config.getThresholdValue();

        if (absChange.compareTo(threshold.multiply(new BigDecimal("2"))) >= 0) {
            return 3; // 高级别
        } else if (absChange.compareTo(threshold.multiply(new BigDecimal("1.5"))) >= 0) {
            return 2; // 中级别
        } else {
            return 1; // 低级别
        }
    }

    /**
     * 创建错误结果
     */
    private Map<String, Object> createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        return result;
    }

    /**
     * 记录类型定义
     */
    public static class RuleExecutionResult {
        private final String ruleType;
        private final int checkedCount;
        private final int triggeredCount;
        private final long executionTime;
        private final List<String> errors;

        public RuleExecutionResult(String ruleType, int checkedCount, int triggeredCount,
                long executionTime, List<String> errors) {
            this.ruleType = ruleType;
            this.checkedCount = checkedCount;
            this.triggeredCount = triggeredCount;
            this.executionTime = executionTime;
            this.errors = errors;
        }

        public String ruleType() { return ruleType; }
        public int checkedCount() { return checkedCount; }
        public int triggeredCount() { return triggeredCount; }
        public long executionTime() { return executionTime; }
        public List<String> errors() { return errors; }
    }

    public static class AlertCheckResult {
        private final VimItem item;
        private final VimPriceAlertConfig config;
        private final BigDecimal currentPrice;
        private final Map<String, Object> priceInfo;
        private final boolean isTriggered;

        public AlertCheckResult(VimItem item, VimPriceAlertConfig config, BigDecimal currentPrice,
                Map<String, Object> priceInfo, boolean isTriggered) {
            this.item = item;
            this.config = config;
            this.currentPrice = currentPrice;
            this.priceInfo = priceInfo;
            this.isTriggered = isTriggered;
        }

        public VimItem item() { return item; }
        public VimPriceAlertConfig config() { return config; }
        public BigDecimal currentPrice() { return currentPrice; }
        public Map<String, Object> priceInfo() { return priceInfo; }
        public boolean isTriggered() { return isTriggered; }
    }

    public static class ExecutionSummary {
        private final int totalChecked;
        private final int totalTriggered;
        private final long totalExecutionTime;
        private final List<String> errors;

        public ExecutionSummary(int totalChecked, int totalTriggered,
                long totalExecutionTime, List<String> errors) {
            this.totalChecked = totalChecked;
            this.totalTriggered = totalTriggered;
            this.totalExecutionTime = totalExecutionTime;
            this.errors = errors;
        }

        public int totalChecked() { return totalChecked; }
        public int totalTriggered() { return totalTriggered; }
        public long totalExecutionTime() { return totalExecutionTime; }
        public List<String> errors() { return errors; }
    }

    // ========== 剩余接口方法实现 ==========

    @Override
    public List<VimPriceAlertExecutionLog> selectExecutionLogList(VimPriceAlertExecutionLog logParam) {
        try {
            return executionLogMapper.selectExecutionLogList(logParam);
        } catch (Exception e) {
            log.error("查询执行日志列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public VimPriceAlertExecutionLog selectExecutionLogById(Long id) {
        try {
            return executionLogMapper.selectExecutionLogById(id);
        } catch (Exception e) {
            log.error("根据ID查询执行日志失败，id: {}", id, e);
            return null;
        }
    }

    @Override
    @Transactional
    public int cleanExpiredExecutionLogs(int retentionDays) {
        try {
            return executionLogMapper.cleanExpiredLogs(retentionDays);
        } catch (Exception e) {
            log.error("清理过期执行日志失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getAlertStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        try {
            // 基础统计数据
            int totalRecords = recordMapper.countAllRecords();
            int pendingRecords = recordMapper.countPendingRecords();
            int todayRecords = recordMapper.countTodayRecords();
            int processedRecords = totalRecords - pendingRecords; // 计算已处理数量

            statistics.put("totalAlerts", totalRecords);
            statistics.put("todayAlerts", todayRecords);
            statistics.put("pendingAlerts", pendingRecords);
            statistics.put("processedAlerts", processedRecords);

            // 配置统计
            statistics.put("totalConfigs", configMapper.countAllConfigs());
            statistics.put("enabledConfigs", configMapper.countEnabledConfigs());

            // 规则统计
            List<Map<String, Object>> ruleStats = recordMapper.getRecordStatsByRuleType();
            statistics.put("ruleStats", ruleStats);
            statistics.put("ruleTypeDistribution", ruleStats);

            // 数据源统计 - 前端期望的数据
            try {
                List<Map<String, Object>> dataSourceStats = recordMapper.getDataSourceStatistics();
                // 计算百分比
                int total = dataSourceStats.stream()
                    .mapToInt(stat -> ((Number) stat.get("alertCount")).intValue())
                    .sum();

                dataSourceStats.forEach(stat -> {
                    int count = ((Number) stat.get("alertCount")).intValue();
                    double percentage = total > 0 ? (double) count / total * 100 : 0;
                    stat.put("percentage", Math.round(percentage * 100.0) / 100.0);
                });

                statistics.put("dataSourceStats", dataSourceStats);
            } catch (Exception e) {
                log.warn("获取数据源统计失败，使用空数据", e);
                statistics.put("dataSourceStats", new ArrayList<>());
            }

        } catch (Exception e) {
            log.error("获取预警统计信息失败", e);
            // 返回默认值避免前端错误
            statistics.put("totalAlerts", 0);
            statistics.put("todayAlerts", 0);
            statistics.put("pendingAlerts", 0);
            statistics.put("processedAlerts", 0);
            statistics.put("totalConfigs", 0);
            statistics.put("enabledConfigs", 0);
            statistics.put("ruleStats", new ArrayList<>());
            statistics.put("ruleTypeDistribution", new ArrayList<>());
            statistics.put("dataSourceStats", new ArrayList<>());
        }
        return statistics;
    }

    @Override
    public List<Map<String, Object>> getAlertTrendData(int days) {
        try {
            return recordMapper.getAlertTrendData(days);
        } catch (Exception e) {
            log.error("获取预警趋势数据失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getItemAlertRanking(int limit) {
        try {
            return recordMapper.getItemAlertRanking(limit);
        } catch (Exception e) {
            log.error("获取商品预警排行失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getDataSourceStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        try {
            List<Map<String, Object>> dataSourceStats = recordMapper.getDataSourceStatistics();
            statistics.put("dataSourceStats", dataSourceStats);

            // 计算总数和百分比
            int total = dataSourceStats.stream()
                .mapToInt(stat -> ((Number) stat.get("count")).intValue())
                .sum();
            statistics.put("total", total);

            // 为每个数据源添加百分比
            dataSourceStats.forEach(stat -> {
                int count = ((Number) stat.get("count")).intValue();
                double percentage = total > 0 ? (double) count / total * 100 : 0;
                stat.put("percentage", Math.round(percentage * 100.0) / 100.0);
            });

            return statistics;
        } catch (Exception e) {
            log.error("获取数据源统计失败", e);
            statistics.put("dataSourceStats", new ArrayList<>());
            statistics.put("total", 0);
            return statistics;
        }
    }

    @Override
    public Map<String, Object> getSystemHealthStatus() {
        Map<String, Object> health = new HashMap<>();
        try {
            // 检查数据库连接
            boolean dbConnected = checkDatabaseConnection();
            health.put("databaseConnected", dbConnected);

            // 检查配置状态
            List<VimPriceAlertConfig> configs = getAllConfigs();
            boolean configsValid = configs.size() >= 2 &&
                configs.stream().anyMatch(c -> c.getIsEnabled() == 1);
            health.put("configsValid", configsValid);

            // 检查线程池状态
            health.put("threadPoolActive", !executorService.isShutdown());
            health.put("threadPoolSize", executorService.getPoolSize());
            health.put("activeThreads", executorService.getActiveCount());

            // 检查内存使用
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            double memoryUsage = (double) (totalMemory - freeMemory) / totalMemory * 100;
            health.put("memoryUsagePercent", memoryUsage);
            health.put("memoryHealthy", memoryUsage < 80);

            // 整体健康状态
            boolean overallHealthy = dbConnected && configsValid &&
                !executorService.isShutdown() && memoryUsage < 90;
            health.put("overallHealthy", overallHealthy);

        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            health.put("overallHealthy", false);
            health.put("error", e.getMessage());
        }
        return health;
    }

    @Override
    public Map<String, Object> getSystemConfig() {
        Map<String, Object> config = new HashMap<>();
        try {
            List<VimPriceAlertConfig> alertConfigs = getAllConfigs();
            config.put("alertConfigs", alertConfigs);

            // 性能配置
            config.put("batchSize", performanceConfig.getBatchQuery().getBatchSize());
            config.put("threadPoolSize", performanceConfig.getExecution().getThreadPoolSize());
            config.put("timeoutSeconds", performanceConfig.getExecution().getTimeoutSeconds());
            config.put("cacheEnabled", performanceConfig.getCache().isEnabled());
            config.put("cacheTtl", performanceConfig.getCache().getPriceInfoTtl());

        } catch (Exception e) {
            log.error("获取系统配置失败", e);
        }
        return config;
    }

    @Override
    @Transactional
    public int updateSystemConfig(Map<String, Object> config) {
        try {
            // 这里可以实现系统配置的更新逻辑
            log.info("更新系统配置: {}", config);
            return 1;
        } catch (Exception e) {
            log.error("更新系统配置失败", e);
            return 0;
        }
    }

    @Override
    @Transactional
    public int resetSystemConfig() {
        try {
            return configMapper.resetConfigToDefault("price_up") +
                   configMapper.resetConfigToDefault("price_down");
        } catch (Exception e) {
            log.error("重置系统配置失败", e);
            return 0;
        }
    }

    @Override
    public List<Map<String, Object>> selectItemPriceAnalysisList(VimItem vimItem) {
        try {
            // 🔧 检查是否有涨跌状态筛选
            boolean hasPriceStatusFilter = vimItem.getParams() != null &&
                vimItem.getParams().get("priceStatus") != null &&
                !vimItem.getParams().get("priceStatus").toString().trim().isEmpty();

            // 🔧 如果有涨跌状态筛选，需要特殊处理（因为涨跌状态是计算字段）
            if (hasPriceStatusFilter) {
                return selectItemPriceAnalysisListWithPriceStatusFilter(vimItem);
            }

            // 正常流程：使用PageHelper分页
            List<VimItem> items = vimItemMapper.selectVimItemList(vimItem);

            // 记录查询结果数量用于调试
            log.debug("查询商品价格分析列表，获取到 {} 条记录", items.size());

            // 🔧 修复分页问题：在Stream操作前保存分页信息
            // PageHelper返回的是包含分页信息的特殊List（Page对象）
            // Stream操作会创建新的ArrayList，导致分页信息丢失
            // 因此需要在Stream操作前保存分页信息，然后重新构建包含分页信息的结果

            // 检查是否是PageHelper的分页结果
            boolean isPageHelperResult = items instanceof com.github.pagehelper.Page;
            long total = 0;
            int pageNum = 1;
            int pageSize = 10;

            if (isPageHelperResult) {
                com.github.pagehelper.Page<VimItem> page = (com.github.pagehelper.Page<VimItem>) items;
                total = page.getTotal();
                pageNum = page.getPageNum();
                pageSize = page.getPageSize();
                log.debug("PageHelper分页信息 - 总记录数: {}, 当前页: {}, 每页大小: {}", total, pageNum, pageSize);
            }

            // 🔧 性能优化：批量获取所有商品的价格信息，解决N+1查询问题
            long priceQueryStart = System.currentTimeMillis();
            Map<String, Map<String, Object>> batchPriceInfo =
                batchQueryOptimizer.getBatchCrossDatabasePrice(items, "buff,steam");
            long priceQueryTime = System.currentTimeMillis() - priceQueryStart;

            log.info("批量价格查询完成 - 商品数量: {}, 获取价格数量: {}, 耗时: {}ms",
                items.size(), batchPriceInfo.size(), priceQueryTime);

            // 🔧 使用批量结果构建分析数据，避免重复查询
            List<Map<String, Object>> resultList = items.stream()
                .map(item -> buildItemPriceAnalysisWithBatchData(item, batchPriceInfo.get(item.getHashname())))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            // 🔧 如果是分页结果，需要重新构建包含分页信息的Page对象
            if (isPageHelperResult) {
                com.github.pagehelper.Page<Map<String, Object>> resultPage = new com.github.pagehelper.Page<>(pageNum, pageSize);
                resultPage.setTotal(total);
                resultPage.addAll(resultList);
                return resultPage;
            }

            return resultList;
        } catch (Exception e) {
            log.error("查询商品价格分析列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getItemPriceAnalysisById(Long itemId) {
        try {
            VimItem item = vimItemMapper.selectVimItemById(itemId);
            if (item == null) {
                return createErrorResult("商品不存在");
            }
            return buildItemPriceAnalysis(item);
        } catch (Exception e) {
            log.error("获取商品价格分析失败，itemId: {}", itemId, e);
            return createErrorResult("获取价格分析失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Map<String, Object> batchUpdateItemPriceAnalysis(Long[] itemIds) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Integer> itemIdList = Arrays.stream(itemIds)
                .map(Long::intValue)
                .collect(Collectors.toList());

            List<VimItem> items = vimItemMapper.selectVimItemByIds(itemIdList);

            // 批量获取价格信息
            Map<String, Map<String, Object>> batchPriceInfo =
                batchQueryOptimizer.getBatchCrossDatabasePrice(items, "buff,steam");

            int successCount = 0;
            int failCount = 0;

            // 🔧 修复：为每个商品设置统一的更新时间戳
            long currentTimestamp = System.currentTimeMillis() / 1000;

            for (VimItem item : items) {
                try {
                    Map<String, Object> priceInfo = batchPriceInfo.get(item.getHashname());
                    if (priceInfo != null && !priceInfo.isEmpty()) {
                        updateItemPricesFromPriceInfo(item, priceInfo);
                        // 🔧 修复：批量更新时为每个商品设置更新时间
                        item.setUpdateTimeStamp(currentTimestamp);
                        vimItemMapper.updateVimItem(item);
                        successCount++;
                    } else {
                        failCount++;
                        log.warn("商品 {} 未找到价格信息", item.getName());
                    }
                } catch (Exception e) {
                    failCount++;
                    log.warn("更新商品价格失败，商品: {}", item.getName(), e);
                }
            }

            result.put("success", true);
            result.put("message", "批量更新完成");
            result.put("totalItems", items.size());
            result.put("successCount", successCount);
            result.put("failCount", failCount);

            log.info("批量更新商品价格完成 - 总数: {}, 成功: {}, 失败: {}",
                items.size(), successCount, failCount);

        } catch (Exception e) {
            log.error("批量更新商品价格分析失败", e);
            result.put("success", false);
            result.put("message", "批量更新失败: " + e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> updateSingleItemPrice(Long itemId) {
        Map<String, Object> result = new HashMap<>();
        try {
            VimItem item = vimItemMapper.selectVimItemById(itemId);
            if (item == null) {
                return createErrorResult("商品不存在");
            }

            Map<String, Object> priceInfo = batchQueryOptimizer
                .getBatchCrossDatabasePrice(List.of(item), "buff,steam")
                .get(item.getHashname());

            if (priceInfo == null || priceInfo.isEmpty()) {
                return createErrorResult("未找到价格信息");
            }

            updateItemPricesFromPriceInfo(item, priceInfo);
            // 🔧 修复：更新价格时同时更新lastPriceUpdate时间戳
            item.setUpdateTimeStamp(System.currentTimeMillis() / 1000);
            vimItemMapper.updateVimItem(item);

            result.put("success", true);
            result.put("message", "价格更新成功");
            result.put("item", item);

        } catch (Exception e) {
            log.error("更新单个商品价格失败，itemId: {}", itemId, e);
            result.put("success", false);
            result.put("message", "价格更新失败: " + e.getMessage());
        }
        return result;
    }

    @Override
    public Map<String, Object> testPriceQuery(String hashname) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> priceInfo = batchQueryOptimizer
                .getBatchCrossDatabasePrice(List.of(), "buff,steam,c5game")
                .getOrDefault(hashname, new HashMap<>());

            result.put("hashname", hashname);
            result.put("priceInfo", priceInfo);
            result.put("success", !priceInfo.isEmpty());
            result.put("timestamp", System.currentTimeMillis());
            result.put("message", priceInfo.isEmpty() ? "未找到价格信息" : "价格查询成功");

        } catch (Exception e) {
            log.error("测试价格查询失败，hashname: {}", hashname, e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    // ========== 私有辅助方法 ==========

    private boolean checkDatabaseConnection() {
        try {
            configMapper.countAllConfigs();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 🔧 性能优化：使用预获取的批量数据构建商品价格分析
     * 避免N+1查询问题
     */
    private Map<String, Object> buildItemPriceAnalysisWithBatchData(VimItem item, Map<String, Object> priceInfo) {
        try {
            Map<String, Object> analysis = new HashMap<>();
            analysis.put("id", item.getId());
            analysis.put("name", item.getName());
            analysis.put("hashname", item.getHashname());
            analysis.put("priceShow", item.getPriceShow());
            analysis.put("priceCost", item.getPriceCost());
            analysis.put("priceBuy", item.getPriceBuy());
            analysis.put("priceRecycle", item.getPriceRecycle());
            analysis.put("tag", item.getTag());
            analysis.put("image", item.getImage());
            analysis.put("stock", item.getStock());
            analysis.put("sale", item.getSale());
            analysis.put("createTime", item.getCreateTime());
            analysis.put("updateTime", item.getUpdateTime());
            // 🔧 修复：使用updateTimeStamp作为价格更新时间，实现时间字段的备选逻辑
            Long lastPriceUpdate = item.getUpdateTimeStamp() != null ? item.getUpdateTimeStamp() :
                                  (item.getCreateTimeStamp() != null ? item.getCreateTimeStamp() : null);
            analysis.put("lastPriceUpdate", lastPriceUpdate);

            // 🔧 直接使用传入的价格信息，无需再次查询数据库
            if (priceInfo != null && !priceInfo.isEmpty()) {
                BigDecimal referencePrice = (BigDecimal) priceInfo.get("sell_min_price");
                if (referencePrice != null && item.getPriceShow() != null) {
                    // 展示价格 = 参考价格 × 1.03，所以基础价格 = 展示价格 ÷ 1.03
                    BigDecimal basePrice = item.getPriceShow().divide(PriceCalculationUtil.PRICE_SHOW_MULTIPLIER, 2, RoundingMode.HALF_UP);

                    // 计算价格差值：参考价格 - 基础价格
                    BigDecimal absoluteDifference = referencePrice.subtract(basePrice);

                    // 计算变化百分比：(参考价格 - 基础价格) / 基础价格 * 100
                    BigDecimal percentDifference = BigDecimal.ZERO;
                    if (basePrice.compareTo(BigDecimal.ZERO) > 0) {
                        percentDifference = absoluteDifference
                            .divide(basePrice, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                    }
                    
                    // 🔧 注意：这里的absoluteDifference已经在上面计算过了，使用正确的基础价格
                    
                    // 确定价格状态
                    String priceStatus = determinePriceStatus(percentDifference);
                    
                    // 设置价格状态文本
                    String priceStatusText;
                    String priceStatusColor;
                    
                    switch (priceStatus) {
                        case "up":
                            priceStatusText = "上涨";
                            priceStatusColor = "#f56c6c"; // 红色
                            break;
                        case "down":
                            priceStatusText = "下跌";
                            priceStatusColor = "#67c23a"; // 绿色
                            break;
                        case "stable":
                            priceStatusText = "持平";
                            priceStatusColor = "#909399"; // 灰色
                            break;
                        default:
                            priceStatusText = "无数据";
                            priceStatusColor = "#e6a23c"; // 黄色
                            break;
                    }
                    
                    // 添加所有必要字段
                    analysis.put("referenceSellMinPrice", referencePrice);
                    analysis.put("basePrice", basePrice); // 添加基础价格用于调试
                    analysis.put("priceDifference", absoluteDifference);
                    analysis.put("priceChangePercent", percentDifference);
                    analysis.put("priceStatus", priceStatus);
                    analysis.put("priceStatusText", priceStatusText);
                    analysis.put("priceStatusColor", priceStatusColor);

                    // 保留原字段以兼容可能的旧代码
                    analysis.put("priceDifferencePercentage", percentDifference);

                    log.debug("商品 {} 价格分析 - 展示价格: {}, 基础价格: {}, 参考价格: {}, 差值: {}, 百分比: {}%",
                        item.getName(), item.getPriceShow(), basePrice, referencePrice,
                        absoluteDifference, percentDifference);
                }
            }

            return analysis;
        } catch (Exception e) {
            log.warn("构建商品价格分析失败，商品: {}", item.getName(), e);
            return null;
        }
    }

    /**
     * 🔧 保留原方法用于单个商品查询（向后兼容）
     * 注意：此方法仍存在N+1查询问题，仅用于单个商品查询场景
     */
    private Map<String, Object> buildItemPriceAnalysis(VimItem item) {
        try {
            // 单个商品查询时，仍使用原逻辑
            Map<String, Object> priceInfo = batchQueryOptimizer
                .getBatchCrossDatabasePrice(List.of(item), "buff,steam")
                .get(item.getHashname());

            return buildItemPriceAnalysisWithBatchData(item, priceInfo);
        } catch (Exception e) {
            log.warn("构建商品价格分析失败，商品: {}", item.getName(), e);
            return null;
        }
    }

    /**
     * 🔧 处理带有涨跌状态筛选的商品价格分析查询
     * 由于涨跌状态是计算字段，需要特殊处理分页逻辑
     */
    private List<Map<String, Object>> selectItemPriceAnalysisListWithPriceStatusFilter(VimItem vimItem) {
        try {
            String priceStatusFilter = vimItem.getParams().get("priceStatus").toString();
            log.debug("处理涨跌状态筛选查询，筛选条件: {}", priceStatusFilter);

            // 临时移除涨跌状态筛选参数，避免SQL查询报错
            Object originalPriceStatus = vimItem.getParams().remove("priceStatus");

            // 获取所有符合其他条件的商品（不分页）
            List<VimItem> allItems = vimItemMapper.selectVimItemList(vimItem);

            // 恢复原始参数
            vimItem.getParams().put("priceStatus", originalPriceStatus);

            log.debug("获取到符合基础条件的商品数量: {}", allItems.size());

            // 批量获取价格信息
            Map<String, Map<String, Object>> batchPriceInfo =
                batchQueryOptimizer.getBatchCrossDatabasePrice(allItems, "buff,steam");

            // 构建分析数据并筛选
            List<Map<String, Object>> filteredList = allItems.stream()
                .map(item -> buildItemPriceAnalysisWithBatchData(item, batchPriceInfo.get(item.getHashname())))
                .filter(Objects::nonNull)
                .filter(item -> filterByPriceStatus(item, priceStatusFilter))
                .collect(Collectors.toList());

            log.debug("涨跌状态筛选后的商品数量: {}", filteredList.size());

            // 手动实现分页
            return applyManualPagination(filteredList);

        } catch (Exception e) {
            log.error("处理涨跌状态筛选查询失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 🔧 根据涨跌状态筛选商品价格分析数据
     *
     * @param item 商品价格分析数据
     * @param priceStatusFilter 涨跌状态筛选条件
     * @return 是否符合筛选条件
     */
    private boolean filterByPriceStatus(Map<String, Object> item, String priceStatusFilter) {
        if (priceStatusFilter == null || priceStatusFilter.trim().isEmpty()) {
            return true; // 无筛选条件，返回所有数据
        }

        try {
            String itemPriceStatus = (String) item.get("priceStatus");

            // 处理无价格状态的情况
            if (itemPriceStatus == null) {
                return "no_data".equals(priceStatusFilter);
            }

            // 匹配筛选条件
            return priceStatusFilter.equals(itemPriceStatus);

        } catch (Exception e) {
            log.warn("涨跌状态筛选失败，商品: {}, 筛选条件: {}", item.get("name"), priceStatusFilter, e);
            return false;
        }
    }

    /**
     * 🔧 手动实现分页逻辑
     * 修复涨跌状态筛选导致的分页失效问题
     */
    private List<Map<String, Object>> applyManualPagination(List<Map<String, Object>> allData) {
        try {
            // 从PageHelper的ThreadLocal获取分页参数
            com.github.pagehelper.Page<?> localPage = com.github.pagehelper.PageHelper.getLocalPage();

            int pageNum = 1;
            int pageSize = 10;

            if (localPage != null) {
                pageNum = localPage.getPageNum();
                pageSize = localPage.getPageSize();
                log.debug("从PageHelper获取分页参数 - 页码: {}, 每页大小: {}", pageNum, pageSize);
            } else {
                log.warn("无法从PageHelper获取分页参数，使用默认值");
            }

            // 计算分页
            int total = allData.size();
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            // 创建分页结果
            com.github.pagehelper.Page<Map<String, Object>> resultPage =
                new com.github.pagehelper.Page<>(pageNum, pageSize);
            resultPage.setTotal(total);

            // 添加当前页的数据
            if (startIndex < total) {
                List<Map<String, Object>> pageData = allData.subList(startIndex, endIndex);
                resultPage.addAll(pageData);
                log.debug("手动分页完成 - 总数: {}, 当前页: {}, 每页大小: {}, 当前页数据量: {}",
                    total, pageNum, pageSize, pageData.size());
            } else {
                log.debug("手动分页完成 - 总数: {}, 当前页: {}, 每页大小: {}, 当前页无数据",
                    total, pageNum, pageSize);
            }

            return resultPage;

        } catch (Exception e) {
            log.error("手动分页失败", e);
            // 降级处理：返回包含所有数据的Page对象
            com.github.pagehelper.Page<Map<String, Object>> resultPage = new com.github.pagehelper.Page<>();
            resultPage.setTotal(allData.size());
            resultPage.addAll(allData);
            return resultPage;
        }
    }

    private void updateItemPricesFromPriceInfo(VimItem item, Map<String, Object> priceInfo) {
        BigDecimal referencePrice = (BigDecimal) priceInfo.get("sell_min_price");
        if (referencePrice != null) {
            priceCalculationUtil.updateItemPrices(item, referencePrice);
        }
    }

    private String determinePriceStatus(BigDecimal differencePercentage) {
        if (differencePercentage == null) {
            return "no_data";
        }

        if (differencePercentage.compareTo(BigDecimal.ZERO) > 0) {
            return "up";
        } else if (differencePercentage.compareTo(BigDecimal.ZERO) < 0) {
            return "down";
        } else {
            return "stable";
        }
    }

    // ========== 成本价异常商品管理实现 ==========

    @Override
    public List<VimCostPriceAnomalyDTO> selectCostPriceAnomalyList(String itemName, String boxName, String itemTag,
            Integer saleStatus, java.math.BigDecimal minCost, java.math.BigDecimal maxCost,
            java.math.BigDecimal minRecycle, java.math.BigDecimal maxRecycle,
            java.math.BigDecimal minDifference, java.math.BigDecimal maxDifference,
            java.math.BigDecimal minPercentage, java.math.BigDecimal maxPercentage,
            Integer stockStatus, Integer severityLevel, String sortField) {
        try {
            log.info("查询成本价异常商品列表，参数：itemName={}, boxName={}, itemTag={}, saleStatus={}, " +
                    "价格范围：[{}-{}], 回收价范围：[{}-{}], 差异范围：[{}-{}], 百分比范围：[{}-{}], " +
                    "stockStatus={}, severityLevel={}, sortField={}",
                    itemName, boxName, itemTag, saleStatus, minCost, maxCost, minRecycle, maxRecycle,
                    minDifference, maxDifference, minPercentage, maxPercentage, stockStatus, severityLevel, sortField);

            long startTime = System.currentTimeMillis();
            List<VimCostPriceAnomalyDTO> result = recordMapper.selectCostPriceAnomalyList(
                itemName, boxName, itemTag, saleStatus, minCost, maxCost, minRecycle, maxRecycle,
                minDifference, maxDifference, minPercentage, maxPercentage, stockStatus, severityLevel, sortField);
            long endTime = System.currentTimeMillis();

            log.info("成本价异常商品查询完成，耗时：{}ms，查询到{}条记录", (endTime - startTime), result.size());
            return result;
        } catch (Exception e) {
            log.error("查询成本价异常商品列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public int countCostPriceAnomalyItems() {
        try {
            int count = recordMapper.countCostPriceAnomalyItems();
            log.info("统计成本价异常商品数量：{}", count);
            return count;
        } catch (Exception e) {
            log.error("统计成本价异常商品数量失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getCostPriceAnomalyStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        try {
            // 获取异常商品总数
            int totalAnomalyItems = countCostPriceAnomalyItems();
            statistics.put("totalAnomalyItems", totalAnomalyItems);

            // 获取异常商品列表用于统计分析
            List<VimCostPriceAnomalyDTO> anomalyList = selectCostPriceAnomalyList(
                null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, null);

            if (!anomalyList.isEmpty()) {
                // 计算平均价格差异
                BigDecimal totalDifference = anomalyList.stream()
                    .map(VimCostPriceAnomalyDTO::getPriceDifference)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal avgDifference = totalDifference.divide(
                    BigDecimal.valueOf(anomalyList.size()), 2, RoundingMode.HALF_UP);
                statistics.put("avgPriceDifference", avgDifference);

                // 计算最大价格差异
                BigDecimal maxDifference = anomalyList.stream()
                    .map(VimCostPriceAnomalyDTO::getPriceDifference)
                    .filter(Objects::nonNull)
                    .max(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);
                statistics.put("maxPriceDifference", maxDifference);

                // 按商品标签分组统计
                Map<String, Long> tagStatistics = anomalyList.stream()
                    .filter(item -> item.getItemTag() != null)
                    .collect(Collectors.groupingBy(
                        VimCostPriceAnomalyDTO::getItemTag,
                        Collectors.counting()
                    ));
                statistics.put("tagStatistics", tagStatistics);

                // 按盲盒分组统计（处理多盲盒情况）
                Map<String, Long> boxStatistics = new HashMap<>();
                for (VimCostPriceAnomalyDTO item : anomalyList) {
                    if (item.getBoxNames() != null && !item.getBoxNames().isEmpty()) {
                        String[] boxNames = item.getBoxNames().split(", ");
                        for (String boxName : boxNames) {
                            boxName = boxName.trim();
                            if (!boxName.isEmpty()) {
                                boxStatistics.put(boxName, boxStatistics.getOrDefault(boxName, 0L) + 1);
                            }
                        }
                    }
                }
                statistics.put("boxStatistics", boxStatistics);
            } else {
                statistics.put("avgPriceDifference", BigDecimal.ZERO);
                statistics.put("maxPriceDifference", BigDecimal.ZERO);
                statistics.put("tagStatistics", new HashMap<>());
                statistics.put("boxStatistics", new HashMap<>());
            }

            log.info("成本价异常商品统计信息生成完成：{}", statistics);
            return statistics;
        } catch (Exception e) {
            log.error("获取成本价异常商品统计信息失败", e);
            statistics.put("totalAnomalyItems", 0);
            statistics.put("avgPriceDifference", BigDecimal.ZERO);
            statistics.put("maxPriceDifference", BigDecimal.ZERO);
            statistics.put("tagStatistics", new HashMap<>());
            statistics.put("boxStatistics", new HashMap<>());
            return statistics;
        }
    }
}
