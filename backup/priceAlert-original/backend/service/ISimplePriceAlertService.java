package com.ruoyi.project.priceAlert.service;

import com.ruoyi.project.commoditySys.domain.VimItem;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertConfig;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertRecordSimple;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertExecutionLog;
import com.ruoyi.project.priceAlert.domain.VimCostPriceAnomalyDTO;

import java.util.List;
import java.util.Map;

/**
 * 简化价格预警服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ISimplePriceAlertService {

    // ========== 预警配置管理 ==========

    /**
     * 获取所有预警配置（固化的两条规则）
     * 
     * @return 预警配置列表
     */
    List<VimPriceAlertConfig> getAllConfigs();

    /**
     * 根据规则类型获取配置
     * 
     * @param ruleType 规则类型：price_up, price_down
     * @return 预警配置
     */
    VimPriceAlertConfig getConfigByRuleType(String ruleType);

    /**
     * 更新预警配置
     * 
     * @param config 预警配置
     * @return 更新结果
     */
    int updateConfig(VimPriceAlertConfig config);

    /**
     * 批量更新预警配置
     * 
     * @param configs 预警配置列表
     * @return 更新结果
     */
    int batchUpdateConfigs(List<VimPriceAlertConfig> configs);

    /**
     * 启用/禁用预警规则
     * 
     * @param ruleType 规则类型
     * @param enabled 是否启用
     * @return 更新结果
     */
    int toggleRuleStatus(String ruleType, boolean enabled);

    // ========== 预警执行 ==========

    /**
     * 手动执行预警检查（所有规则）
     * 
     * @param triggerBy 触发者
     * @return 执行结果
     */
    Map<String, Object> executeManualCheck(String triggerBy);

    /**
     * 执行指定规则的预警检查
     * 
     * @param ruleType 规则类型
     * @param triggerBy 触发者
     * @return 执行结果
     */
    Map<String, Object> executeRuleCheck(String ruleType, String triggerBy);

    /**
     * 配置变更后自动触发预警检查
     * 
     * @param ruleType 变更的规则类型
     * @param triggerBy 触发者
     * @return 执行结果
     */
    Map<String, Object> executeConfigChangedCheck(String ruleType, String triggerBy);

    /**
     * 定时执行预警检查
     * 
     * @return 执行结果
     */
    Map<String, Object> executeScheduledCheck();

    // ========== 预警记录管理 ==========

    /**
     * 查询预警记录列表
     * 
     * @param record 查询条件
     * @return 预警记录列表
     */
    List<VimPriceAlertRecordSimple> selectRecordList(VimPriceAlertRecordSimple record);

    /**
     * 根据ID查询预警记录
     * 
     * @param id 记录ID
     * @return 预警记录
     */
    VimPriceAlertRecordSimple selectRecordById(Long id);

    /**
     * 处理预警记录
     * 
     * @param id 记录ID
     * @param handleBy 处理人
     * @param handleRemark 处理备注
     * @param status 处理状态：2=已处理，3=已忽略
     * @return 处理结果
     */
    int handleRecord(Long id, String handleBy, String handleRemark, Integer status);

    /**
     * 批量处理预警记录
     * 
     * @param ids 记录ID列表
     * @param handleBy 处理人
     * @param handleRemark 处理备注
     * @param status 处理状态
     * @return 处理结果
     */
    int batchHandleRecords(Long[] ids, String handleBy, String handleRemark, Integer status);

    /**
     * 删除预警记录
     * 
     * @param ids 记录ID数组
     * @return 删除结果
     */
    int deleteRecordByIds(Long[] ids);

    // ========== 执行日志管理 ==========

    /**
     * 查询执行日志列表
     * 
     * @param log 查询条件
     * @return 执行日志列表
     */
    List<VimPriceAlertExecutionLog> selectExecutionLogList(VimPriceAlertExecutionLog log);

    /**
     * 根据ID查询执行日志
     * 
     * @param id 日志ID
     * @return 执行日志
     */
    VimPriceAlertExecutionLog selectExecutionLogById(Long id);

    /**
     * 清理过期执行日志
     * 
     * @param retentionDays 保留天数
     * @return 清理数量
     */
    int cleanExpiredExecutionLogs(int retentionDays);

    // ========== 统计分析 ==========

    /**
     * 获取预警统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getAlertStatistics();

    /**
     * 获取预警趋势数据
     * 
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getAlertTrendData(int days);

    /**
     * 获取商品预警排行
     * 
     * @param limit 返回数量限制
     * @return 排行数据
     */
    List<Map<String, Object>> getItemAlertRanking(int limit);

    /**
     * 获取数据源统计
     * 
     * @return 数据源统计
     */
    Map<String, Object> getDataSourceStatistics();

    // ========== 系统管理 ==========

    /**
     * 获取系统健康状态
     * 
     * @return 健康状态信息
     */
    Map<String, Object> getSystemHealthStatus();

    /**
     * 获取系统配置
     * 
     * @return 系统配置
     */
    Map<String, Object> getSystemConfig();

    /**
     * 更新系统配置
     * 
     * @param config 配置信息
     * @return 更新结果
     */
    int updateSystemConfig(Map<String, Object> config);

    /**
     * 重置系统配置为默认值
     * 
     * @return 重置结果
     */
    int resetSystemConfig();

    // ========== 商品价格分析 ==========

    /**
     * 查询商品价格分析列表
     *
     * @param vimItem 查询条件
     * @return 商品价格分析列表
     */
    List<Map<String, Object>> selectItemPriceAnalysisList(VimItem vimItem);

    /**
     * 根据商品ID获取价格分析详情
     *
     * @param itemId 商品ID
     * @return 价格分析详情
     */
    Map<String, Object> getItemPriceAnalysisById(Long itemId);

    /**
     * 批量更新商品价格分析
     *
     * @param itemIds 商品ID列表
     * @return 更新结果
     */
    Map<String, Object> batchUpdateItemPriceAnalysis(Long[] itemIds);

    /**
     * 更新单个商品价格
     *
     * @param itemId 商品ID
     * @return 更新结果
     */
    Map<String, Object> updateSingleItemPrice(Long itemId);

    /**
     * 测试价格查询功能
     *
     * @param hashname 商品哈希名称
     * @return 测试结果
     */
    Map<String, Object> testPriceQuery(String hashname);

    // ========== 成本价异常商品管理 ==========

    /**
     * 查询成本价异常商品列表
     *
     * @param itemName 商品名称（可选）
     * @param boxName 盲盒名称（可选）
     * @param itemTag 商品标签（可选）
     * @param saleStatus 商品状态（可选）
     * @param minCost 最小成本价（可选）
     * @param maxCost 最大成本价（可选）
     * @param minRecycle 最小回收价（可选）
     * @param maxRecycle 最大回收价（可选）
     * @param minDifference 最小价格差异（可选）
     * @param maxDifference 最大价格差异（可选）
     * @param minPercentage 最小差异百分比（可选）
     * @param maxPercentage 最大差异百分比（可选）
     * @param stockStatus 库存状态（可选）
     * @param severityLevel 异常严重程度（可选）
     * @param sortField 排序字段（可选）
     * @return 成本价异常商品列表
     */
    List<VimCostPriceAnomalyDTO> selectCostPriceAnomalyList(String itemName, String boxName, String itemTag,
            Integer saleStatus, java.math.BigDecimal minCost, java.math.BigDecimal maxCost,
            java.math.BigDecimal minRecycle, java.math.BigDecimal maxRecycle,
            java.math.BigDecimal minDifference, java.math.BigDecimal maxDifference,
            java.math.BigDecimal minPercentage, java.math.BigDecimal maxPercentage,
            Integer stockStatus, Integer severityLevel, String sortField);

    /**
     * 统计成本价异常商品数量
     *
     * @return 异常商品数量
     */
    int countCostPriceAnomalyItems();

    /**
     * 获取成本价异常商品统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getCostPriceAnomalyStatistics();
}
