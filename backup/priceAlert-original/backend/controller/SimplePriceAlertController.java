package com.ruoyi.project.priceAlert.controller;

import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.commoditySys.domain.VimItem;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertConfig;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertExecutionLog;
import com.ruoyi.project.priceAlert.domain.VimPriceAlertRecordSimple;
import com.ruoyi.project.priceAlert.domain.VimCostPriceAnomalyDTO;
import com.ruoyi.project.priceAlert.service.ISimplePriceAlertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.common.utils.poi.ExcelUtil;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 简化价格预警控制器
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Api(tags = "简化价格预警管理")
@Slf4j
@RestController
@RequestMapping("/priceAlert/simple")
public class SimplePriceAlertController extends BaseController {

    @Autowired
    private ISimplePriceAlertService simplePriceAlertService;

    // ========== 预警配置管理 ==========

    @ApiOperation("获取所有预警配置")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:config:list')")
    @GetMapping("/configs")
    public AjaxResult getAllConfigs() {
        List<VimPriceAlertConfig> configs = simplePriceAlertService.getAllConfigs();
        return success(configs);
    }

    @ApiOperation("根据规则类型获取配置")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:config:query')")
    @GetMapping("/config/{ruleType}")
    public AjaxResult getConfigByRuleType(
            @ApiParam(value = "规则类型", required = true) @PathVariable String ruleType) {
        VimPriceAlertConfig config = simplePriceAlertService.getConfigByRuleType(ruleType);
        return success(config);
    }

    @ApiOperation("更新预警配置")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:config:edit')")
    @Log(title = "简化价格预警配置", businessType = BusinessType.UPDATE)
    @PutMapping("/config")
    public AjaxResult updateConfig(@RequestBody VimPriceAlertConfig config) {
        config.setUpdateBy(getUsername());
        int result = simplePriceAlertService.updateConfig(config);
        return toAjax(result);
    }

    @ApiOperation("批量更新预警配置")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:config:edit')")
    @Log(title = "批量更新预警配置", businessType = BusinessType.UPDATE)
    @PutMapping("/configs/batch")
    public AjaxResult batchUpdateConfigs(@RequestBody List<VimPriceAlertConfig> configs) {
        String username = getUsername();
        configs.forEach(config -> config.setUpdateBy(username));
        int result = simplePriceAlertService.batchUpdateConfigs(configs);
        return toAjax(result);
    }

    @ApiOperation("启用/禁用预警规则")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:config:edit')")
    @Log(title = "切换预警规则状态", businessType = BusinessType.UPDATE)
    @PutMapping("/config/toggle/{ruleType}/{enabled}")
    public AjaxResult toggleRuleStatus(
            @ApiParam(value = "规则类型", required = true) @PathVariable String ruleType,
            @ApiParam(value = "是否启用", required = true) @PathVariable boolean enabled) {
        int result = simplePriceAlertService.toggleRuleStatus(ruleType, enabled);
        return toAjax(result);
    }

    @ApiOperation("重置配置为默认值")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:config:edit')")
    @Log(title = "重置预警配置", businessType = BusinessType.UPDATE)
    @PostMapping("/config/reset/{ruleType}")
    public AjaxResult resetConfig(
            @ApiParam(value = "规则类型", required = true) @PathVariable String ruleType) {
        // 这里可以调用重置逻辑
        return success("配置已重置为默认值");
    }

    // ========== 预警执行 ==========

    @ApiOperation("手动执行预警检查")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:execute')")
    @Log(title = "手动执行预警检查", businessType = BusinessType.OTHER)
    @PostMapping("/execute/manual")
    public AjaxResult executeManualCheck() {
        Map<String, Object> result = simplePriceAlertService.executeManualCheck(getUsername());
        return success(result);
    }

    @ApiOperation("执行指定规则的预警检查")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:execute')")
    @Log(title = "执行指定规则检查", businessType = BusinessType.OTHER)
    @PostMapping("/execute/rule/{ruleType}")
    public AjaxResult executeRuleCheck(
            @ApiParam(value = "规则类型", required = true) @PathVariable String ruleType) {
        Map<String, Object> result = simplePriceAlertService.executeRuleCheck(ruleType, getUsername());
        return success(result);
    }

    @ApiOperation("测试预警规则")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:test')")
    @Log(title = "测试预警规则", businessType = BusinessType.OTHER)
    @PostMapping("/test/{ruleType}")
    public AjaxResult testRule(
            @ApiParam(value = "规则类型", required = true) @PathVariable String ruleType) {
        Map<String, Object> result = simplePriceAlertService.executeRuleCheck(ruleType, getUsername());
        return success(result);
    }

    // ========== 预警记录管理 ==========

    @ApiOperation("查询预警记录列表")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:record:list')")
    @GetMapping("/records")
    public TableDataInfo getRecordList(VimPriceAlertRecordSimple record) {
        startPage();
        List<VimPriceAlertRecordSimple> list = simplePriceAlertService.selectRecordList(record);
        return getDataTable(list);
    }

    @ApiOperation("根据ID查询预警记录")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:record:query')")
    @GetMapping("/record/{id}")
    public AjaxResult getRecordById(
            @ApiParam(value = "记录ID", required = true) @PathVariable Long id) {
        VimPriceAlertRecordSimple record = simplePriceAlertService.selectRecordById(id);
        return success(record);
    }

    @ApiOperation("处理预警记录")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:record:handle')")
    @Log(title = "处理预警记录", businessType = BusinessType.UPDATE)
    @PutMapping("/record/handle/{id}")
    public AjaxResult handleRecord(
            @ApiParam(value = "记录ID", required = true) @PathVariable Long id,
            @RequestBody Map<String, Object> data) {
        String handleRemark = (String) data.get("handleRemark");
        Integer status = (Integer) data.get("status");
        int result = simplePriceAlertService.handleRecord(id, getUsername(), handleRemark, status);
        return toAjax(result);
    }

    @ApiOperation("批量处理预警记录")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:record:handle')")
    @Log(title = "批量处理预警记录", businessType = BusinessType.UPDATE)
    @PutMapping("/records/batch-handle")
    public AjaxResult batchHandleRecords(@RequestBody Map<String, Object> data) {
        try {
            // 安全的类型转换
            Object idsObj = data.get("ids");
            Long[] ids = convertToLongArray(idsObj);

            if (ids.length == 0) {
                return AjaxResult.error("请选择要处理的记录");
            }

            String handleRemark = (String) data.get("handleRemark");
            Integer status = (Integer) data.get("status");

            if (status == null) {
                return AjaxResult.error("处理状态不能为空");
            }

            int result = simplePriceAlertService.batchHandleRecords(ids, getUsername(), handleRemark, status);
            return toAjax(result);
        } catch (Exception e) {
            log.error("批量处理预警记录失败", e);
            return AjaxResult.error("批量处理失败: " + e.getMessage());
        }
    }

    @ApiOperation("删除预警记录")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:record:remove')")
    @Log(title = "删除预警记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/records")
    public AjaxResult deleteRecords(@RequestBody Object idsObj) {
        try {
            Long[] ids = convertToLongArray(idsObj);

            if (ids.length == 0) {
                return AjaxResult.error("请选择要删除的记录");
            }

            int result = simplePriceAlertService.deleteRecordByIds(ids);
            return toAjax(result);
        } catch (Exception e) {
            log.error("删除预警记录失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }

    // ========== 执行日志管理 ==========

    @ApiOperation("查询执行日志列表")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:log:list')")
    @GetMapping("/execution-logs")
    public TableDataInfo getExecutionLogList(VimPriceAlertExecutionLog log) {
        startPage();
        List<VimPriceAlertExecutionLog> list = simplePriceAlertService.selectExecutionLogList(log);
        return getDataTable(list);
    }

    @ApiOperation("根据ID查询执行日志")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:log:query')")
    @GetMapping("/execution-log/{id}")
    public AjaxResult getExecutionLogById(
            @ApiParam(value = "日志ID", required = true) @PathVariable Long id) {
        VimPriceAlertExecutionLog log = simplePriceAlertService.selectExecutionLogById(id);
        return success(log);
    }

    @ApiOperation("清理过期执行日志")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:log:clean')")
    @Log(title = "清理过期执行日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/execution-logs/clean/{retentionDays}")
    public AjaxResult cleanExpiredExecutionLogs(
            @ApiParam(value = "保留天数", required = true) @PathVariable int retentionDays) {
        int result = simplePriceAlertService.cleanExpiredExecutionLogs(retentionDays);
        return success("成功清理 " + result + " 条过期日志");
    }

    // ========== 统计分析 ==========

    @ApiOperation("获取预警统计信息")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:statistics:view')")
    @GetMapping("/statistics/alert")
    public AjaxResult getAlertStatistics() {
        Map<String, Object> statistics = simplePriceAlertService.getAlertStatistics();
        return success(statistics);
    }

    @ApiOperation("获取预警趋势数据")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:statistics:view')")
    @GetMapping("/statistics/trend/{days}")
    public AjaxResult getAlertTrendData(
            @ApiParam(value = "统计天数", required = true) @PathVariable int days) {
        List<Map<String, Object>> trendData = simplePriceAlertService.getAlertTrendData(days);
        return success(trendData);
    }

    @ApiOperation("获取商品预警排行")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:statistics:view')")
    @GetMapping("/statistics/item-ranking/{limit}")
    public AjaxResult getItemAlertRanking(
            @ApiParam(value = "返回数量限制", required = true) @PathVariable int limit) {
        List<Map<String, Object>> ranking = simplePriceAlertService.getItemAlertRanking(limit);
        return success(ranking);
    }

    @ApiOperation("获取数据源统计")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:statistics:view')")
    @GetMapping("/statistics/data-source")
    public AjaxResult getDataSourceStatistics() {
        Map<String, Object> statistics = simplePriceAlertService.getDataSourceStatistics();
        return success(statistics);
    }

    // ========== 系统管理 ==========

    @ApiOperation("获取系统健康状态")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:system:view')")
    @GetMapping("/system/health")
    public AjaxResult getSystemHealthStatus() {
        Map<String, Object> health = simplePriceAlertService.getSystemHealthStatus();
        return success(health);
    }

    @ApiOperation("获取系统配置")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:system:view')")
    @GetMapping("/system/config")
    public AjaxResult getSystemConfig() {
        Map<String, Object> config = simplePriceAlertService.getSystemConfig();
        return success(config);
    }

    @ApiOperation("更新系统配置")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:system:edit')")
    @Log(title = "更新系统配置", businessType = BusinessType.UPDATE)
    @PutMapping("/system/config")
    public AjaxResult updateSystemConfig(@RequestBody Map<String, Object> config) {
        int result = simplePriceAlertService.updateSystemConfig(config);
        return toAjax(result);
    }

    @ApiOperation("重置系统配置")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:system:edit')")
    @Log(title = "重置系统配置", businessType = BusinessType.UPDATE)
    @PostMapping("/system/config/reset")
    public AjaxResult resetSystemConfig() {
        int result = simplePriceAlertService.resetSystemConfig();
        return toAjax(result);
    }

    // ========== 商品价格分析 ==========

    @ApiOperation("查询商品价格分析列表")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:item:list')")
    @GetMapping("/items")
    public TableDataInfo getItemPriceAnalysisList(VimItem vimItem) {
        startPage();
        List<Map<String, Object>> list = simplePriceAlertService.selectItemPriceAnalysisList(vimItem);
        return getDataTable(list);
    }

    @ApiOperation("获取商品价格分析详细信息")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:item:query')")
    @GetMapping("/items/{id}")
    public AjaxResult getItemPriceAnalysisInfo(@PathVariable("id") Long id) {
        Map<String, Object> analysis = simplePriceAlertService.getItemPriceAnalysisById(id);
        return analysis != null ? AjaxResult.success(analysis) : AjaxResult.error("商品不存在");
    }

    @ApiOperation("批量更新商品价格分析")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:item:edit')")
    @Log(title = "批量更新商品价格分析", businessType = BusinessType.UPDATE)
    @PutMapping("/items/batch-update")
    public AjaxResult batchUpdateItemPriceAnalysis(@RequestBody Object idsObj) {
        try {
            Long[] ids = convertToLongArray(idsObj);

            if (ids.length == 0) {
                return AjaxResult.error("请选择要更新的商品");
            }

            Map<String, Object> result = simplePriceAlertService.batchUpdateItemPriceAnalysis(ids);
            return AjaxResult.success("批量更新完成", result);
        } catch (Exception e) {
            log.error("批量更新商品价格分析失败", e);
            return AjaxResult.error("批量更新失败: " + e.getMessage());
        }
    }

    @ApiOperation("更新单个商品价格")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:item:edit')")
    @Log(title = "更新单个商品价格", businessType = BusinessType.UPDATE)
    @PutMapping("/items/{id}/update-price")
    public AjaxResult updateSingleItemPrice(@PathVariable("id") Long id) {
        Map<String, Object> result = simplePriceAlertService.updateSingleItemPrice(id);

        if ((Boolean) result.getOrDefault("success", false)) {
            return AjaxResult.success(result.get("message").toString(), result);
        } else {
            return AjaxResult.error(result.get("message").toString());
        }
    }

    @ApiOperation("测试价格查询功能")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:test')")
    @GetMapping("/test/price/{hashname}")
    public AjaxResult testPriceQuery(@PathVariable String hashname) {
        try {
            Map<String, Object> result = simplePriceAlertService.testPriceQuery(hashname);
            return AjaxResult.success("价格查询测试完成", result);
        } catch (Exception e) {
            return AjaxResult.error("价格查询测试失败: " + e.getMessage());
        }
    }

    /**
     * 安全地将Object转换为Long数组
     *
     * @param obj 要转换的对象
     * @return Long数组
     */
    private Long[] convertToLongArray(Object obj) {
        if (obj == null) {
            return new Long[0];
        }

        try {
            if (obj instanceof Long[]) {
                return (Long[]) obj;
            } else if (obj instanceof Collection<?> collection) {
                Long[] result = new Long[collection.size()];
                int index = 0;
                for (Object item : collection) {
                    if (item instanceof Number) {
                        result[index++] = ((Number) item).longValue();
                    } else if (item instanceof String) {
                        result[index++] = Long.parseLong((String) item);
                    } else {
                        throw new IllegalArgumentException("无法转换为Long类型: " + item);
                    }
                }
                return result;
            } else if (obj instanceof Object[] array) {
                Long[] result = new Long[array.length];
                for (int i = 0; i < array.length; i++) {
                    if (array[i] instanceof Number) {
                        result[i] = ((Number) array[i]).longValue();
                    } else if (array[i] instanceof String) {
                        result[i] = Long.parseLong((String) array[i]);
                    } else {
                        throw new IllegalArgumentException("无法转换为Long类型: " + array[i]);
                    }
                }
                return result;
            } else {
                throw new IllegalArgumentException("不支持的数据类型: " + obj.getClass());
            }
        } catch (Exception e) {
            log.error("类型转换失败: {}", e.getMessage());
            throw new IllegalArgumentException("参数类型转换失败: " + e.getMessage());
        }
    }

    // ========== 成本价异常商品管理 ==========

    @ApiOperation("查询成本价异常商品列表")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:anomaly:list')")
    @GetMapping("/cost-price-anomaly")
    public TableDataInfo getCostPriceAnomalyList(
            @ApiParam(value = "商品名称", required = false) @RequestParam(required = false) String itemName,
            @ApiParam(value = "盲盒名称", required = false) @RequestParam(required = false) String boxName,
            @ApiParam(value = "商品标签", required = false) @RequestParam(required = false) String itemTag,
            @ApiParam(value = "商品状态", required = false) @RequestParam(required = false) Integer saleStatus,
            @ApiParam(value = "最小成本价", required = false) @RequestParam(required = false) BigDecimal minCost,
            @ApiParam(value = "最大成本价", required = false) @RequestParam(required = false) BigDecimal maxCost,
            @ApiParam(value = "最小回收价", required = false) @RequestParam(required = false) BigDecimal minRecycle,
            @ApiParam(value = "最大回收价", required = false) @RequestParam(required = false) BigDecimal maxRecycle,
            @ApiParam(value = "最小价格差异", required = false) @RequestParam(required = false) BigDecimal minDifference,
            @ApiParam(value = "最大价格差异", required = false) @RequestParam(required = false) BigDecimal maxDifference,
            @ApiParam(value = "最小差异百分比", required = false) @RequestParam(required = false) BigDecimal minPercentage,
            @ApiParam(value = "最大差异百分比", required = false) @RequestParam(required = false) BigDecimal maxPercentage,
            @ApiParam(value = "库存状态", required = false) @RequestParam(required = false) Integer stockStatus,
            @ApiParam(value = "异常严重程度", required = false) @RequestParam(required = false) Integer severityLevel,
            @ApiParam(value = "排序字段", required = false) @RequestParam(required = false) String sortField) {

        // 参数验证
        if (minCost != null && maxCost != null && minCost.compareTo(maxCost) > 0) {
            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(500);
            dataTable.setMsg("成本价范围参数错误：最小值不能大于最大值");
            dataTable.setRows(new ArrayList<>());
            dataTable.setTotal(0);
            return dataTable;
        }
        if (minRecycle != null && maxRecycle != null && minRecycle.compareTo(maxRecycle) > 0) {
            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(500);
            dataTable.setMsg("回收价范围参数错误：最小值不能大于最大值");
            dataTable.setRows(new ArrayList<>());
            dataTable.setTotal(0);
            return dataTable;
        }
        if (minDifference != null && maxDifference != null && minDifference.compareTo(maxDifference) > 0) {
            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(500);
            dataTable.setMsg("价格差异范围参数错误：最小值不能大于最大值");
            dataTable.setRows(new ArrayList<>());
            dataTable.setTotal(0);
            return dataTable;
        }
        if (minPercentage != null && maxPercentage != null && minPercentage.compareTo(maxPercentage) > 0) {
            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(500);
            dataTable.setMsg("差异百分比范围参数错误：最小值不能大于最大值");
            dataTable.setRows(new ArrayList<>());
            dataTable.setTotal(0);
            return dataTable;
        }

        startPage();
        List<VimCostPriceAnomalyDTO> list = simplePriceAlertService.selectCostPriceAnomalyList(
            itemName, boxName, itemTag, saleStatus, minCost, maxCost, minRecycle, maxRecycle,
            minDifference, maxDifference, minPercentage, maxPercentage, stockStatus, severityLevel, sortField);
        return getDataTable(list);
    }

    @ApiOperation("统计成本价异常商品数量")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:anomaly:list')")
    @GetMapping("/cost-price-anomaly/count")
    public AjaxResult getCostPriceAnomalyCount() {
        int count = simplePriceAlertService.countCostPriceAnomalyItems();
        return success(count);
    }

    @ApiOperation("导出成本价异常商品列表")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:anomaly:export')")
    @Log(title = "成本价异常商品", businessType = BusinessType.EXPORT)
    @PostMapping("/cost-price-anomaly/export")
    public void exportCostPriceAnomalyList(HttpServletResponse response,
            @RequestParam(required = false) String itemName,
            @RequestParam(required = false) String boxName,
            @RequestParam(required = false) String itemTag,
            @RequestParam(required = false) Integer saleStatus,
            @RequestParam(required = false) BigDecimal minCost,
            @RequestParam(required = false) BigDecimal maxCost,
            @RequestParam(required = false) BigDecimal minRecycle,
            @RequestParam(required = false) BigDecimal maxRecycle,
            @RequestParam(required = false) BigDecimal minDifference,
            @RequestParam(required = false) BigDecimal maxDifference,
            @RequestParam(required = false) BigDecimal minPercentage,
            @RequestParam(required = false) BigDecimal maxPercentage,
            @RequestParam(required = false) Integer stockStatus,
            @RequestParam(required = false) Integer severityLevel,
            @RequestParam(required = false) String sortField) {
        List<VimCostPriceAnomalyDTO> list = simplePriceAlertService.selectCostPriceAnomalyList(
            itemName, boxName, itemTag, saleStatus, minCost, maxCost, minRecycle, maxRecycle,
            minDifference, maxDifference, minPercentage, maxPercentage, stockStatus, severityLevel, sortField);
        ExcelUtil<VimCostPriceAnomalyDTO> util = new ExcelUtil<VimCostPriceAnomalyDTO>(VimCostPriceAnomalyDTO.class);
        util.exportExcel(response, list, "成本价异常商品数据");
    }

    @ApiOperation("获取成本价异常商品统计信息")
    @PreAuthorize("@ss.hasPermi('priceAlert:simple:anomaly:list')")
    @GetMapping("/cost-price-anomaly/statistics")
    public AjaxResult getCostPriceAnomalyStatistics() {
        Map<String, Object> statistics = simplePriceAlertService.getCostPriceAnomalyStatistics();
        return success(statistics);
    }
}
