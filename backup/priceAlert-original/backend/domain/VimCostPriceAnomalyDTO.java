package com.ruoyi.project.priceAlert.domain;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;

/**
 * 成本价异常商品数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@ApiModel(value = "VimCostPriceAnomalyDTO", description = "成本价异常商品数据传输对象")
public class VimCostPriceAnomalyDTO {
    
    /** 商品ID */
    @ApiModelProperty(value = "商品ID", example = "1")
    private Long itemId;
    
    /** 商品名称 */
    @ApiModelProperty(value = "商品名称", example = "AK-47 | 红线")
    @Excel(name = "商品名称")
    private String itemName;
    
    /** 商品英文名 */
    @ApiModelProperty(value = "商品英文名", example = "AK-47 | Redline")
    private String itemHashname;
    
    /** 商品标签 */
    @ApiModelProperty(value = "商品标签", example = "步枪")
    @Excel(name = "商品标签")
    private String itemTag;
    
    /** 商品图片 */
    @ApiModelProperty(value = "商品图片", example = "http://example.com/images/item.jpg")
    private String itemImage;
    
    /** 成本价 */
    @ApiModelProperty(value = "成本价", example = "150.00")
    @Excel(name = "成本价")
    private BigDecimal priceCost;
    
    /** 回收价 */
    @ApiModelProperty(value = "回收价", example = "120.00")
    @Excel(name = "回收价")
    private BigDecimal priceRecycle;
    
    /** 展示价 */
    @ApiModelProperty(value = "展示价", example = "180.00")
    @Excel(name = "展示价")
    private BigDecimal priceShow;
    
    /** 购买价 */
    @ApiModelProperty(value = "购买价", example = "200.00")
    @Excel(name = "购买价")
    private BigDecimal priceBuy;
    
    /** 价格差异（成本价-回收价） */
    @ApiModelProperty(value = "价格差异", example = "30.00")
    @Excel(name = "价格差异")
    private BigDecimal priceDifference;
    
    /** 差异百分比 */
    @ApiModelProperty(value = "差异百分比", example = "25.00")
    @Excel(name = "差异百分比(%)")
    private BigDecimal differencePercentage;
    
    /** 盲盒ID列表（逗号分隔） */
    @ApiModelProperty(value = "盲盒ID列表", example = "1,2,3")
    private String boxIds;

    /** 盲盒名称列表（逗号分隔） */
    @ApiModelProperty(value = "盲盒名称列表", example = "神秘盲盒,高级盲盒,特殊盲盒")
    @Excel(name = "所属盲盒")
    private String boxNames;

    /** 盲盒类型列表（逗号分隔） */
    @ApiModelProperty(value = "盲盒类型列表", example = "普通盲盒,高级盲盒,特殊盲盒")
    @Excel(name = "盲盒类型")
    private String boxTypes;

    /** 盲盒价格列表（逗号分隔） */
    @ApiModelProperty(value = "盲盒价格列表", example = "50.00,100.00,200.00")
    @Excel(name = "盲盒价格")
    private String boxPrices;

    /** 盲盒数量 */
    @ApiModelProperty(value = "所属盲盒数量", example = "3")
    @Excel(name = "盲盒数量")
    private Integer boxCount;
    
    /** 商品是否上架 */
    @ApiModelProperty(value = "商品是否上架", example = "1", notes = "0=下架，1=上架")
    @Excel(name = "是否上架", readConverterExp = "0=下架,1=上架")
    private Long itemSale;
    
    /** 商品库存 */
    @ApiModelProperty(value = "商品库存", example = "100")
    @Excel(name = "库存")
    private Long itemStock;

    // Getters and Setters
    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemHashname() {
        return itemHashname;
    }

    public void setItemHashname(String itemHashname) {
        this.itemHashname = itemHashname;
    }

    public String getItemTag() {
        return itemTag;
    }

    public void setItemTag(String itemTag) {
        this.itemTag = itemTag;
    }

    public String getItemImage() {
        return itemImage;
    }

    public void setItemImage(String itemImage) {
        this.itemImage = itemImage;
    }

    public BigDecimal getPriceCost() {
        return priceCost;
    }

    public void setPriceCost(BigDecimal priceCost) {
        this.priceCost = priceCost;
    }

    public BigDecimal getPriceRecycle() {
        return priceRecycle;
    }

    public void setPriceRecycle(BigDecimal priceRecycle) {
        this.priceRecycle = priceRecycle;
    }

    public BigDecimal getPriceShow() {
        return priceShow;
    }

    public void setPriceShow(BigDecimal priceShow) {
        this.priceShow = priceShow;
    }

    public BigDecimal getPriceBuy() {
        return priceBuy;
    }

    public void setPriceBuy(BigDecimal priceBuy) {
        this.priceBuy = priceBuy;
    }

    public BigDecimal getPriceDifference() {
        return priceDifference;
    }

    public void setPriceDifference(BigDecimal priceDifference) {
        this.priceDifference = priceDifference;
    }

    public BigDecimal getDifferencePercentage() {
        return differencePercentage;
    }

    public void setDifferencePercentage(BigDecimal differencePercentage) {
        this.differencePercentage = differencePercentage;
    }

    public String getBoxIds() {
        return boxIds;
    }

    public void setBoxIds(String boxIds) {
        this.boxIds = boxIds;
    }

    public String getBoxNames() {
        return boxNames;
    }

    public void setBoxNames(String boxNames) {
        this.boxNames = boxNames;
    }

    public String getBoxTypes() {
        return boxTypes;
    }

    public void setBoxTypes(String boxTypes) {
        this.boxTypes = boxTypes;
    }

    public String getBoxPrices() {
        return boxPrices;
    }

    public void setBoxPrices(String boxPrices) {
        this.boxPrices = boxPrices;
    }

    public Integer getBoxCount() {
        return boxCount;
    }

    public void setBoxCount(Integer boxCount) {
        this.boxCount = boxCount;
    }

    public Long getItemSale() {
        return itemSale;
    }

    public void setItemSale(Long itemSale) {
        this.itemSale = itemSale;
    }

    public Long getItemStock() {
        return itemStock;
    }

    public void setItemStock(Long itemStock) {
        this.itemStock = itemStock;
    }

    @Override
    public String toString() {
        return "VimCostPriceAnomalyDTO{" +
                "itemId=" + itemId +
                ", itemName='" + itemName + '\'' +
                ", itemHashname='" + itemHashname + '\'' +
                ", itemTag='" + itemTag + '\'' +
                ", priceCost=" + priceCost +
                ", priceRecycle=" + priceRecycle +
                ", priceDifference=" + priceDifference +
                ", differencePercentage=" + differencePercentage +
                ", boxIds='" + boxIds + '\'' +
                ", boxNames='" + boxNames + '\'' +
                ", boxTypes='" + boxTypes + '\'' +
                ", boxCount=" + boxCount +
                '}';
    }
}
