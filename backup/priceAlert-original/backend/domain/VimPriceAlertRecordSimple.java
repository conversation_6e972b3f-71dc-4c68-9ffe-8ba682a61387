package com.ruoyi.project.priceAlert.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 简化价格预警记录对象 vim_price_alert_record_simple
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "VimPriceAlertRecordSimple", description = "简化价格预警记录对象")
public class VimPriceAlertRecordSimple extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 预警记录ID */
    @ApiModelProperty(value = "预警记录ID", example = "1")
    private Long id;

    /** 触发的规则类型 */
    @ApiModelProperty(value = "规则类型", example = "price_up", allowableValues = "price_up,price_down")
    @Excel(name = "规则类型")
    private String ruleType;

    /** 商品ID */
    @ApiModelProperty(value = "商品ID", example = "101")
    @Excel(name = "商品ID")
    private Long itemId;

    /** 商品名称 */
    @ApiModelProperty(value = "商品名称", example = "AK47-红线", required = true)
    @Excel(name = "商品名称")
    private String itemName;

    /** 商品哈希名称 */
    @ApiModelProperty(value = "商品哈希名称", example = "AK-47 | Redline (Field-Tested)")
    private String itemHashname;

    /** 价格数据源 */
    @ApiModelProperty(value = "价格数据源", example = "buff", required = true)
    @Excel(name = "数据源")
    private String dataSource;

    /** 原价格 */
    @ApiModelProperty(value = "原价格", example = "100.00")
    @Excel(name = "原价格")
    private BigDecimal oldPrice;

    /** 新价格 */
    @ApiModelProperty(value = "新价格", example = "120.00", required = true)
    @Excel(name = "新价格")
    private BigDecimal newPrice;

    /** 变动金额 */
    @ApiModelProperty(value = "变动金额", example = "20.00")
    @Excel(name = "变动金额")
    private BigDecimal changeAmount;

    /** 变动百分比 */
    @ApiModelProperty(value = "变动百分比", example = "20.0000")
    @Excel(name = "变动百分比")
    private BigDecimal changePercentage;

    /** 触发阈值 */
    @ApiModelProperty(value = "触发阈值", example = "15.0000", required = true)
    @Excel(name = "触发阈值")
    private BigDecimal thresholdValue;

    /** 阈值类型：1=百分比，2=固定金额 */
    @ApiModelProperty(value = "阈值类型", example = "1", allowableValues = "1,2")
    @Excel(name = "阈值类型", readConverterExp = "1=百分比,2=固定金额")
    private Integer thresholdType;

    /** 预警级别：1=低，2=中，3=高 */
    @ApiModelProperty(value = "预警级别", example = "2", allowableValues = "1,2,3")
    @Excel(name = "预警级别", readConverterExp = "1=低,2=中,3=高")
    private Integer alertLevel;

    /** 预警状态：1=待处理，2=已处理，3=已忽略 */
    @ApiModelProperty(value = "预警状态", example = "1", allowableValues = "1,2,3")
    @Excel(name = "预警状态", readConverterExp = "1=待处理,2=已处理,3=已忽略")
    private Integer alertStatus;

    /** 通知状态：0=未发送，1=已发送，2=发送失败 */
    @ApiModelProperty(value = "通知状态", example = "1", allowableValues = "0,1,2")
    @Excel(name = "通知状态", readConverterExp = "0=未发送,1=已发送,2=发送失败")
    private Integer notificationStatus;

    /** 预警时间 */
    @ApiModelProperty(value = "预警时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date alertTime;

    /** 处理人 */
    @ApiModelProperty(value = "处理人", example = "admin")
    @Excel(name = "处理人")
    private String handleBy;

    /** 处理时间 */
    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 处理备注 */
    @ApiModelProperty(value = "处理备注", example = "已确认价格变动正常")
    @Excel(name = "处理备注")
    private String handleRemark;

    /** 扩展信息（JSON格式） */
    @ApiModelProperty(value = "扩展信息", notes = "JSON格式的扩展数据")
    private String extraInfo;

    // ========== 辅助方法 ==========

    /** 规则类型显示文本 */
    @ApiModelProperty(value = "规则类型显示文本", example = "价格上涨")
    public String getRuleTypeDisplay() {
        if ("price_up".equals(ruleType)) {
            return "价格上涨";
        } else if ("price_down".equals(ruleType)) {
            return "价格下跌";
        }
        return ruleType;
    }

    /** 阈值显示文本 */
    @ApiModelProperty(value = "阈值显示文本", example = "15.0%")
    public String getThresholdDisplay() {
        if (thresholdValue == null) {
            return "";
        }
        return thresholdValue + (thresholdType != null && thresholdType == 1 ? "%" : "元");
    }

    /** 变动显示文本 */
    @ApiModelProperty(value = "变动显示文本", example = "+20.00(+20.0%)")
    public String getChangeDisplay() {
        if (changeAmount == null && changePercentage == null) return "";
        
        StringBuilder sb = new StringBuilder();
        if (changeAmount != null) {
            sb.append(changeAmount.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "")
              .append(changeAmount).append("元");
        }
        if (changePercentage != null) {
            if (sb.length() > 0) {
                sb.append("(");
            }
            sb.append(changePercentage.compareTo(BigDecimal.ZERO) >= 0 ? "+" : "")
              .append(changePercentage).append("%");
            if (sb.length() > changeAmount.toString().length() + 1) {
                sb.append(")");
            }
        }
        return sb.toString();
    }

    /** 预警级别显示文本 */
    @ApiModelProperty(value = "预警级别显示文本", example = "中等")
    public String getAlertLevelDisplay() {
        if (alertLevel == null) return "";
        switch (alertLevel) {
            case 1: return "低";
            case 2: return "中";
            case 3: return "高";
            default: return "未知";
        }
    }

    /** 预警状态显示文本 */
    @ApiModelProperty(value = "预警状态显示文本", example = "待处理")
    public String getAlertStatusDisplay() {
        if (alertStatus == null) return "";
        switch (alertStatus) {
            case 1: return "待处理";
            case 2: return "已处理";
            case 3: return "已忽略";
            default: return "未知";
        }
    }

    /** 通知状态显示文本 */
    @ApiModelProperty(value = "通知状态显示文本", example = "已发送")
    public String getNotificationStatusDisplay() {
        if (notificationStatus == null) {
            return "";
        }
        switch (notificationStatus) {
            case 0: return "未发送";
            case 1: return "已发送";
            case 2: return "发送失败";
            default: return "未知";
        }
    }
}
