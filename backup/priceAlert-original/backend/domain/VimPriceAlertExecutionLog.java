package com.ruoyi.project.priceAlert.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 预警执行日志对象 vim_price_alert_execution_log
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "VimPriceAlertExecutionLog", description = "预警执行日志对象")
public class VimPriceAlertExecutionLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    @ApiModelProperty(value = "日志ID", example = "1")
    private Long id;

    /** 执行类型：scheduled,manual,config_changed */
    @ApiModelProperty(value = "执行类型", example = "manual", allowableValues = "scheduled,manual,config_changed")
    @Excel(name = "执行类型")
    private String executionType;

    /** 规则类型 */
    @ApiModelProperty(value = "规则类型", example = "price_up", allowableValues = "price_up,price_down")
    @Excel(name = "规则类型")
    private String ruleType;

    /** 执行时间 */
    @ApiModelProperty(value = "执行时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "执行时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date executionTime;

    /** 检查商品总数 */
    @ApiModelProperty(value = "检查商品总数", example = "1500")
    @Excel(name = "检查商品数")
    private Integer totalItemsChecked;

    /** 触发预警数量 */
    @ApiModelProperty(value = "触发预警数量", example = "25")
    @Excel(name = "触发预警数")
    private Integer triggeredAlerts;

    /** 执行耗时（毫秒） */
    @ApiModelProperty(value = "执行耗时", example = "5000", notes = "单位：毫秒")
    @Excel(name = "执行耗时(ms)")
    private Integer executionDuration;

    /** 执行状态：1=成功，2=部分成功，3=失败 */
    @ApiModelProperty(value = "执行状态", example = "1", allowableValues = "1,2,3")
    @Excel(name = "执行状态", readConverterExp = "1=成功,2=部分成功,3=失败")
    private Integer executionStatus;

    /** 错误信息 */
    @ApiModelProperty(value = "错误信息", example = "部分商品价格获取失败")
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 执行详情（JSON格式） */
    @ApiModelProperty(value = "执行详情", notes = "JSON格式的详细执行信息")
    private String executionDetails;

    /** 触发者 */
    @ApiModelProperty(value = "触发者", example = "admin")
    @Excel(name = "触发者")
    private String triggerBy;

    // ========== 辅助方法 ==========

    /** 执行类型显示文本 */
    @ApiModelProperty(value = "执行类型显示文本", example = "手动执行")
    public String getExecutionTypeDisplay() {
        if (executionType == null) return "";
        switch (executionType) {
            case "scheduled": return "定时执行";
            case "manual": return "手动执行";
            case "config_changed": return "配置变更触发";
            default: return executionType;
        }
    }

    /** 规则类型显示文本 */
    @ApiModelProperty(value = "规则类型显示文本", example = "价格上涨")
    public String getRuleTypeDisplay() {
        if (ruleType == null) return "全部规则";
        if ("price_up".equals(ruleType)) {
            return "价格上涨";
        } else if ("price_down".equals(ruleType)) {
            return "价格下跌";
        }
        return ruleType;
    }

    /** 执行状态显示文本 */
    @ApiModelProperty(value = "执行状态显示文本", example = "成功")
    public String getExecutionStatusDisplay() {
        if (executionStatus == null) return "";
        switch (executionStatus) {
            case 1: return "成功";
            case 2: return "部分成功";
            case 3: return "失败";
            default: return "未知";
        }
    }

    /** 执行耗时显示文本 */
    @ApiModelProperty(value = "执行耗时显示文本", example = "5.0秒")
    public String getExecutionDurationDisplay() {
        if (executionDuration == null) return "";
        if (executionDuration < 1000) {
            return executionDuration + "毫秒";
        } else {
            return String.format("%.1f秒", executionDuration / 1000.0);
        }
    }

    /** 执行效率显示文本 */
    @ApiModelProperty(value = "执行效率显示文本", example = "300商品/秒")
    public String getExecutionEfficiencyDisplay() {
        if (totalItemsChecked == null || executionDuration == null || executionDuration == 0) {
            return "";
        }
        double itemsPerSecond = (totalItemsChecked * 1000.0) / executionDuration;
        return String.format("%.0f商品/秒", itemsPerSecond);
    }

    /** 预警触发率显示文本 */
    @ApiModelProperty(value = "预警触发率显示文本", example = "1.67%")
    public String getTriggerRateDisplay() {
        if (totalItemsChecked == null || triggeredAlerts == null || totalItemsChecked == 0) {
            return "";
        }
        double rate = (triggeredAlerts * 100.0) / totalItemsChecked;
        return String.format("%.2f%%", rate);
    }
}
