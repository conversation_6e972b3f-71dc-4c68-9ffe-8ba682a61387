package com.ruoyi.project.priceAlert.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 简化价格预警配置对象 vim_price_alert_config
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "VimPriceAlertConfig", description = "简化价格预警配置对象")
public class VimPriceAlertConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    @ApiModelProperty(value = "配置ID", example = "1")
    private Long id;

    /** 规则类型：price_up,price_down */
    @ApiModelProperty(value = "规则类型", example = "price_up", required = true, allowableValues = "price_up,price_down")
    @Excel(name = "规则类型")
    private String ruleType;

    /** 规则名称 */
    @ApiModelProperty(value = "规则名称", example = "全局商品价格上涨预警", required = true)
    @Excel(name = "规则名称")
    private String ruleName;

    /**
     * 阈值类型：1=百分比，2=固定金额
     */
    @ApiModelProperty(value = "阈值类型", example = "1", required = true, allowableValues = "1,2", notes = "1=百分比，2=固定金额")
    @Excel(name = "阈值类型", readConverterExp = "1=百分比,2=固定金额")
    private Integer thresholdType;

    /**
     * 阈值数值
     */
    @ApiModelProperty(value = "阈值数值", example = "10.0", required = true)
    @Excel(name = "阈值数值")
    private BigDecimal thresholdValue;

    /** 价格数据源（逗号分隔） */
    @ApiModelProperty(value = "价格数据源", example = "buff,steam", notes = "多个数据源用逗号分隔")
    @Excel(name = "价格数据源")
    private String dataSources;

    /** 数据源优先级 */
    @ApiModelProperty(value = "数据源优先级", example = "buff,steam,c5game", notes = "按优先级排序，逗号分隔")
    private String dataSourcePriority;

    /** 最低价格过滤阈值 */
    @ApiModelProperty(value = "最低价格过滤阈值", example = "10.00", notes = "低于此价格的商品不参与预警")
    @Excel(name = "最低价格过滤")
    private BigDecimal minPriceFilter;

    /** 执行时间点（逗号分隔） */
    @ApiModelProperty(value = "执行时间点", example = "09:00,15:00,21:00", notes = "多个时间点用逗号分隔")
    @Excel(name = "执行时间点")
    private String executionTimes;

    /** 是否启用：0=禁用，1=启用 */
    @ApiModelProperty(value = "是否启用", example = "1", allowableValues = "0,1")
    @Excel(name = "是否启用", readConverterExp = "0=禁用,1=启用")
    private Integer isEnabled;

    /** 通知方式：system,email,sms */
    @ApiModelProperty(value = "通知方式", example = "system,email", notes = "多种方式用逗号分隔")
    private String notificationMethods;

    /** 通知用户ID列表（JSON格式） */
    @ApiModelProperty(value = "通知用户ID列表", example = "[1,2,3]", notes = "JSON格式的用户ID数组")
    private String notificationUsers;

    /** 最后执行时间 */
    @ApiModelProperty(value = "最后执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后执行时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastExecutionTime;

    /** 最后执行触发数量 */
    @ApiModelProperty(value = "最后执行触发数量", example = "5")
    @Excel(name = "最后触发数量")
    private Integer lastTriggerCount;

    /** 总触发次数 */
    @ApiModelProperty(value = "总触发次数", example = "100")
    @Excel(name = "总触发次数")
    private Integer totalTriggerCount;

    /** 更新者 */
    @ApiModelProperty(value = "更新者", example = "admin")
    private String updateBy;

    // ========== 辅助字段 ==========

    /** 数据源列表（解析后的对象） */
    @ApiModelProperty(value = "数据源列表", hidden = true)
    private List<String> dataSourceList;

    /** 数据源优先级列表（解析后的对象） */
    @ApiModelProperty(value = "数据源优先级列表", hidden = true)
    private List<String> dataSourcePriorityList;

    /** 执行时间列表（解析后的对象） */
    @ApiModelProperty(value = "执行时间列表", hidden = true)
    private List<String> executionTimeList;

    /** 通知方式列表（解析后的对象） */
    @ApiModelProperty(value = "通知方式列表", hidden = true)
    private List<String> notificationMethodList;

    /** 通知用户ID列表（解析后的对象） */
    @ApiModelProperty(value = "通知用户ID列表", hidden = true)
    private List<Long> notificationUserList;

    /** 阈值显示文本 */
    @ApiModelProperty(value = "阈值显示文本", example = "10.0%")
    public String getThresholdDisplay() {
        if (thresholdValue == null) return "";
        return thresholdValue + (thresholdType != null && thresholdType == 1 ? "%" : "元");
    }

    /** 规则类型显示文本 */
    @ApiModelProperty(value = "规则类型显示文本", example = "价格上涨")
    public String getRuleTypeDisplay() {
        if ("price_up".equals(ruleType)) {
            return "价格上涨";
        } else if ("price_down".equals(ruleType)) {
            return "价格下跌";
        }
        return ruleType;
    }

    /** 启用状态显示文本 */
    @ApiModelProperty(value = "启用状态显示文本", example = "启用")
    public String getEnabledDisplay() {
        return isEnabled != null && isEnabled == 1 ? "启用" : "禁用";
    }
}
