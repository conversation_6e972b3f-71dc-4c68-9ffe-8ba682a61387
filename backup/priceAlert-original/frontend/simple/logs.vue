<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="规则类型" prop="ruleType">
        <el-select v-model="queryParams.ruleType" placeholder="选择规则类型" clearable>
          <el-option label="价格上涨" value="price_up" />
          <el-option label="价格下跌" value="price_down" />
        </el-select>
      </el-form-item>
      <el-form-item label="执行状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="选择执行状态" clearable>
          <el-option label="成功" value="success" />
          <el-option label="失败" value="failed" />
          <el-option label="部分成功" value="partial" />
        </el-select>
      </el-form-item>
      <el-form-item label="触发方式" prop="triggerType">
        <el-select v-model="queryParams.triggerType" placeholder="选择触发方式" clearable>
          <el-option label="定时执行" value="scheduled" />
          <el-option label="手动执行" value="manual" />
          <el-option label="配置变更" value="config_changed" />
        </el-select>
      </el-form-item>
      <el-form-item label="执行时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          size="default"
          @click="handleCleanLogs"
        >清理日志</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Download"
          size="default"
          @click="handleExport"
        >导出日志</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="logList">
      <el-table-column label="日志ID" align="center" prop="id" width="80" />
      <el-table-column label="规则类型" align="center" prop="ruleType" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.ruleType === 'price_up' ? 'danger' : 'success'">
            {{ scope.row.ruleType === 'price_up' ? '价格上涨' : '价格下跌' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="触发方式" align="center" prop="triggerType" width="100">
        <template #default="scope">
          <el-tag :type="getTriggerTypeColor(scope.row.triggerType)">
            {{ getTriggerTypeText(scope.row.triggerType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="执行状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusColor(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="检查商品数" align="center" prop="checkedItemCount" width="100" />
      <el-table-column label="触发预警数" align="center" prop="triggeredAlertCount" width="100">
        <template #default="scope">
          <span :class="scope.row.triggeredAlertCount > 0 ? 'alert-count' : ''">
            {{ scope.row.triggeredAlertCount || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="执行耗时(ms)" align="center" prop="executionTime" width="120" />
      <el-table-column label="触发者" align="center" prop="triggerBy" width="100" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleView(scope.row)"
          >查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 日志详情对话框 -->
    <el-dialog title="执行日志详情" v-model="detailOpen" width="1000px" append-to-body>
      <el-descriptions :column="3" border v-if="logDetail">
        <el-descriptions-item label="日志ID">{{ logDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="规则类型">
          <el-tag :type="logDetail.ruleType === 'price_up' ? 'danger' : 'success'">
            {{ logDetail.ruleType === 'price_up' ? '价格上涨' : '价格下跌' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="触发方式">
          <el-tag :type="getTriggerTypeColor(logDetail.triggerType)">
            {{ getTriggerTypeText(logDetail.triggerType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="执行状态">
          <el-tag :type="getStatusColor(logDetail.status)">
            {{ getStatusText(logDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="检查商品数">{{ logDetail.checkedItemCount }}</el-descriptions-item>
        <el-descriptions-item label="触发预警数">{{ logDetail.triggeredAlertCount }}</el-descriptions-item>
        <el-descriptions-item label="执行耗时">{{ logDetail.executionTime }}ms</el-descriptions-item>
        <el-descriptions-item label="触发者">{{ logDetail.triggerBy }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ parseTime(logDetail.startTime) }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ parseTime(logDetail.endTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(logDetail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="执行结果" :span="3">
          <el-input
            v-model="logDetail.executionResult"
            type="textarea"
            :rows="4"
            readonly
            placeholder="无执行结果"
          />
        </el-descriptions-item>
        <el-descriptions-item label="错误信息" :span="3" v-if="logDetail.errorMessage">
          <el-input
            v-model="logDetail.errorMessage"
            type="textarea"
            :rows="3"
            readonly
            class="error-message"
          />
        </el-descriptions-item>
        <el-descriptions-item label="执行详情" :span="3" v-if="logDetail.executionDetails">
          <el-input
            v-model="logDetail.executionDetails"
            type="textarea"
            :rows="6"
            readonly
            placeholder="无执行详情"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 清理日志对话框 -->
    <el-dialog title="清理执行日志" v-model="cleanOpen" width="500px" append-to-body>
      <el-form :model="cleanForm" :rules="cleanRules" ref="cleanFormRef" label-width="120px">
        <el-form-item label="保留天数" prop="retentionDays">
          <el-input-number
            v-model="cleanForm.retentionDays"
            :min="1"
            :max="365"
            style="width: 100%"
            placeholder="保留最近N天的日志"
          />
          <div class="form-tip">将删除 {{ cleanForm.retentionDays }} 天前的所有执行日志</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cleanOpen = false">取 消</el-button>
          <el-button type="danger" @click="submitClean">确认清理</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PriceAlertLogs">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { parseTime } from '@/utils/ruoyi'
import {
  getExecutionLogList,
  getExecutionLogById,
  cleanExpiredExecutionLogs
} from '@/api/priceAlert/simple'

// 响应式数据
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const logList = ref([])
const detailOpen = ref(false)
const cleanOpen = ref(false)
const logDetail = ref({})
const dateRange = ref([])

const queryRef = ref()
const cleanFormRef = ref()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  ruleType: null,
  status: null,
  triggerType: null,
  beginTime: null,
  endTime: null
})

// 清理表单
const cleanForm = reactive({
  retentionDays: 30
})

// 表单验证规则
const cleanRules = {
  retentionDays: [
    { required: true, message: '请输入保留天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '保留天数必须在1-365之间', trigger: 'blur' }
  ]
}

// 方法定义
const getTriggerTypeColor = (triggerType) => {
  const colorMap = {
    'scheduled': 'primary',
    'manual': 'success',
    'config_changed': 'warning'
  }
  return colorMap[triggerType] || 'info'
}

const getTriggerTypeText = (triggerType) => {
  const textMap = {
    'scheduled': '定时执行',
    'manual': '手动执行',
    'config_changed': '配置变更'
  }
  return textMap[triggerType] || '未知'
}

const getStatusColor = (status) => {
  const colorMap = {
    'success': 'success',
    'failed': 'danger',
    'partial': 'warning'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'success': '成功',
    'failed': '失败',
    'partial': '部分成功'
  }
  return textMap[status] || '未知'
}

const getList = async () => {
  loading.value = true
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.beginTime = dateRange.value[0]
    queryParams.endTime = dateRange.value[1]
  } else {
    queryParams.beginTime = null
    queryParams.endTime = null
  }
  
  try {
    const response = await getExecutionLogList(queryParams)
    logList.value = response.rows
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取日志列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const resetQuery = () => {
  dateRange.value = []
  queryRef.value.resetFields()
  handleQuery()
}

const handleView = async (row) => {
  try {
    const response = await getExecutionLogById(row.id)
    logDetail.value = response.data
    detailOpen.value = true
  } catch (error) {
    ElMessage.error('获取日志详情失败: ' + error.message)
  }
}

const handleCleanLogs = () => {
  cleanForm.retentionDays = 30
  cleanOpen.value = true
}

const submitClean = async () => {
  try {
    await cleanFormRef.value.validate()
    
    await ElMessageBox.confirm(
      `确认清理 ${cleanForm.retentionDays} 天前的所有执行日志吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await cleanExpiredExecutionLogs(cleanForm.retentionDays)
    if (response.code === 200) {
      ElMessage.success('日志清理成功')
      cleanOpen.value = false
      getList()
    } else {
      ElMessage.error('日志清理失败: ' + response.msg)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('日志清理失败: ' + error.message)
    }
  }
}

const handleExport = () => {
  // 导出功能实现
  ElMessage.info('导出功能开发中...')
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.alert-count {
  color: #f56c6c;
  font-weight: bold;
}

.error-message .el-textarea__inner {
  color: #f56c6c;
  background-color: #fef0f0;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
