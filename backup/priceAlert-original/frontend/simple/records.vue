<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="商品名称" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规则类型" prop="ruleType">
        <el-select v-model="queryParams.ruleType" placeholder="选择规则类型" clearable>
          <el-option label="价格上涨" value="price_up" />
          <el-option label="价格下跌" value="price_down" />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="alertStatus">
        <el-select v-model="queryParams.alertStatus" placeholder="选择处理状态" clearable>
          <el-option label="待处理" value="1" />
          <el-option label="已处理" value="2" />
          <el-option label="已忽略" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          size="default"
          :disabled="multiple"
          @click="handleBatchHandle"
        >批量处理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          size="default"
          :disabled="multiple"
          @click="handleDelete"
        >批量删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="id" width="80" />
      <el-table-column label="商品名称" align="center" prop="itemName" :show-overflow-tooltip="true" />
      <el-table-column label="规则类型" align="center" prop="ruleType" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.ruleType === 'price_up' ? 'danger' : 'success'">
            {{ scope.row.ruleType === 'price_up' ? '价格上涨' : '价格下跌' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="价格变化" align="center" width="120">
        <template #default="scope">
          <span :class="scope.row.ruleType === 'price_up' ? 'price-up' : 'price-down'">
            {{ scope.row.oldPrice }} → {{ scope.row.newPrice }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="变化幅度" align="center" prop="changePercentage" width="100">
        <template #default="scope">
          <span :class="scope.row.ruleType === 'price_up' ? 'price-up' : 'price-down'">
            {{ scope.row.changePercentage }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column label="数据源" align="center" prop="dataSource" width="100" />
      <el-table-column label="处理状态" align="center" prop="alertStatus" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.alertStatus)">
            {{ getStatusText(scope.row.alertStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="handleBy" width="100" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            link
            type="success"
            icon="Edit"
            @click="handleProcess(scope.row)"
            v-if="scope.row.alertStatus === 1"
          >处理</el-button>
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 记录详情对话框 -->
    <el-dialog title="预警记录详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border v-if="recordDetail">
        <el-descriptions-item label="记录ID">{{ recordDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="商品名称">{{ recordDetail.itemName }}</el-descriptions-item>
        <el-descriptions-item label="规则类型">
          <el-tag :type="recordDetail.ruleType === 'price_up' ? 'danger' : 'success'">
            {{ recordDetail.ruleType === 'price_up' ? '价格上涨' : '价格下跌' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="数据源">{{ recordDetail.dataSource }}</el-descriptions-item>
        <el-descriptions-item label="原价格">{{ recordDetail.oldPrice }}</el-descriptions-item>
        <el-descriptions-item label="新价格">{{ recordDetail.newPrice }}</el-descriptions-item>
        <el-descriptions-item label="变化幅度">
          <span :class="recordDetail.ruleType === 'price_up' ? 'price-up' : 'price-down'">
            {{ recordDetail.changePercentage }}%
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag :type="getStatusType(recordDetail.alertStatus)">
            {{ getStatusText(recordDetail.alertStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(recordDetail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="处理时间">{{ parseTime(recordDetail.handleTime) }}</el-descriptions-item>
        <el-descriptions-item label="处理人">{{ recordDetail.handleBy || '-' }}</el-descriptions-item>
        <el-descriptions-item label="处理备注" :span="2">{{ recordDetail.handleRemark || '-' }}</el-descriptions-item>
        <el-descriptions-item label="预警详情" :span="2">{{ recordDetail.alertDetails || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 处理记录对话框 -->
    <el-dialog title="处理预警记录" v-model="processOpen" width="600px" append-to-body>
      <el-form :model="processForm" :rules="processRules" ref="processFormRef" label-width="100px">
        <el-form-item label="处理状态" prop="alertStatus">
          <el-radio-group v-model="processForm.alertStatus">
            <el-radio :label="2">已处理</el-radio>
            <el-radio :label="3">已忽略</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理备注" prop="handleRemark">
          <el-input
            v-model="processForm.handleRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入处理备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitProcess">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量处理对话框 -->
    <el-dialog title="批量处理预警记录" v-model="batchProcessOpen" width="600px" append-to-body>
      <el-form :model="batchProcessForm" :rules="processRules" ref="batchProcessFormRef" label-width="100px">
        <el-form-item label="处理状态" prop="alertStatus">
          <el-radio-group v-model="batchProcessForm.alertStatus">
            <el-radio :label="2">已处理</el-radio>
            <el-radio :label="3">已忽略</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理备注" prop="handleRemark">
          <el-input
            v-model="batchProcessForm.handleRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入处理备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchProcessOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitBatchProcess">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PriceAlertRecords">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { parseTime } from '@/utils/ruoyi'
import {
  getRecordList,
  getRecordById,
  handleRecord,
  batchHandleRecords,
  deleteRecords
} from '@/api/priceAlert/simple'

// 响应式数据
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const multiple = ref(true)
const total = ref(0)
const recordList = ref([])
const detailOpen = ref(false)
const processOpen = ref(false)
const batchProcessOpen = ref(false)
const recordDetail = ref({})
const dateRange = ref([])

const queryRef = ref()
const processFormRef = ref()
const batchProcessFormRef = ref()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  itemName: null,
  ruleType: null,
  alertStatus: null,
  beginTime: null,
  endTime: null
})

// 处理表单
const processForm = reactive({
  id: null,
  alertStatus: 2,
  handleRemark: ''
})

const batchProcessForm = reactive({
  ids: [],
  alertStatus: 2,
  handleRemark: ''
})

// 表单验证规则
const processRules = {
  alertStatus: [
    { required: true, message: '请选择处理状态', trigger: 'change' }
  ],
  handleRemark: [
    { required: true, message: '请输入处理备注', trigger: 'blur' }
  ]
}

// 方法定义
const getStatusType = (status) => {
  const statusMap = {
    1: 'warning',
    2: 'success',
    3: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    1: '待处理',
    2: '已处理',
    3: '已忽略'
  }
  return statusMap[status] || '未知'
}

const getList = async () => {
  loading.value = true
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.beginTime = dateRange.value[0]
    queryParams.endTime = dateRange.value[1]
  } else {
    queryParams.beginTime = null
    queryParams.endTime = null
  }
  
  try {
    const response = await getRecordList(queryParams)
    recordList.value = response.rows
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取记录列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const resetQuery = () => {
  dateRange.value = []
  queryRef.value.resetFields()
  handleQuery()
}

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id)
  multiple.value = !selection.length
}

const handleView = async (row) => {
  try {
    const response = await getRecordById(row.id)
    recordDetail.value = response.data
    detailOpen.value = true
  } catch (error) {
    ElMessage.error('获取记录详情失败: ' + error.message)
  }
}

const handleProcess = (row) => {
  processForm.id = row.id
  processForm.alertStatus = 2
  processForm.handleRemark = ''
  processOpen.value = true
}

const submitProcess = async () => {
  try {
    await processFormRef.value.validate()
    // 转换字段名称以匹配后端API
    const requestData = {
      id: processForm.id,
      status: processForm.alertStatus,
      handleRemark: processForm.handleRemark
    }
    const response = await handleRecord(requestData)
    if (response.code === 200) {
      ElMessage.success('处理成功')
      processOpen.value = false
      getList()
    } else {
      ElMessage.error('处理失败: ' + response.msg)
    }
  } catch (error) {
    ElMessage.error('处理失败: ' + error.message)
  }
}

const handleBatchHandle = () => {
  if (ids.value.length === 0) {
    ElMessage.warning('请选择要处理的记录')
    return
  }
  batchProcessForm.ids = ids.value
  batchProcessForm.alertStatus = 2
  batchProcessForm.handleRemark = ''
  batchProcessOpen.value = true
}

const submitBatchProcess = async () => {
  try {
    await batchProcessFormRef.value.validate()
    // 转换字段名称以匹配后端API
    const requestData = {
      ids: batchProcessForm.ids,
      status: batchProcessForm.alertStatus,
      handleRemark: batchProcessForm.handleRemark
    }
    const response = await batchHandleRecords(requestData)
    if (response.code === 200) {
      ElMessage.success('批量处理成功')
      batchProcessOpen.value = false
      getList()
    } else {
      ElMessage.error('批量处理失败: ' + response.msg)
    }
  } catch (error) {
    ElMessage.error('批量处理失败: ' + error.message)
  }
}

const handleDelete = async (row) => {
  const recordIds = row ? [row.id] : ids.value
  if (recordIds.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm('是否确认删除选中的预警记录？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await deleteRecords(recordIds)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error('删除失败: ' + response.msg)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.price-up {
  color: #f56c6c;
  font-weight: bold;
}

.price-down {
  color: #67c23a;
  font-weight: bold;
}
</style>
