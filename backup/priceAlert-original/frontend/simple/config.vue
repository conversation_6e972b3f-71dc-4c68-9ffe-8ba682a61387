<template>
  <div class="app-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="page-title">
        <h2>价格预警配置</h2>
        <p>配置全局价格预警规则，支持价格上涨和下跌两种预警类型</p>
      </div>
      <div class="page-actions">
        <el-button type="primary" icon="Refresh" @click="refreshConfigs">刷新配置</el-button>
        <el-button type="success" icon="VideoPlay" @click="executeManualCheck">手动执行检查</el-button>
      </div>
    </div>

    <!-- 配置卡片区域 -->
    <div class="config-cards">
      <!-- 价格上涨预警配置 -->
      <el-card class="config-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="card-title">
              <el-icon class="title-icon" color="#f56c6c"><TrendCharts /></el-icon>
              <span>价格上涨预警</span>
            </div>
            <el-switch
              v-model="priceUpConfig.isEnabled"
              :active-value="1"
              :inactive-value="0"
              active-text="启用"
              inactive-text="禁用"
              @change="toggleRuleStatus('price_up', priceUpConfig.isEnabled)"
            />
          </div>
        </template>
        
        <el-form :model="priceUpConfig" :rules="priceUpRules" ref="priceUpFormRef" label-width="120px" class="price-alert-form">
          <!-- 第一行：阈值配置 -->
          <el-row :gutter="24" class="form-row">
            <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="8">
              <el-form-item label="阈值类型" prop="thresholdType">
                <el-select
                  v-model="priceUpConfig.thresholdType"
                  placeholder="选择类型"
                  style="width: 100%"
                  size="default"
                >
                  <el-option label="百分比(%)" :value="1" />
                  <el-option label="固定金额(¥)" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="9" :lg="9" :xl="8">
              <el-form-item
                :label="priceUpConfig.thresholdType === 1 ? '阈值(%)' : '阈值(¥)'"
                prop="thresholdValue"
              >
                <el-input-number
                  v-model="priceUpConfig.thresholdValue"
                  :min="priceUpConfig.thresholdType === 1 ? 0.1 : 0.01"
                  :max="priceUpConfig.thresholdType === 1 ? 100 : 9999"
                  :precision="priceUpConfig.thresholdType === 1 ? 1 : 2"
                  :step="priceUpConfig.thresholdType === 1 ? 0.1 : 0.01"
                  style="width: 100%"
                  size="default"
                  :placeholder="priceUpConfig.thresholdType === 1 ? '如: 10.0' : '如: 5.00'"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="9" :lg="9" :xl="8">
              <el-form-item label="最低价格过滤" prop="minPriceFilter">
                <el-input-number
                  v-model="priceUpConfig.minPriceFilter"
                  :min="0"
                  :precision="2"
                  :step="1"
                  style="width: 100%"
                  size="default"
                  placeholder="如: 20.00"
                >
                  <template #append>¥</template>
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行：数据源和执行时间 -->
          <el-row :gutter="24" class="form-row">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="数据源" prop="dataSources">
                <el-select
                  v-model="priceUpConfig.dataSources"
                  placeholder="选择数据源"
                  style="width: 100%"
                  size="default"
                >
                  <el-option label="🔄 所有数据源" value="buff,steam,c5game" />
                  <el-option label="🎯 BUFF市场" value="buff" />
                  <el-option label="🎮 Steam市场" value="steam" />
                  <el-option label="🎲 C5Game市场" value="c5game" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="执行时间点" prop="executionTimes">
                <el-input
                  v-model="priceUpConfig.executionTimes"
                  placeholder="如: 09:00,15:00,21:00"
                  style="width: 100%"
                  size="default"
                >
                  <template #prepend>⏰</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行：通知方式 -->
          <el-row :gutter="24" class="form-row">
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="通知方式" prop="notificationMethods">
                <el-checkbox-group v-model="priceUpNotificationMethods" class="notification-group">
                  <el-checkbox label="system" size="default">📢 系统通知</el-checkbox>
                  <el-checkbox label="email" size="default">📧 邮件通知</el-checkbox>
                  <el-checkbox label="sms" size="default">📱 短信通知</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button type="primary" size="default" @click="saveConfig('price_up')" :loading="loading">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
            <el-button type="success" size="default" @click="testRule('price_up')" :loading="loading">
              <el-icon><VideoPlay /></el-icon>
              启用规则
            </el-button>
            <el-button type="warning" size="default" @click="resetConfig('price_up')" :loading="loading">
              <el-icon><RefreshLeft /></el-icon>
              重置默认
            </el-button>
          </div>
        </el-form>

        <!-- 统计信息 -->
        <div class="config-stats" v-if="priceUpConfig.lastExecutionTime">
          <el-divider content-position="left">执行统计</el-divider>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-item">
                <span class="stat-label">最后执行:</span>
                <span class="stat-value">{{ formatDate(priceUpConfig.lastExecutionTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <span class="stat-label">最后触发:</span>
                <span class="stat-value">{{ priceUpConfig.lastTriggerCount || 0 }}次</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <span class="stat-label">总触发:</span>
                <span class="stat-value">{{ priceUpConfig.totalTriggerCount || 0 }}次</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 价格下跌预警配置 -->
      <el-card class="config-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="card-title">
              <el-icon class="title-icon" color="#67c23a"><TrendCharts /></el-icon>
              <span>价格下跌预警</span>
            </div>
            <el-switch
              v-model="priceDownConfig.isEnabled"
              :active-value="1"
              :inactive-value="0"
              active-text="启用"
              inactive-text="禁用"
              @change="toggleRuleStatus('price_down', priceDownConfig.isEnabled)"
            />
          </div>
        </template>
        
        <el-form :model="priceDownConfig" :rules="priceDownRules" ref="priceDownFormRef" label-width="120px" class="price-alert-form">
          <!-- 第一行：阈值配置 -->
          <el-row :gutter="24" class="form-row">
            <el-col :xs="24" :sm="8" :md="6" :lg="6" :xl="8">
              <el-form-item label="阈值类型" prop="thresholdType">
                <el-select
                  v-model="priceDownConfig.thresholdType"
                  placeholder="选择类型"
                  style="width: 100%"
                  size="default"
                >
                  <el-option label="百分比(%)" :value="1" />
                  <el-option label="固定金额(¥)" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="9" :lg="9" :xl="8">
              <el-form-item
                :label="priceDownConfig.thresholdType === 1 ? '阈值(%)' : '阈值(¥)'"
                prop="thresholdValue"
              >
                <el-input-number
                  v-model="priceDownConfig.thresholdValue"
                  :min="priceDownConfig.thresholdType === 1 ? 0.1 : 0.01"
                  :max="priceDownConfig.thresholdType === 1 ? 100 : 9999"
                  :precision="priceDownConfig.thresholdType === 1 ? 1 : 2"
                  :step="priceDownConfig.thresholdType === 1 ? 0.1 : 0.01"
                  style="width: 100%"
                  size="default"
                  :placeholder="priceDownConfig.thresholdType === 1 ? '如: 10.0' : '如: 5.00'"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="9" :lg="9" :xl="8">
              <el-form-item label="最低价格过滤" prop="minPriceFilter">
                <el-input-number
                  v-model="priceDownConfig.minPriceFilter"
                  :min="0"
                  :precision="2"
                  :step="1"
                  style="width: 100%"
                  size="default"
                  placeholder="如: 20.00"
                >
                  <template #append>¥</template>
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行：数据源和执行时间 -->
          <el-row :gutter="24" class="form-row">
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="数据源" prop="dataSources">
                <el-select
                  v-model="priceDownConfig.dataSources"
                  placeholder="选择数据源"
                  style="width: 100%"
                  size="default"
                >
                  <el-option label="🔄 所有数据源" value="buff,steam,c5game" />
                  <el-option label="🎯 BUFF市场" value="buff" />
                  <el-option label="🎮 Steam市场" value="steam" />
                  <el-option label="🎲 C5Game市场" value="c5game" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="执行时间点" prop="executionTimes">
                <el-input
                  v-model="priceDownConfig.executionTimes"
                  placeholder="如: 09:00,15:00,21:00"
                  style="width: 100%"
                  size="default"
                >
                  <template #prepend>⏰</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行：通知方式 -->
          <el-row :gutter="24" class="form-row">
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="通知方式" prop="notificationMethods">
                <el-checkbox-group v-model="priceDownNotificationMethods" class="notification-group">
                  <el-checkbox label="system" size="default">📢 系统通知</el-checkbox>
                  <el-checkbox label="email" size="default">📧 邮件通知</el-checkbox>
                  <el-checkbox label="sms" size="default">📱 短信通知</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button type="primary" size="default" @click="saveConfig('price_down')" :loading="loading">
              <el-icon><Check /></el-icon>
              保存配置
            </el-button>
            <el-button type="success" size="default" @click="testRule('price_down')" :loading="loading">
              <el-icon><VideoPlay /></el-icon>
              启用规则
            </el-button>
            <el-button type="warning" size="default" @click="resetConfig('price_down')" :loading="loading">
              <el-icon><RefreshLeft /></el-icon>
              重置默认
            </el-button>
          </div>
        </el-form>

        <!-- 统计信息 -->
        <div class="config-stats" v-if="priceDownConfig.lastExecutionTime">
          <el-divider content-position="left">执行统计</el-divider>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-item">
                <span class="stat-label">最后执行:</span>
                <span class="stat-value">{{ formatDate(priceDownConfig.lastExecutionTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <span class="stat-label">最后触发:</span>
                <span class="stat-value">{{ priceDownConfig.lastTriggerCount || 0 }}次</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <span class="stat-label">总触发:</span>
                <span class="stat-value">{{ priceDownConfig.totalTriggerCount || 0 }}次</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="PriceAlertConfig">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  TrendCharts,
  Check,
  VideoPlay,
  RefreshLeft,
  Refresh
} from '@element-plus/icons-vue'
import {
  getAllConfigs,
  updateConfig,
  toggleRuleStatusApi,
  resetConfig as resetConfigApi,
  testRule as testRuleApi,
  executeManualCheck as executeManualCheckApi
} from '@/api/priceAlert/simple'

// 响应式数据
const priceUpFormRef = ref()
const priceDownFormRef = ref()
const loading = ref(false)

// 配置数据
const priceUpConfig = reactive({
  id: null,
  ruleType: 'price_up',
  ruleName: '全局商品价格上涨预警',
  thresholdType: 1, // 1=百分比，2=固定金额
  thresholdValue: 10.0, // 阈值数值
  minPriceFilter: 10.0,
  dataSources: 'buff,steam,c5game', // 修改为复数形式
  dataSourcePriority: 'buff,steam,c5game', // 数据源优先级
  executionTimes: '09:00,15:00,21:00',
  isEnabled: 1,
  notificationMethods: 'system',
  lastExecutionTime: null,
  lastTriggerCount: 0,
  totalTriggerCount: 0
})

const priceDownConfig = reactive({
  id: null,
  ruleType: 'price_down',
  ruleName: '全局商品价格下跌预警',
  thresholdType: 1, // 1=百分比，2=固定金额
  thresholdValue: 10.0, // 阈值数值
  minPriceFilter: 10.0,
  dataSources: 'buff,steam,c5game', // 修改为复数形式
  dataSourcePriority: 'buff,steam,c5game', // 数据源优先级
  executionTimes: '09:00,15:00,21:00',
  isEnabled: 1,
  notificationMethods: 'system',
  lastExecutionTime: null,
  lastTriggerCount: 0,
  totalTriggerCount: 0
})

// 通知方式的计算属性
const priceUpNotificationMethods = computed({
  get: () => priceUpConfig.notificationMethods ? priceUpConfig.notificationMethods.split(',') : [],
  set: (val) => priceUpConfig.notificationMethods = val.join(',')
})

const priceDownNotificationMethods = computed({
  get: () => priceDownConfig.notificationMethods ? priceDownConfig.notificationMethods.split(',') : [],
  set: (val) => priceDownConfig.notificationMethods = val.join(',')
})

// 创建动态验证规则的函数
const createValidationRules = (config) => ({
  thresholdType: [
    { required: true, message: '请选择阈值类型', trigger: 'change' }
  ],
  thresholdValue: [
    { required: true, message: '请输入阈值数值', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (config.thresholdType === 1) {
          // 百分比验证
          if (value < 0.1 || value > 100) {
            callback(new Error('百分比阈值必须在0.1-100之间'))
          }
        } else {
          // 固定金额验证
          if (value < 0.01 || value > 9999) {
            callback(new Error('金额阈值必须在0.01-9999之间'))
          }
        }
        callback()
      },
      trigger: 'blur'
    }
  ],
  minPriceFilter: [
    { required: true, message: '请输入最低价格过滤值', trigger: 'blur' },
    { type: 'number', min: 0, message: '最低价格过滤值不能小于0', trigger: 'blur' }
  ],
  dataSources: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  executionTimes: [
    { required: true, message: '请输入执行时间点', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && !/^(\d{2}:\d{2})(,\d{2}:\d{2})*$/.test(value)) {
          callback(new Error('时间格式不正确，请使用 HH:MM,HH:MM 格式'))
        }
        callback()
      },
      trigger: 'blur'
    }
  ]
})

// 表单验证规则
const priceUpRules = computed(() => createValidationRules(priceUpConfig))
const priceDownRules = computed(() => createValidationRules(priceDownConfig))

// 方法定义
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 数据一致性验证函数
const validateConfigData = (config) => {
  const errors = []

  // 必需字段检查
  if (!config.ruleType) errors.push('规则类型不能为空')
  if (!config.ruleName) errors.push('规则名称不能为空')
  if (config.thresholdType === null || config.thresholdType === undefined) {
    errors.push('阈值类型不能为空')
  }
  if (config.thresholdValue === null || config.thresholdValue === undefined) {
    errors.push('阈值数值不能为空')
  }

  // 数据类型检查
  if (typeof config.thresholdType !== 'number') {
    errors.push('阈值类型必须是数字')
  }
  if (typeof config.thresholdValue !== 'number') {
    errors.push('阈值数值必须是数字')
  }
  if (typeof config.minPriceFilter !== 'number') {
    errors.push('最低价格过滤必须是数字')
  }
  if (typeof config.isEnabled !== 'number') {
    errors.push('启用状态必须是数字')
  }

  // 数值范围检查
  if (config.thresholdType === 1) {
    if (config.thresholdValue < 0.1 || config.thresholdValue > 100) {
      errors.push('百分比阈值必须在0.1-100之间')
    }
  } else if (config.thresholdType === 2) {
    if (config.thresholdValue < 0.01 || config.thresholdValue > 9999) {
      errors.push('金额阈值必须在0.01-9999之间')
    }
  }

  if (config.minPriceFilter < 0) {
    errors.push('最低价格过滤不能小于0')
  }

  // 字符串字段检查
  if (!config.dataSources || config.dataSources.trim() === '') {
    errors.push('数据源不能为空')
  }
  if (!config.executionTimes || config.executionTimes.trim() === '') {
    errors.push('执行时间点不能为空')
  }

  return errors
}

const loadConfigs = async () => {
  try {
    loading.value = true
    const response = await getAllConfigs()
    if (response.code === 200 && response.data) {
      response.data.forEach(config => {
        // 数据类型转换和默认值处理
        const processedConfig = {
          ...config,
          thresholdValue: Number(config.thresholdValue) || 10.0,
          minPriceFilter: Number(config.minPriceFilter) || 10.0,
          thresholdType: Number(config.thresholdType) || 1,
          isEnabled: Number(config.isEnabled) || 0,
          dataSources: config.dataSources || 'buff,steam,c5game',
          dataSourcePriority: config.dataSourcePriority || config.dataSources || 'buff,steam,c5game',
          executionTimes: config.executionTimes || '09:00,15:00,21:00',
          notificationMethods: config.notificationMethods || 'system',
          lastTriggerCount: Number(config.lastTriggerCount) || 0,
          totalTriggerCount: Number(config.totalTriggerCount) || 0
        }

        if (config.ruleType === 'price_up') {
          Object.assign(priceUpConfig, processedConfig)
          console.log('加载价格上涨配置:', processedConfig)
        } else if (config.ruleType === 'price_down') {
          Object.assign(priceDownConfig, processedConfig)
          console.log('加载价格下跌配置:', processedConfig)
        }
      })
    } else {
      ElMessage.warning('未找到配置数据，使用默认配置')
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const saveConfig = async (ruleType) => {
  const formRef = ruleType === 'price_up' ? priceUpFormRef.value : priceDownFormRef.value
  const config = ruleType === 'price_up' ? priceUpConfig : priceDownConfig

  try {
    loading.value = true
    await formRef.validate()

    // 数据预处理和类型转换
    const configData = {
      ...config,
      // 确保数值类型正确
      thresholdValue: Number(config.thresholdValue),
      minPriceFilter: Number(config.minPriceFilter),
      thresholdType: Number(config.thresholdType),
      isEnabled: Number(config.isEnabled),
      // 确保数据源优先级字段与数据源字段一致
      dataSourcePriority: config.dataSources,
      // 确保字符串字段不为空
      dataSources: config.dataSources || 'buff,steam,c5game',
      executionTimes: config.executionTimes || '09:00,15:00,21:00',
      notificationMethods: config.notificationMethods || 'system'
    }

    // 数据一致性验证
    const validationErrors = validateConfigData(configData)
    if (validationErrors.length > 0) {
      ElMessage.error('数据验证失败: ' + validationErrors.join(', '))
      return
    }

    // 添加调试日志
    console.log('保存配置数据:', {
      ruleType: configData.ruleType,
      thresholdType: configData.thresholdType,
      thresholdValue: configData.thresholdValue,
      dataSources: configData.dataSources,
      dataSourcePriority: configData.dataSourcePriority,
      minPriceFilter: configData.minPriceFilter,
      isEnabled: configData.isEnabled
    })

    const response = await updateConfig(configData)
    if (response.code === 200) {
      ElMessage.success('配置保存成功')
      await loadConfigs()
    } else {
      ElMessage.error('配置保存失败: ' + response.msg)
    }
  } catch (error) {
    console.error('配置保存失败:', error)
    ElMessage.error('配置保存失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const toggleRuleStatus = async (ruleType, enabled) => {
  try {
    const response = await toggleRuleStatusApi(ruleType, enabled)
    if (response.code === 200) {
      ElMessage.success(`规则${enabled ? '启用' : '禁用'}成功`)
    } else {
      ElMessage.error('状态切换失败: ' + response.msg)
    }
  } catch (error) {
    ElMessage.error('状态切换失败: ' + error.message)
  }
}

const resetConfig = async (ruleType) => {
  try {
    await ElMessageBox.confirm('确认重置该规则配置为默认值吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await resetConfigApi(ruleType)
    if (response.code === 200) {
      ElMessage.success('配置重置成功')
      await loadConfigs()
    } else {
      ElMessage.error('配置重置失败: ' + response.msg)
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('配置重置失败: ' + error.message)
    }
  }
}

const testRule = async (ruleType) => {
  try {
    const response = await testRuleApi(ruleType)
    if (response.code === 200) {
      ElMessage.success('规则测试完成，请查看执行结果')
    } else {
      ElMessage.error('规则测试失败: ' + response.msg)
    }
  } catch (error) {
    ElMessage.error('规则测试失败: ' + error.message)
  }
}

const executeManualCheck = async () => {
  const response = await executeManualCheckApi()
  if (response.code === 200) {
    ElMessage.success('手动检查执行完成')
    await loadConfigs()
  } else {
    ElMessage.error('手动检查失败: ' + response.msg)
  }
}

const refreshConfigs = async () => {
  await loadConfigs()
  ElMessage.success('配置刷新成功')
}

// 生命周期
onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-title h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 页面整体布局 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px 0;
}

.page-title h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-title p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.page-actions {
  display: flex;
  gap: 12px;
}

/* 配置卡片布局 */
.config-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

@media (max-width: 1200px) {
  .config-cards {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.config-card {
  min-height: 520px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.config-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.title-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 表单布局优化 */
.price-alert-form {
  padding: 20px 0;
}

.form-row {
  margin-bottom: 20px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.price-alert-form .el-form-item {
  margin-bottom: 18px;
}

.price-alert-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
  line-height: 32px;
}

.price-alert-form .el-input-number {
  width: 100%;
}

.price-alert-form .el-select {
  width: 100%;
}

/* 通知方式组件 */
.notification-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.notification-group .el-checkbox {
  margin-right: 0;
  white-space: nowrap;
}

/* 操作按钮 */
.form-actions {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 8px;
  min-width: 100px;
}

.form-actions .el-button + .el-button {
  margin-left: 12px;
}

/* 统计信息 */
.config-stats {
  margin-top: 24px;
  padding-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .config-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .price-alert-form {
    padding: 16px 0;
  }

  .form-row .el-col {
    margin-bottom: 16px;
  }

  .notification-group {
    flex-direction: column;
    gap: 12px;
  }

  .form-actions {
    margin-top: 20px;
    padding-top: 16px;
  }

  .form-actions .el-button {
    margin: 4px 6px;
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .page-title h2 {
    font-size: 20px;
  }

  .config-card {
    min-height: auto;
  }

  .price-alert-form {
    padding: 12px 0;
  }

  .form-actions .el-button {
    display: block;
    width: 100%;
    margin: 8px 0;
  }
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.enabled {
  background-color: #f0f9ff;
  color: #1890ff;
}

.status-indicator.disabled {
  background-color: #fafafa;
  color: #8c8c8c;
}

/* 加载状态 */
.loading-overlay {
  position: relative;
}

.loading-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.stat-item {
  text-align: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

@media (max-width: 1200px) {
  .config-cards {
    grid-template-columns: 1fr;
  }
}
</style>
