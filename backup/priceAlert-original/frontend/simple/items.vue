<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="商品名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品标签" prop="tag">
        <el-input
          v-model="queryParams.tag"
          placeholder="请输入商品标签"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="价格范围">
        <el-input-number
          v-model="queryParams.params.minPrice"
          placeholder="最低价格"
          :min="0"
          :precision="2"
        />
        <span style="margin: 0 8px">-</span>
        <el-input-number
          v-model="queryParams.params.maxPrice"
          placeholder="最高价格"
          :min="0"
          :precision="2"
        />
      </el-form-item>
<!--      <el-form-item label="涨跌状态" prop="priceStatus">-->
<!--        <el-select v-model="queryParams.params.priceStatus" placeholder="选择涨跌状态" clearable>-->
<!--          <el-option label="全部" value="all" />-->
<!--          <el-option label="上涨" value="up" />-->
<!--          <el-option label="下跌" value="down" />-->
<!--          <el-option label="持平" value="stable" />-->
<!--          <el-option label="无数据" value="no_data" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="是否在售" prop="sale">
        <el-select v-model="queryParams.sale" placeholder="选择销售状态" clearable>
          <el-option label="在售" :value="1" />
          <el-option label="停售" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Refresh"
          :disabled="multiple"
          @click="handleBatchUpdate"
        >批量更新价格</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Download"
          @click="handleExport"
        >导出数据</el-button>
      </el-col>
      <right-toolbar :showSearch="showSearch" @update:showSearch="val => showSearch = val" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="itemList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="商品图片" align="center" width="80">
        <template #default="scope">
          <el-image
            v-if="scope.row.image"
            :src="scope.row.image"
            :preview-src-list="[scope.row.image]"
            fit="cover"
            style="width: 50px; height: 50px; border-radius: 4px;"
            :preview-teleported="true"
          />
          <span v-else class="no-image">无图片</span>
        </template>
      </el-table-column>
      <el-table-column label="商品名称" align="left" prop="name" :show-overflow-tooltip="true" min-width="200">
        <template #default="scope">
          <div class="item-info">
            <div class="item-name">{{ scope.row.name }}</div>
            <div class="item-tag" v-if="scope.row.tag">{{ scope.row.tag }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="当前展示价" align="center" prop="priceShow" width="120">
        <template #default="scope">
          <span class="price-text">{{ safeFormatPrice(scope.row.priceShow, '¥0.00') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参考价格" align="center" width="120">
        <template #default="scope">
          <span class="price-text" v-if="isValidNumber(scope.row.referenceSellMinPrice)">
            {{ safeFormatPrice(scope.row.referenceSellMinPrice) }}
          </span>
          <span v-else class="no-data">无数据</span>
        </template>
      </el-table-column>
      <el-table-column label="价格差值" align="center" width="120">
        <template #default="scope">
          <span v-if="isValidNumber(scope.row.priceDifference)"
                :class="getPriceDiffClass(scope.row.priceDifference)">
            {{ formatPriceDiff(scope.row.priceDifference) }}
          </span>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
      <el-table-column label="变化百分比" align="center" width="140">
        <template #default="scope">
          <div v-if="isValidNumber(scope.row.priceChangePercent)" class="price-change">
            <el-icon :class="getPriceChangeIconClass(scope.row.priceStatus)">
              <component :is="getPriceChangeIcon(scope.row.priceStatus)" />
            </el-icon>
            <span :style="{ color: scope.row.priceStatusColor }">
              {{ formatPercentage(scope.row.priceChangePercent) }}%
            </span>
          </div>
          <span v-else class="no-data">-</span>
        </template>
      </el-table-column>
      <el-table-column label="涨跌状态" align="center" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.priceStatus)" size="small">
            {{ scope.row.priceStatusText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="库存" align="center" prop="stock" width="80" />
      <el-table-column label="销售状态" align="center" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.sale === 1 ? 'success' : 'danger'" size="small">
            {{ scope.row.sale === 1 ? '在售' : '停售' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最后更新" align="center" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastPriceUpdate) || parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleView(scope.row)"
          >查看详情</el-button>
          <el-button
            link
            type="success"
            icon="Edit"
            @click="handleEdit(scope.row)"
          >编辑商品</el-button>
          <el-button
            link
            type="warning"
            icon="Refresh"
            @click="handleRefreshPrice(scope.row)"
          >刷新价格</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="val => queryParams.pageNum = val"
      @update:limit="val => queryParams.pageSize = val"
      @pagination="getList"
    />

    <!-- 商品详情对话框 -->
    <el-dialog title="商品价格分析详情" v-model="detailOpen" width="1000px" append-to-body>
      <el-descriptions :column="3" border v-if="itemDetail">
        <el-descriptions-item label="商品ID">{{ itemDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="商品名称">{{ itemDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="商品标签">{{ itemDetail.tag || '-' }}</el-descriptions-item>
        <el-descriptions-item label="哈希名称" :span="3">{{ itemDetail.hashname }}</el-descriptions-item>
        
        <el-descriptions-item label="当前展示价">{{ safeFormatPrice(itemDetail.priceShow, '¥0.00') }}</el-descriptions-item>
        <el-descriptions-item label="成本价">{{ safeFormatPrice(itemDetail.priceCost, '¥0.00') }}</el-descriptions-item>
        <el-descriptions-item label="收购价">{{ safeFormatPrice(itemDetail.priceBuy, '¥0.00') }}</el-descriptions-item>

        <el-descriptions-item label="参考售价">
          <span v-if="isValidNumber(itemDetail.referenceSellMinPrice)">{{ safeFormatPrice(itemDetail.referenceSellMinPrice) }}</span>
          <span v-else class="no-data">无数据</span>
        </el-descriptions-item>
        <el-descriptions-item label="参考收购价">
          <span v-if="itemDetail.referenceBuyMaxPrice">¥{{ itemDetail.referenceBuyMaxPrice }}</span>
          <span v-else class="no-data">无数据</span>
        </el-descriptions-item>
        <el-descriptions-item label="估值价格">
          <span v-if="itemDetail.referenceSellValuation">¥{{ itemDetail.referenceSellValuation }}</span>
          <span v-else class="no-data">无数据</span>
        </el-descriptions-item>
        
        <el-descriptions-item label="价格差值">
          <span v-if="itemDetail.priceDifference !== null" 
                :class="getPriceDiffClass(itemDetail.priceDifference)">
            {{ formatPriceDiff(itemDetail.priceDifference) }}
          </span>
          <span v-else class="no-data">-</span>
        </el-descriptions-item>
        <el-descriptions-item label="变化百分比">
          <span v-if="itemDetail.priceChangePercent !== null" 
                :style="{ color: itemDetail.priceStatusColor }">
            {{ formatPercentage(itemDetail.priceChangePercent) }}%
          </span>
          <span v-else class="no-data">-</span>
        </el-descriptions-item>
        <el-descriptions-item label="涨跌状态">
          <el-tag :type="getStatusTagType(itemDetail.priceStatus)" size="small">
            {{ itemDetail.priceStatusText }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="7天价格变化">
          <span v-if="itemDetail.priceAlterPercentage7d">{{ itemDetail.priceAlterPercentage7d }}%</span>
          <span v-else class="no-data">无数据</span>
        </el-descriptions-item>
        <el-descriptions-item label="库存数量">{{ itemDetail.stock || 0 }}</el-descriptions-item>
        <el-descriptions-item label="销售状态">
          <el-tag :type="itemDetail.sale === 1 ? 'success' : 'danger'" size="small">
            {{ itemDetail.sale === 1 ? '在售' : '停售' }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="创建时间">{{ parseTime(itemDetail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(itemDetail.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="价格更新时间">{{ parseTime(itemDetail.lastPriceUpdate) || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 商品编辑对话框 -->
    <el-dialog :title="editTitle" v-model="editOpen" width="800px" append-to-body>
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="120px">
        <!-- 隐藏的时间戳字段 -->
        <el-form-item v-show="false" prop="updateTimeStamp">
          <el-input v-model.number="editForm.updateTimeStamp" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物品名称：" prop="name">
              <el-input
                v-model="editForm.name"
                placeholder="请输入物品名称"
                style="width: 280px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="英文名称：" prop="hashname">
              <el-input
                v-model="editForm.hashname"
                placeholder="请输入物品英文名"
                style="width: 280px"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物品展示价：" prop="priceShow">
              <el-input v-model="editForm.priceShow" placeholder="请输入物品展示价" style="width: 280px"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物品成本价：" prop="priceCost">
              <el-input v-model="editForm.priceCost" placeholder="请输入物品成本价" style="width: 280px"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物品收购价：" prop="priceBuy">
              <el-input v-model="editForm.priceBuy" placeholder="请输入物品收购价" style="width: 280px"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物品回收价：" prop="priceRecycle">
              <el-input v-model="editForm.priceRecycle" placeholder="请输入物品回收价" style="width: 280px"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物品标签：" prop="tag">
              <el-input
                v-model="editForm.tag"
                placeholder="请输入物品标签"
                style="width: 280px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物品图片：" prop="image">
              <el-input
                v-model="editForm.image"
                placeholder="请输入物品图片URL"
                style="width: 280px"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物品库存：" prop="stock">
              <el-input
                v-model="editForm.stock"
                placeholder="请输入物品库存"
                style="width: 280px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否上架：" prop="sale" required>
              <el-radio-group v-model="editForm.sale">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer" style="text-align: center">
          <el-button type="primary" @click="submitEditForm" style="width: 100px">确 定</el-button>
          <el-button @click="cancelEdit" style="width: 100px; margin-left: 30px">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PriceAlertItems">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import { parseTime } from '@/utils/ruoyi'
import { debounce, clearAllDebounceTimers, requestDeduplicator } from '@/utils/debounce'
import {
  getItemPriceAnalysisList,
  getItemPriceAnalysisInfo,
  batchUpdateItemPriceAnalysis,
  updateSingleItemPrice
} from '@/api/priceAlert/simple'
import { getCommoditys, updateCommoditys } from '@/api/commoditySys/commoditys'

// 响应式数据
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const multiple = ref(true)
const total = ref(0)
const itemList = ref([])
const detailOpen = ref(false)
const editOpen = ref(false)
const itemDetail = ref({})
const editTitle = ref('')
const editFormRef = ref()

// 编辑表单数据
const editForm = reactive({
  id: null,
  name: '',
  hashname: '',
  priceShow: '',
  priceCost: '',
  priceBuy: '',
  priceRecycle: '',
  tag: '',
  image: '',
  stock: '',
  sale: 1,
  updateTimeStamp: null
})

const queryRef = ref()

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: null,
  tag: null,
  sale: null,
  params: {
    minPrice: null,
    maxPrice: null,
    priceStatus: null
  }
})

// 编辑表单验证规则
const editRules = {
  name: [
    { required: true, message: "物品名称不能为空", trigger: "blur" }
  ],
  hashname: [
    { required: true, message: "物品英文名不能为空", trigger: "blur" }
  ],
  priceShow: [
    { required: true, message: "物品展示价不能为空", trigger: "blur" }
  ],
  priceCost: [
    { required: true, message: "物品成本价不能为空", trigger: "blur" }
  ],
  priceBuy: [
    { required: true, message: "物品收购价不能为空", trigger: "blur" }
  ],
  priceRecycle: [
    { required: true, message: "物品回收价不能为空", trigger: "blur" }
  ],
  stock: [
    { required: true, message: "物品库存不能为空", trigger: "blur" }
  ],
  sale: [
    { required: true, message: "请选择是否上架", trigger: "change" }
  ]
}

// 方法定义
const getList = async () => {
  // 🔧 防止重复请求
  const requestKey = 'getItemPriceAnalysisList'
  if (requestDeduplicator.isDuplicate(requestKey, queryParams)) {
    console.log('检测到重复请求，跳过执行')
    return
  }

  loading.value = true
  try {
    const requestPromise = getItemPriceAnalysisList(queryParams)
    requestDeduplicator.addRequest(requestKey, queryParams, requestPromise)

    const response = await requestPromise

    // 🔧 调试：检查响应数据结构
    console.log('价格分析API响应:', response)
    console.log('响应数据行数:', response.rows?.length || 0)
    console.log('响应总数:', response.total)

    // 处理数据，确保所有必要字段都存在
    const processedRows = (response.rows || []).map(item => {
      // 确保价格字段是数字类型
      const priceShow = item.priceShow !== null && item.priceShow !== undefined ? 
        Number(item.priceShow) : null;
      const referenceSellMinPrice = item.referenceSellMinPrice !== null && 
        item.referenceSellMinPrice !== undefined ? Number(item.referenceSellMinPrice) : null;
      
      // 计算价格差值（如果后端没有提供）
      let priceDifference = item.priceDifference;
      if ((priceDifference === null || priceDifference === undefined) && 
          referenceSellMinPrice !== null && priceShow !== null) {
        priceDifference = referenceSellMinPrice - priceShow;
      }
      
      // 计算价格变化百分比（如果后端没有提供）
      let priceChangePercent = item.priceChangePercent;
      if ((priceChangePercent === null || priceChangePercent === undefined) && 
          referenceSellMinPrice !== null && priceShow !== null && referenceSellMinPrice !== 0) {
        priceChangePercent = ((priceShow - referenceSellMinPrice) / referenceSellMinPrice) * 100;
      }
      
      // 确定价格状态（如果后端没有提供）
      let priceStatus = item.priceStatus;
      if (!priceStatus && priceChangePercent !== null && priceChangePercent !== undefined) {
        if (priceChangePercent > 0) priceStatus = 'up';
        else if (priceChangePercent < 0) priceStatus = 'down';
        else priceStatus = 'stable';
      } else if (!priceStatus) {
        priceStatus = 'no_data';
      }
      
      // 设置价格状态文本（如果后端没有提供）
      let priceStatusText = item.priceStatusText;
      if (!priceStatusText) {
        switch (priceStatus) {
          case 'up': priceStatusText = '上涨'; break;
          case 'down': priceStatusText = '下跌'; break;
          case 'stable': priceStatusText = '持平'; break;
          default: priceStatusText = '无数据'; break;
        }
      }
      
      // 设置价格状态颜色（如果后端没有提供）
      let priceStatusColor = item.priceStatusColor;
      if (!priceStatusColor) {
        switch (priceStatus) {
          case 'up': priceStatusColor = '#f56c6c'; break; // 红色
          case 'down': priceStatusColor = '#67c23a'; break; // 绿色
          case 'stable': priceStatusColor = '#909399'; break; // 灰色
          default: priceStatusColor = '#e6a23c'; break; // 黄色
        }
      }
      
      return {
        ...item,
        priceShow,
        referenceSellMinPrice,
        priceDifference,
        priceChangePercent,
        priceStatus,
        priceStatusText,
        priceStatusColor
      };
    });

    itemList.value = processedRows;
    total.value = response.total || 0;

    // 🔧 验证数据完整性
    if (itemList.value.length > 0) {
      const sampleItem = itemList.value[0]
      console.log('处理后的示例商品数据:', sampleItem)

      // 检查关键字段
      const requiredFields = ['id', 'name', 'priceShow', 'referenceSellMinPrice', 
        'priceDifference', 'priceChangePercent', 'priceStatus', 'priceStatusText']
      const missingFields = requiredFields.filter(field =>
        sampleItem[field] === undefined || sampleItem[field] === null
      )

      if (missingFields.length > 0) {
        console.warn('⚠️ 商品数据缺少字段:', missingFields)
      }
    }

  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败: ' + error.message)
    itemList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 🔧 使用防抖处理查询请求
const handleQuery = debounce(() => {
  queryParams.pageNum = 1
  getList()
}, 300, 'handleQuery')

const resetQuery = () => {
  queryRef.value.resetFields()
  queryParams.params.minPrice = null
  queryParams.params.maxPrice = null
  queryParams.params.priceStatus = null
  handleQuery()
}

const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id)
  multiple.value = !selection.length
}

const handleView = async (row) => {
  try {
    const response = await getItemPriceAnalysisInfo(row.id)
    itemDetail.value = response.data
    detailOpen.value = true
  } catch (error) {
    ElMessage.error('获取商品详情失败: ' + error.message)
  }
}

const handleEdit = async (row) => {
  try {
    // 重置表单
    resetEditForm()

    // 获取商品详细信息
    const response = await getCommoditys(row.id)

    // 填充表单数据
    Object.assign(editForm, {
      ...response.data,
      updateTimeStamp: Date.now()
    })

    editTitle.value = "修改商品信息"
    editOpen.value = true
  } catch (error) {
    ElMessage.error('获取商品信息失败: ' + error.message)
  }
}

const resetEditForm = () => {
  Object.assign(editForm, {
    id: null,
    name: '',
    hashname: '',
    priceShow: '',
    priceCost: '',
    priceBuy: '',
    priceRecycle: '',
    tag: '',
    image: '',
    stock: '',
    sale: 1,
    updateTimeStamp: null
  })
}

const cancelEdit = () => {
  editOpen.value = false
  resetEditForm()
}

const submitEditForm = async () => {
  try {
    await editFormRef.value.validate()

    const response = await updateCommoditys(editForm)
    if (response.code === 200) {
      ElMessage.success('商品信息修改成功')
      editOpen.value = false
      resetEditForm()
      // 刷新列表
      getList()
    } else {
      ElMessage.error('商品信息修改失败: ' + response.msg)
    }
  } catch (error) {
    ElMessage.error('商品信息修改失败: ' + error.message)
  }
}

const handleRefreshPrice = async (row) => {
  try {
    const response = await updateSingleItemPrice(row.id)
    if (response.code === 200) {
      const result = response.data
      const priceText = isValidNumber(result.referencePrice)
        ? safeFormatPrice(result.referencePrice)
        : '无数据'
      ElMessage.success(`商品 ${result.itemName} 价格更新成功！参考价格: ${priceText}`)
      getList() // 刷新列表
    } else {
      ElMessage.error('价格刷新失败: ' + response.msg)
    }
  } catch (error) {
    ElMessage.error('价格刷新失败: ' + error.message)
  }
}

const handleBatchUpdate = async () => {
  if (ids.value.length === 0) {
    ElMessage.warning('请选择要更新的商品')
    return
  }
  
  try {
    await ElMessageBox.confirm('确认批量更新选中商品的价格信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await batchUpdateItemPriceAnalysis(ids.value)

    // 🔧 修复消息提示：正确处理后端返回的数据格式
    if (response.code === 200 && response.data) {
      const { successCount = 0, failCount = 0, totalItems = 0 } = response.data
      ElMessage.success(`批量更新完成，总数: ${totalItems}, 成功: ${successCount}, 失败: ${failCount}`)
    } else {
      ElMessage.error('批量更新失败: ' + (response.msg || '未知错误'))
    }

    getList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量更新失败: ' + error.message)
    }
  }
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 工具方法
const getPriceDiffClass = (diff) => {
  // 🔧 修复：添加空值检查
  if (!isValidNumber(diff)) return 'price-stable'

  const numDiff = Number(diff)
  if (numDiff > 0) return 'price-up'
  if (numDiff < 0) return 'price-down'
  return 'price-stable'
}

const formatPriceDiff = (diff) => {
  // 🔧 修复：添加空值和类型检查
  if (diff === null || diff === undefined || isNaN(diff)) {
    return '--'
  }

  const numDiff = Number(diff)
  if (numDiff > 0) return `+¥${numDiff.toFixed(2)}`
  if (numDiff < 0) return `¥${numDiff.toFixed(2)}`
  return '¥0.00'
}

const formatPercentage = (percent) => {
  // 🔧 修复：添加空值和类型检查
  if (percent === null || percent === undefined || isNaN(percent)) {
    return '--'
  }

  const numPercent = Number(percent)
  if (numPercent > 0) return `+${numPercent.toFixed(2)}`
  return numPercent.toFixed(2)
}

const getPriceChangeIcon = (status) => {
  switch (status) {
    case 'up': return ArrowUp
    case 'down': return ArrowDown
    default: return Minus
  }
}

const getPriceChangeIconClass = (status) => {
  switch (status) {
    case 'up': return 'price-up-icon'
    case 'down': return 'price-down-icon'
    default: return 'price-stable-icon'
  }
}

const getStatusTagType = (status) => {
  switch (status) {
    case 'up': return 'danger'
    case 'down': return 'success'
    case 'stable': return 'info'
    default: return 'warning'
  }
}

// 生命周期
onMounted(() => {
  // 🔧 延迟执行，避免页面刷新时的竞态条件
  setTimeout(() => {
    getList()
  }, 100)
})

// 🔧 组件卸载时清理防抖定时器
onUnmounted(() => {
  clearAllDebounceTimers()
})

const safeFormatPrice = (price, defaultValue = '--') => {
  if (price === null || price === undefined || price === '' || isNaN(price)) {
    return defaultValue
  }

  const numPrice = Number(price)
  if (isNaN(numPrice)) {
    return defaultValue
  }

  return `¥${numPrice.toFixed(2)}`
}

// 🔧 安全的数值检查函数
const isValidNumber = (value) => {
  return value !== null && value !== undefined && value !== '' && !isNaN(value)
}
</script>

<style scoped>
.item-info {
  text-align: left;
}

.item-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.item-tag {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

.price-text {
  font-weight: 600;
  color: #409eff;
}

.price-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.price-up {
  color: #f56c6c;
  font-weight: 600;
}

.price-down {
  color: #67c23a;
  font-weight: 600;
}

.price-stable {
  color: #909399;
  font-weight: 600;
}

.price-up-icon {
  color: #f56c6c;
}

.price-down-icon {
  color: #67c23a;
}

.price-stable-icon {
  color: #909399;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
}

.no-image {
  color: #c0c4cc;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}
</style>
