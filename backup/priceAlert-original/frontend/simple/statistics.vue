<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalAlerts || 0 }}</div>
              <div class="stat-label">总预警数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon today">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.todayAlerts || 0 }}</div>
              <div class="stat-label">今日预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon pending">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingAlerts || 0 }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon processed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.processedAlerts || 0 }}</div>
              <div class="stat-label">已处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <!-- 预警趋势图 -->
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>预警趋势分析</span>
              <el-select v-model="trendDays" @change="loadTrendData" style="width: 120px">
                <el-option label="7天" :value="7" />
                <el-option label="15天" :value="15" />
                <el-option label="30天" :value="30" />
              </el-select>
            </div>
          </template>
          <div ref="trendChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>

      <!-- 规则类型分布 -->
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>规则类型分布</span>
          </template>
          <div ref="ruleTypeChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 商品预警排行 -->
    <el-row :gutter="20" class="mt20">
      <el-col :span="24">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>商品预警排行榜</span>
              <el-select v-model="rankingLimit" @change="loadRankingData" style="width: 120px">
                <el-option label="前10名" :value="10" />
                <el-option label="前20名" :value="20" />
                <el-option label="前50名" :value="50" />
              </el-select>
            </div>
          </template>
          <div ref="rankingChartRef" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-row :gutter="20" class="mt20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>规则执行统计</span>
          </template>
          <el-table :data="ruleStats" style="width: 100%">
            <el-table-column prop="ruleType" label="规则类型" width="120">
              <template #default="scope">
                <el-tag :type="scope.row.ruleType === 'price_up' ? 'danger' : 'success'">
                  {{ scope.row.ruleType === 'price_up' ? '价格上涨' : '价格下跌' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="totalTriggers" label="总触发次数" align="center" />
            <el-table-column prop="lastExecution" label="最后执行" align="center">
              <template #default="scope">
                {{ parseTime(scope.row.lastExecution) }}
              </template>
            </el-table-column>
            <el-table-column prop="isEnabled" label="状态" align="center" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.isEnabled ? 'success' : 'danger'">
                  {{ scope.row.isEnabled ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>数据源统计</span>
          </template>
          <el-table :data="dataSourceStats" style="width: 100%">
            <el-table-column prop="dataSource" label="数据源" width="120" />
            <el-table-column prop="alertCount" label="预警数量" align="center" />
            <el-table-column prop="percentage" label="占比" align="center">
              <template #default="scope">
                {{ scope.row.percentage }}%
              </template>
            </el-table-column>
            <el-table-column prop="avgChangePercent" label="平均变化幅度" align="center">
              <template #default="scope">
                {{ scope.row.avgChangePercent }}%
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="PriceAlertStatistics">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { DataAnalysis, Calendar, Warning, CircleCheck } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { parseTime } from '@/utils/ruoyi'
import {
  getAlertStatistics,
  getAlertTrendData,
  getItemAlertRanking
} from '@/api/priceAlert/simple'

// 响应式数据
const trendChartRef = ref()
const ruleTypeChartRef = ref()
const rankingChartRef = ref()

const trendDays = ref(7)
const rankingLimit = ref(10)

const statistics = reactive({
  totalAlerts: 0,
  todayAlerts: 0,
  pendingAlerts: 0,
  processedAlerts: 0
})

const ruleStats = ref([])
const dataSourceStats = ref([])
const trendData = ref([])
const rankingData = ref([])

let trendChart = null
let ruleTypeChart = null
let rankingChart = null

// 方法定义
const loadStatistics = async () => {
  try {
    const response = await getAlertStatistics()
    if (response.code === 200 && response.data) {
      Object.assign(statistics, response.data)

      // 更新规则统计数据
      if (response.data.ruleStats) {
        ruleStats.value = response.data.ruleStats
      }

      // 更新数据源统计数据
      if (response.data.dataSourceStats) {
        dataSourceStats.value = response.data.dataSourceStats
      }

      // 更新规则类型分布图
      updateRuleTypeChart(response.data.ruleTypeDistribution || [])
    }
  } catch (error) {
    ElMessage.error('加载统计数据失败: ' + error.message)
  }
}

const loadTrendData = async () => {
  try {
    const response = await getAlertTrendData(trendDays.value)
    if (response.code === 200 && response.data) {
      trendData.value = response.data
      updateTrendChart()
    }
  } catch (error) {
    ElMessage.error('加载趋势数据失败: ' + error.message)
  }
}

const loadRankingData = async () => {
  try {
    const response = await getItemAlertRanking(rankingLimit.value)
    if (response.code === 200 && response.data) {
      rankingData.value = response.data
      updateRankingChart()
    }
  } catch (error) {
    ElMessage.error('加载排行数据失败: ' + error.message)
  }
}

const initTrendChart = () => {
  trendChart = echarts.init(trendChartRef.value)
  updateTrendChart()
}

const updateTrendChart = () => {
  if (!trendChart || !trendData.value.length) return

  const dates = trendData.value.map(item => item.date)
  const priceUpData = trendData.value.map(item => item.priceUpCount || 0)
  const priceDownData = trendData.value.map(item => item.priceDownCount || 0)

  const option = {
    title: {
      text: '预警趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['价格上涨预警', '价格下跌预警'],
      bottom: 10
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '价格上涨预警',
        type: 'line',
        data: priceUpData,
        itemStyle: { color: '#f56c6c' },
        smooth: true
      },
      {
        name: '价格下跌预警',
        type: 'line',
        data: priceDownData,
        itemStyle: { color: '#67c23a' },
        smooth: true
      }
    ]
  }

  trendChart.setOption(option)
}

const initRuleTypeChart = () => {
  ruleTypeChart = echarts.init(ruleTypeChartRef.value)
}

const updateRuleTypeChart = (data) => {
  if (!ruleTypeChart || !data.length) return

  const option = {
    title: {
      text: '规则类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '预警类型',
        type: 'pie',
        radius: '60%',
        data: data.map(item => ({
          name: item.ruleType === 'price_up' ? '价格上涨' : '价格下跌',
          value: item.count
        })),
        itemStyle: {
          color: function(params) {
            return params.name === '价格上涨' ? '#f56c6c' : '#67c23a'
          }
        }
      }
    ]
  }

  ruleTypeChart.setOption(option)
}

const initRankingChart = () => {
  rankingChart = echarts.init(rankingChartRef.value)
  updateRankingChart()
}

const updateRankingChart = () => {
  if (!rankingChart || !rankingData.value.length) return

  const itemNames = rankingData.value.map(item => item.itemName)
  const alertCounts = rankingData.value.map(item => item.alertCount)

  const option = {
    title: {
      text: '商品预警排行',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: itemNames,
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '预警次数',
        type: 'bar',
        data: alertCounts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#409eff' },
            { offset: 1, color: '#67c23a' }
          ])
        }
      }
    ]
  }

  rankingChart.setOption(option)
}

const handleResize = () => {
  if (trendChart) trendChart.resize()
  if (ruleTypeChart) ruleTypeChart.resize()
  if (rankingChart) rankingChart.resize()
}

// 生命周期
onMounted(async () => {
  await loadStatistics()
  await loadTrendData()
  await loadRankingData()

  nextTick(() => {
    initTrendChart()
    initRuleTypeChart()
    initRankingChart()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  })
})

// 组件卸载时清理
onUnmounted(() => {
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (ruleTypeChart) {
    ruleTypeChart.dispose()
    ruleTypeChart = null
  }
  if (rankingChart) {
    rankingChart.dispose()
    rankingChart = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.stat-card {
  height: 120px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.today {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-icon.processed {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}
</style>
