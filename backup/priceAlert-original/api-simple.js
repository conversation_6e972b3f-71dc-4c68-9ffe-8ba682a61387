import request from '@/utils/request'

// ========== 预警配置管理 ==========

// 获取所有预警配置
export function getAllConfigs() {
  return request({
    url: '/priceAlert/simple/configs',
    method: 'get'
  })
}

// 根据规则类型获取配置
export function getConfigByRuleType(ruleType) {
  return request({
    url: '/priceAlert/simple/config/' + ruleType,
    method: 'get'
  })
}

// 更新预警配置
export function updateConfig(data) {
  return request({
    url: '/priceAlert/simple/config',
    method: 'put',
    data: data
  })
}

// 批量更新预警配置
export function batchUpdateConfigs(data) {
  return request({
    url: '/priceAlert/simple/configs/batch',
    method: 'put',
    data: data
  })
}

// 启用/禁用预警规则
export function toggleRuleStatusApi(ruleType, enabled) {
  return request({
    url: '/priceAlert/simple/config/toggle/' + ruleType + '/' + enabled,
    method: 'put'
  })
}

// 重置配置为默认值
export function resetConfig(ruleType) {
  return request({
    url: '/priceAlert/simple/config/reset/' + ruleType,
    method: 'post'
  })
}

// ========== 预警执行 ==========

// 手动执行预警检查
export function executeManualCheck() {
  return request({
    url: '/priceAlert/simple/execute/manual',
    method: 'post'
  })
}

// 测试预警规则
export function testRule(ruleType) {
  return request({
    url: '/priceAlert/simple/test/' + ruleType,
    method: 'post'
  })
}

// ========== 预警记录管理 ==========

// 查询预警记录列表
export function getRecordList(query) {
  return request({
    url: '/priceAlert/simple/records',
    method: 'get',
    params: query
  })
}

// 查询预警记录详细
export function getRecordById(id) {
  return request({
    url: '/priceAlert/simple/record/' + id,
    method: 'get'
  })
}

// 处理预警记录
export function handleRecord(data) {
  return request({
    url: '/priceAlert/simple/record/handle/' + data.id,
    method: 'put',
    data: data
  })
}

// 批量处理预警记录
export function batchHandleRecords(data) {
  return request({
    url: '/priceAlert/simple/records/batch-handle',
    method: 'put',
    data: data
  })
}

// 删除预警记录
export function deleteRecords(ids) {
  return request({
    url: '/priceAlert/simple/records',
    method: 'delete',
    data: ids
  })
}

// ========== 执行日志管理 ==========

// 查询执行日志列表
export function getExecutionLogList(query) {
  return request({
    url: '/priceAlert/simple/execution-logs',
    method: 'get',
    params: query
  })
}

// 查询执行日志详细
export function getExecutionLogById(id) {
  return request({
    url: '/priceAlert/simple/execution-log/' + id,
    method: 'get'
  })
}

// 清理过期执行日志
export function cleanExpiredExecutionLogs(retentionDays) {
  return request({
    url: '/priceAlert/simple/execution-logs/clean/' + retentionDays,
    method: 'delete'
  })
}

// ========== 统计分析 ==========

// 获取预警统计信息
export function getAlertStatistics() {
  return request({
    url: '/priceAlert/simple/statistics/alert',
    method: 'get'
  })
}

// 获取预警趋势数据
export function getAlertTrendData(days) {
  return request({
    url: '/priceAlert/simple/statistics/trend/' + days,
    method: 'get'
  })
}

// 获取商品预警排行
export function getItemAlertRanking(limit) {
  return request({
    url: '/priceAlert/simple/statistics/item-ranking/' + limit,
    method: 'get'
  })
}

// ========== 系统管理 ==========

// 获取系统状态
export function getSystemStatus() {
  return request({
    url: '/priceAlert/simple/system/status',
    method: 'get'
  })
}

// 获取系统配置
export function getSystemConfig() {
  return request({
    url: '/priceAlert/simple/system/config',
    method: 'get'
  })
}

// 更新系统配置
export function updateSystemConfig(data) {
  return request({
    url: '/priceAlert/simple/system/config',
    method: 'put',
    data: data
  })
}

// 重置系统配置
export function resetSystemConfig() {
  return request({
    url: '/priceAlert/simple/system/config/reset',
    method: 'post'
  })
}

// ========== 商品价格分析 ==========

// 查询商品价格分析列表
export function getItemPriceAnalysisList(query) {
  return request({
    url: '/priceAlert/simple/items',
    method: 'get',
    params: query
  })
}

// 获取商品价格分析详细信息
export function getItemPriceAnalysisInfo(id) {
  return request({
    url: '/priceAlert/simple/items/' + id,
    method: 'get'
  })
}

// 批量更新商品价格分析
export function batchUpdateItemPriceAnalysis(ids) {
  return request({
    url: '/priceAlert/simple/items/batch-update',
    method: 'put',
    data: ids
  })
}

// 更新单个商品价格
export function updateSingleItemPrice(id) {
  return request({
    url: '/priceAlert/simple/items/' + id + '/update-price',
    method: 'put'
  })
}

// 测试价格查询功能
export function testPriceQuery(hashname) {
  return request({
    url: '/priceAlert/simple/test/price/' + hashname,
    method: 'get'
  })
}

// 获取数据源统计
export function getDataSourceStatistics() {
  return request({
    url: '/priceAlert/simple/statistics/data-source',
    method: 'get'
  })
}

// ========== 成本价异常商品管理 ==========

// 查询成本价异常商品列表
export function getCostPriceAnomalyList(query) {
  return request({
    url: '/priceAlert/simple/cost-price-anomaly',
    method: 'get',
    params: query
  })
}

// 统计成本价异常商品数量
export function getCostPriceAnomalyCount() {
  return request({
    url: '/priceAlert/simple/cost-price-anomaly/count',
    method: 'get'
  })
}

// 获取成本价异常商品统计信息
export function getCostPriceAnomalyStatistics() {
  return request({
    url: '/priceAlert/simple/cost-price-anomaly/statistics',
    method: 'get'
  })
}
